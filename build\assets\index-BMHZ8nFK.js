import{o as e}from"./index-hfMliPo3.js";import{a as r,S as s,B as n,t,b as l,c as i}from"./minecraft-server-types-BF5rsZMx.js";const m=()=>{const a={isAuthMode:1,isCrossVersion:1,version:"1.12.2",gamemodes:r.NoRule},o={gamemodes:r.Survival,isAuthMode:0,isCrossVersion:0,version:"1.21.5"};return e.jsxs("div",{className:"flex flex-col max:w-2xl h-screen space-y-1.5",children:[e.jsx(s,{className:"",serverName:"2B2T",addressUrl:"2b2t.org",maxPlayer:420,currentPlayer:264,bannerUrl:t,serverIconUrl:n,serverBadge:a,isRuning:!0}),e.jsx(s,{className:"",serverName:"MiaoTown VI",addressUrl:"mc.miaotown.cc",maxPlayer:50,currentPlayer:14,bannerUrl:i,serverIconUrl:l,serverBadge:o})]})},v=Object.freeze(Object.defineProperty({__proto__:null,default:m},Symbol.toStringTag,{value:"Module"}));export{v as _};
