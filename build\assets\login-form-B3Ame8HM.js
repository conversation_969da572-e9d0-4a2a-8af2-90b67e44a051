import{o as e}from"./index-hfMliPo3.js";import{u as b,a as g,F as N,b as o,c as n,d as l,e as c,f as i,o as k,s as m}from"./form-mGX1_jA3.js";import{I as x}from"./input-CuqQAyEP.js";import{C as w}from"./checkbox-C3f9JzdG.js";import{L as t}from"./label-DPhRWIda.js";import{a as v,b as F,c as y}from"./index-lZgBQfjq.js";import{B as _}from"./button-BDk51iWJ.js";const d=k({username:m().min(2,{message:"账户最小需要2个字符."}),password:m().min(6,{message:"密码需要大于6位数"}).max(20,{message:"密码需要小于20位数"})});function S(){const{sinIn:p}=v(),{refetch:u}=F(),f=y(),r=b({resolver:g(d),defaultValues:{username:"lingwcy",password:"qweq11111"}});async function h(s){const a=d.safeParse(s);if(!a.success)console.error(a.error);else try{await p(a.data),await u(),f("/"),window.location.reload()}catch(j){console.error("登录或获取用户信息失败:",j)}}return e.jsxs("div",{className:"w-72 h-auto border-0 flex justify-center flex-col mx-auto",children:[e.jsxs("div",{className:"flex flex-col items-center p-4 space-y-1",children:[e.jsx("p",{className:"font-extrabold tracking-tight text-2xl",children:"登录到您的账户"}),e.jsx("p",{className:"text-muted-foreground text-sm",children:"输入您的账号登录到您的账户"})]}),e.jsx(N,{...r,children:e.jsxs("form",{onSubmit:r.handleSubmit(h),className:"space-y-3",children:[e.jsx(o,{control:r.control,name:"username",render:({field:s})=>e.jsxs(n,{children:[e.jsx(l,{children:"账户"}),e.jsx(c,{children:e.jsx(x,{placeholder:"Ou_Takahiro",className:"placeholder:text-sm focus-visible:ring-pink-200",...s})}),e.jsx(i,{})]})}),e.jsx(o,{control:r.control,name:"password",render:({field:s})=>e.jsxs(n,{children:[e.jsx(l,{children:"密码"}),e.jsx(c,{children:e.jsx(x,{className:"placeholder:text-sm focus-visible:ring-pink-200",placeholder:"密码",...s})}),e.jsx(i,{})]})}),e.jsxs("div",{className:"flex flex-row justify-between pt-2 pb-2",children:[e.jsxs("div",{className:"flex space-x-1",children:[e.jsx(w,{className:"data-[state=checked]:bg-pink-400 data-[state=checked]:border-pink-400",id:"terms"}),e.jsx("span",{}),e.jsx(t,{className:"text-xs text-pink-600",htmlFor:"terms",children:"记住我"})]}),e.jsx(t,{className:"text-xs text-pink-600 cursor-pointer",children:"忘记密码?"})]}),e.jsx(_,{type:"submit",variant:"secondary",className:"w-full bg-pink-400",children:e.jsx(t,{className:"text-sm text-pink-100",children:"登录"})})]})})]})}const B=Object.freeze(Object.defineProperty({__proto__:null,default:S},Symbol.toStringTag,{value:"Module"}));export{S as L,B as _};
