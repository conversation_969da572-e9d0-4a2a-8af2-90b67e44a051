import{F as C,j as x,o as e}from"./index-hfMliPo3.js";import{B as b}from"./button-BDk51iWJ.js";import{D as k,a as _,b as L,c as M,d as R,e as E,f as P}from"./dialog-DC2GGX8r.js";import{S as O}from"./switch-Cf-lMhzr.js";import{I as d}from"./input-CuqQAyEP.js";import{u as U,a as B,F as G,b as h,c as p,d as j,e as u,f,o as z,s as o,h as A}from"./form-mGX1_jA3.js";import{C as T,a as q}from"./card-By0kWE3G.js";import{L as I}from"./label-DPhRWIda.js";import{X as F}from"./x-DAWoEBhS.js";import{t as J}from"./index-lZgBQfjq.js";/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const V=[["path",{d:"M12 13v8",key:"1l5pq0"}],["path",{d:"M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242",key:"1pljnt"}],["path",{d:"m8 17 4-4 4 4",key:"1quai1"}]],W=C("cloud-upload",V);function H(){const[i,n]=x.useState(null),[r,c]=x.useState(""),g=x.useCallback(s=>{s.preventDefault();const v=s.dataTransfer.files[0];m(v)},[]),l=s=>{s.target.files&&s.target.files[0]&&m(s.target.files[0])},m=s=>{if(!s.type.startsWith("image/")){c("仅支持图片文件（JPEG/PNG）");return}if(s.size>5*1024*1024){c("文件大小不能超过5MB");return}c(""),n(s)},t=()=>{n(null)};return e.jsx(T,{className:"w-full max-w-md",children:e.jsx(q,{children:e.jsxs("div",{children:[e.jsxs("div",{onDrop:g,onDragOver:s=>s.preventDefault(),className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-primary transition-colors",children:[e.jsx("label",{htmlFor:"file-upload",className:"cursor-pointer",children:e.jsxs("div",{className:"flex flex-col items-center justify-center gap-2 ",children:[e.jsx(W,{className:"h-8 w-8 text-gray-400"}),e.jsxs(I,{className:"cursor-pointer",children:[e.jsx("span",{className:"font-medium text-primary",children:"点击上传"})," 或拖拽文件到此处"]}),e.jsx("p",{className:"text-sm text-gray-500",children:"支持 JPEG, PNG (最大 5MB)"})]})}),e.jsx(d,{id:"file-upload",type:"file",onChange:l,className:"hidden",accept:"image/jpeg,image/png"})]}),r&&e.jsxs("p",{className:"text-sm text-destructive flex items-center gap-1",children:[e.jsx(F,{className:"h-4 w-4"})," ",r]}),i&&e.jsxs("div",{className:"mt-4 space-y-2",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("p",{className:"text-sm font-medium",children:["已选择: ",e.jsx("span",{className:"text-primary",children:i.name})]}),e.jsx(b,{variant:"ghost",size:"sm",onClick:t,className:"text-destructive hover:text-destructive",children:e.jsx(F,{className:"h-4 w-4"})})]}),i.type.startsWith("image/")&&e.jsx("img",{src:URL.createObjectURL(i),alt:"预览",className:"mt-2 rounded-md border border-gray-200 max-h-40 object-contain"})]})]})})})}const w=z({name:o().min(2,{message:"用户名最短需要2个字符"}),email:A({message:"请输入有效的邮箱地址"}),roleId:o(),avatar:o(),password:o().min(6,{message:"密码最短需要6个字符"}).max(20,{message:"密码最长不能超过20个字符"})});function X({open:i,onClose:n,updateUserItem:r,handleCreate:c,handleEdit:g}){const l=!!r,m=()=>l?w.extend({password:o().optional().or(o().min(6,{message:"密码最短需要6个字符"}).max(20,{message:"密码最长不能超过20个字符"}))}):w,t=U({resolver:B(m()),defaultValues:{name:"",email:"",avatar:"",password:"",roleId:""}});x.useEffect(()=>{r?t.reset({name:r.name||"",email:r.email||"",avatar:r.avatar||"",password:"",roleId:r.roleId||""}):t.reset({name:"",email:"",avatar:"",password:"",roleId:""})},[r,t]);const[s,v]=x.useState(!1),S=a=>{const D=s?o().min(1,{message:"请上传头像文件"}):o().url({message:"请输入有效的URL链接"}),N=m().extend({avatar:D}).safeParse(a);if(!N.success)J.error("表单提交失败，请检查字段！"),console.log("验证错误:",N.error.issues);else{if(l){const y={...a,id:r?.id};(!a.password||a.password.trim()==="")&&delete y.password,g(y)}else c(a);n?.()}};return e.jsx(k,{open:i,onOpenChange:n,children:e.jsxs(_,{className:"sm:max-w-[425px]",children:[e.jsxs(L,{children:[e.jsx(M,{children:l?"编辑用户":"新增用户"}),e.jsx(R,{children:l?"修改用户信息，点击保存完成更新。":"填写用户信息，点击保存完成创建。"})]}),e.jsx(G,{...t,children:e.jsxs("form",{id:"create-user-form",onSubmit:t.handleSubmit(S),className:"space-y-3.5",children:[e.jsx(h,{control:t.control,name:"name",render:({field:a})=>e.jsxs(p,{children:[e.jsx(j,{children:"用户名"}),e.jsx(u,{children:e.jsx(d,{placeholder:"请输入用户名",className:"placeholder:text-sm focus-visible:ring-pink-200",...a})}),e.jsx(f,{})]})}),e.jsx(h,{control:t.control,name:"email",render:({field:a})=>e.jsxs(p,{children:[e.jsx(j,{children:"邮箱"}),e.jsx(u,{children:e.jsx(d,{placeholder:"请输入邮箱",className:"placeholder:text-sm focus-visible:ring-pink-200",...a})}),e.jsx(f,{})]})}),e.jsx(h,{control:t.control,name:"avatar",render:({field:a})=>e.jsxs(p,{children:[e.jsxs(j,{className:"flex justify-between",children:[e.jsx("span",{children:s?"头像(文件上传)":"头像(URL上传)"})," ",e.jsx(O,{checked:s,onCheckedChange:v})]}),e.jsx(u,{children:s?e.jsx(H,{}):e.jsx(d,{placeholder:"请输入头像URL",className:"placeholder:text-sm focus-visible:ring-pink-200",...a})}),e.jsx(f,{})]})}),e.jsx(h,{control:t.control,name:"password",render:({field:a})=>e.jsxs(p,{children:[e.jsx(j,{children:l?"密码（留空则不修改）":"密码"}),e.jsx(u,{children:e.jsx(d,{type:"password",placeholder:l?"留空则不修改密码":"请输入密码",className:"placeholder:text-sm focus-visible:ring-pink-200",...a})}),e.jsx(f,{})]})})]})}),e.jsxs(E,{children:[e.jsx(P,{asChild:!0,children:e.jsx(b,{variant:"outline",onClick:n,children:"取消"})}),e.jsx(b,{type:"submit",form:"create-user-form",children:l?"更新":"保存"})]})]})})}const ie=Object.freeze(Object.defineProperty({__proto__:null,default:X},Symbol.toStringTag,{value:"Module"}));export{X as U,ie as _};
