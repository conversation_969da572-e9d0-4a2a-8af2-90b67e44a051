import{j as c,u as v,o as r}from"./index-hfMliPo3.js";import{B as D}from"./button-BDk51iWJ.js";import{S as w}from"./scroll-area-DwuRsZTj.js";import{R as q,T as $,C as T}from"./index-BFIhsz_D.js";import{u as E}from"./index-B6uarZlB.js";import{r as u,d as m,e as f,f as j,t as h,P as k}from"./index-lZgBQfjq.js";import{C as A}from"./checkbox-C3f9JzdG.js";import{C as O}from"./chevron-right-FjfZs_XJ.js";function R(e,s,n){const[o,a]=E({prop:e,defaultProp:n??[],onChange:s}),t=c.useCallback(d=>o.includes(d),[o]),l=c.useCallback(d=>{const g=[d.id,...L(d.children??[])],p=g.every(t);a(p?o.filter(x=>!g.includes(x)):[...new Set([...o,...g])])},[o,t,a]);return{isChecked:t,toggleNode:l}}const L=e=>e.flatMap(s=>[s.id,...L(s.children??[])]),K=async()=>{try{return await u.get({url:`${m.permission.getPermissionList}`})}catch(e){throw console.error("Product API error:",e),e}},_=async e=>{try{return await u.post({url:`${m.permission.getPermissionById}`,data:e})}catch(s){throw console.error("Permission API error:",s),s}},F=async e=>{try{return await u.put({url:`${m.permission.getPermissionById}/${e.id}`,data:e})}catch(s){throw console.error("Permission API error:",s),s}},Q=async e=>{try{return await u.delete({url:`${m.permission.getPermissionById}/${e}`})}catch(s){throw console.error("Permission API error:",s),s}},B=async()=>{try{return await u.get({url:`${m.permission.getRootPermissionList}`})}catch(e){throw console.error("Permission API error:",e),e}},M=async()=>{try{return await u.get({url:`${m.permission.getNextRootId}`})}catch(e){throw console.error("Permission API error:",e),e}},U=async e=>{try{return await u.get({url:`${m.permission.getNextChildId}/${e}`})}catch(s){throw console.error("Permission API error:",s),s}},S=()=>{const e=v(),s=f({queryKey:["permissionList"],queryFn:async()=>{try{const t=await K();return t&&t.code===200&&t.data,t.data}catch(t){console.error("API Error:",t)}},retry:3,staleTime:10*1e3,placeholderData:t=>t}),n=j({mutationFn:_,onSuccess:()=>{e.invalidateQueries({queryKey:["permissionList"]}),e.invalidateQueries({queryKey:["user-profile"]}),h("创建权限节点成功",{description:`您在 ${new Date().toUTCString()} 创建了一个新权限节点`,action:{label:"确认",onClick:()=>console.log("新权限节点已经确认")}})},onError:t=>{h(`创建权限节点失败 ${t.message}`)}}),o=j({mutationFn:F,onSuccess:()=>{e.invalidateQueries({queryKey:["permissionList"]}),e.invalidateQueries({queryKey:["user-profile"]}),h("更新权限节点成功",{description:`您在 ${new Date().toUTCString()} 更新了一个权限节点`,action:{label:"确认",onClick:()=>console.log("更新权限节点已经确认")}})},onError:t=>{h(`更新权限节点失败 ${t.message}`)}}),a=j({mutationFn:Q,onSuccess:()=>{e.invalidateQueries({queryKey:["permissionList"]}),e.invalidateQueries({queryKey:["user-profile"]}),h("删除权限节点成功",{description:`您在 ${new Date().toUTCString()} 删除了一个权限节点`,action:{label:"确认",onClick:()=>console.log("删除权限节点已经确认")}})},onError:t=>{h(`删除权限节点失败 ${t.message}`)}});return{data:s.data,isLoading:s.isLoading,createPermission:n,updatePermission:o,deletePermission:a}},ce=()=>f({queryKey:["rootPermissionList"],queryFn:B,staleTime:10*1e3,retry:2,meta:{silent:!0}}),de=()=>{const e=f({queryKey:["nextRootId"],queryFn:M,staleTime:1e4,retry:2,meta:{silent:!0}});return{data:e.data,isLoading:e.isLoading}},z=()=>{const[e,s]=c.useState(""),n=f({queryKey:["nextChildId",e],queryFn:()=>U(e),staleTime:10*1e3,enabled:!!e,retry:2,meta:{silent:!0}});return{setParentId:s,data:n.data,isLoading:n.isLoading}},ue=()=>{const{updatePermission:e,deletePermission:s,createPermission:n}=S(),{setParentId:o,data:a,isLoading:t}=z(),[l,d]=c.useState(!1),[g,p]=c.useState(!1),[x,y]=c.useState(void 0),[b,P]=c.useState(),[C,I]=c.useState(null);return c.useEffect(()=>{if(!t&&a&&C){const i={id:a.data.id,type:1,parentId:C,status:0,label:"",name:"",route:"",icon:"",order:0};y(i),P("push"),p(!0),I(null)}},[t,a,C]),{handleCreatePermission:i=>{n.mutate(i)},handleDeletePermission:i=>{s.mutate(i)},handleEditPermission:i=>{e.mutate(i)},handleOpenEditDialog:i=>{y(i),P("update"),p(!0)},handleCloseDialog:()=>{d(!1),p(!1),y(void 0),I(null)},handleOpenCreateDialog:()=>{y(null),d(!0),P("create")},handleOpenPushDialog:i=>{if(console.log(i),!i){P("create"),p(!0);return}o(i),I(i)},isCreateDialogOpen:l,isEditDialogOpen:g,editingItem:x,dialogType:b,nextChildLoading:t}};function W({data:e,value:s,onChange:n,defaultValue:o}){const{isChecked:a,toggleNode:t}=R(s,n,o);return!e||!Array.isArray(e)?r.jsx(w,{className:"w-full rounded-md border",children:r.jsx("div",{className:"p-4 text-center text-muted-foreground",children:e===void 0?"加载中...":"暂无权限数据"})}):r.jsx(w,{className:"h-88 rounded-md",children:r.jsx("ul",{className:"p-2 cursor-pointer",children:e.map(l=>r.jsx(N,{node:l,depth:0,isChecked:a,toggleNode:t},l.id))})})}function N({node:e,depth:s,isChecked:n,toggleNode:o}){const a=e.children&&e.children.length>0,t=n(e.id);return r.jsxs(q,{children:[r.jsxs("li",{style:{paddingLeft:`${s+.5*1}rem`},className:"flex justify-between items-center gap-2 rounded hover:bg-accent px-0 py-1",children:[r.jsx(A,{checked:t,onCheckedChange:()=>o(e),disabled:e.status===k.DISABLE}),r.jsx("span",{className:"flex-1 text-sm",children:e.name}),a&&r.jsx($,{asChild:!0,children:r.jsx(D,{variant:"ghost",size:"icon",className:"cursor-pointer",children:r.jsx(O,{className:"h-4 w-4 transition-transform ui-state-open:rotate-90"})})})]}),a&&r.jsx(T,{children:r.jsx("ul",{className:"pl-4",children:e.children.map(l=>r.jsx(N,{node:l,depth:s+.3,isChecked:n,toggleNode:o},l.id))})})]})}function G({checked:e,setChecked:s}){const{data:n,isLoading:o}=S();return r.jsx("div",{children:o?r.jsx("div",{className:"h-80 w-full rounded-md border flex items-center justify-center",children:r.jsx("div",{className:"text-muted-foreground",children:"加载权限数据中..."})}):r.jsx(W,{data:n,value:e,onChange:s})})}const me=Object.freeze(Object.defineProperty({__proto__:null,default:G},Symbol.toStringTag,{value:"Module"}));export{G as P,me as _,z as a,ce as b,S as c,ue as d,de as u};
