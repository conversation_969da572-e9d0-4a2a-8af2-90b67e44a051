import{j as u,o as l,v as b}from"./index-hfMliPo3.js";import{d as w,S as y,c as S,e as C}from"./index-BcAf74l_.js";function E(e,t){const n=u.createContext(t),c=o=>{const{children:r,...s}=o,a=u.useMemo(()=>s,Object.values(s));return l.jsx(n.Provider,{value:a,children:r})};c.displayName=e+"Provider";function i(o){const r=u.useContext(n);if(r)return r;if(t!==void 0)return t;throw new Error(`\`${o}\` must be used within \`${e}\``)}return[c,i]}function z(e,t=[]){let n=[];function c(o,r){const s=u.createContext(r),a=n.length;n=[...n,r];const p=d=>{const{scope:x,children:f,...v}=d,h=x?.[e]?.[a]||s,m=u.useMemo(()=>v,Object.values(v));return l.jsx(h.Provider,{value:m,children:f})};p.displayName=o+"Provider";function g(d,x){const f=x?.[e]?.[a]||s,v=u.useContext(f);if(v)return v;if(r!==void 0)return r;throw new Error(`\`${d}\` must be used within \`${o}\``)}return[p,g]}const i=()=>{const o=n.map(r=>u.createContext(r));return function(s){const a=s?.[e]||o;return u.useMemo(()=>({[`__scope${e}`]:{...s,[e]:a}}),[s,a])}};return i.scopeName=e,[c,P(i,...t)]}function P(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const c=e.map(i=>({useScope:i(),scopeName:i.scopeName}));return function(o){const r=c.reduce((s,{useScope:a,scopeName:p})=>{const d=a(o)[`__scope${p}`];return{...s,...d}},{});return u.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}var k=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],N=k.reduce((e,t)=>{const n=w(`Primitive.${t}`),c=u.forwardRef((i,o)=>{const{asChild:r,...s}=i,a=r?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),l.jsx(a,{...s,ref:o})});return c.displayName=`Primitive.${t}`,{...e,[t]:c}},{});function M(e,t){e&&b.flushSync(()=>e.dispatchEvent(t))}const j=C("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function B({className:e,variant:t,size:n,asChild:c=!1,...i}){const o=c?y:"button";return l.jsx(o,{"data-slot":"button",className:S(j({variant:t,size:n,className:e})),...i})}export{B,N as P,E as a,j as b,z as c,M as d};
