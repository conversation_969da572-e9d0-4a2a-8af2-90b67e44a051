import{F as D,o as t,j as i,H as ke}from"./index-hfMliPo3.js";import{N as Ae,b as Re}from"./index-lZgBQfjq.js";import{R as Me,a as Le,b as Pe}from"./index-BFIhsz_D.js";import{u as te,f as De,c as x,S as F,e as Ie}from"./index-BcAf74l_.js";import{P as A,c as ae,B as Oe}from"./button-BDk51iWJ.js";import{S as ze,e as Be,b as He,c as $e,d as Ve}from"./sheet-CJ0KXymD.js";import{c as y}from"./index-Dc_FVRD7.js";import{e as re,R as qe,A as Fe,f as Ge,g as Ue,C as Ke,D as ne,a as se,b as oe,h as We,c as _,i as ie,j as Ye,k as Xe}from"./dropdown-menu-DENvmJeK.js";import{u as Je}from"./index-CRUxeMtj.js";import{P as Qe}from"./index-De0UIWTt.js";import{P as le}from"./index-BdwSXrlW.js";import{u as Ze}from"./index-B6uarZlB.js";import{u as et,b as tt}from"./use-system-DVY9KgBi.js";import{u as at}from"./use-tabs-CKDZS1Lb.js";import{C as de}from"./chevron-right-FjfZs_XJ.js";import{u as rt}from"./index-CEfSuq4R.js";import{u as $}from"./index-COjQa4th.js";import{C as ce}from"./chevrons-up-down--op-ACbB.js";import{B as nt}from"./badge-check-BpWk_Bsf.js";import{P as st}from"./plus-C-pZoFOv.js";import{S as ot}from"./scroll-area-DwuRsZTj.js";/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const it=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],lt=D("bell",it);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dt=[["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}],["path",{d:"M12 16.5A4.5 4.5 0 1 1 7.5 12 4.5 4.5 0 1 1 12 7.5a4.5 4.5 0 1 1 4.5 4.5 4.5 4.5 0 1 1-4.5 4.5",key:"14wa3c"}],["path",{d:"M12 7.5V9",key:"1oy5b0"}],["path",{d:"M7.5 12H9",key:"eltsq1"}],["path",{d:"M16.5 12H15",key:"vk5kw4"}],["path",{d:"M12 16.5V15",key:"k7eayi"}],["path",{d:"m8 8 1.88 1.88",key:"nxy4qf"}],["path",{d:"M14.12 9.88 16 8",key:"1lst6k"}],["path",{d:"m8 16 1.88-1.88",key:"h2eex1"}],["path",{d:"M14.12 14.12 16 16",key:"uqkrx3"}]],ct=D("flower",dt);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ut=[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]],ft=D("log-out",ut);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pt=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],mt=D("panel-left",pt);function bt({...e}){return t.jsx(Me,{"data-slot":"collapsible",...e})}function xt({...e}){return t.jsx(Le,{"data-slot":"collapsible-trigger",...e})}function ht({...e}){return t.jsx(Pe,{"data-slot":"collapsible-content",...e})}const z=768;function gt(){const[e,a]=i.useState(void 0);return i.useEffect(()=>{const r=window.matchMedia(`(max-width: ${z-1}px)`),s=()=>{a(window.innerWidth<z)};return r.addEventListener("change",s),a(window.innerWidth<z),()=>r.removeEventListener("change",s)},[]),!!e}var vt=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),wt="VisuallyHidden",ue=i.forwardRef((e,a)=>t.jsx(A.span,{...e,ref:a,style:{...vt,...e.style}}));ue.displayName=wt;var jt=ue,[I,Fa]=ae("Tooltip",[re]),O=re(),fe="TooltipProvider",yt=700,V="tooltip.open",[Ct,G]=I(fe),pe=e=>{const{__scopeTooltip:a,delayDuration:r=yt,skipDelayDuration:s=300,disableHoverableContent:n=!1,children:o}=e,d=i.useRef(!0),c=i.useRef(!1),l=i.useRef(0);return i.useEffect(()=>{const p=l.current;return()=>window.clearTimeout(p)},[]),t.jsx(Ct,{scope:a,isOpenDelayedRef:d,delayDuration:r,onOpen:i.useCallback(()=>{window.clearTimeout(l.current),d.current=!1},[]),onClose:i.useCallback(()=>{window.clearTimeout(l.current),l.current=window.setTimeout(()=>d.current=!0,s)},[s]),isPointerInTransitRef:c,onPointerInTransitChange:i.useCallback(p=>{c.current=p},[]),disableHoverableContent:n,children:o})};pe.displayName=fe;var k="Tooltip",[St,R]=I(k),me=e=>{const{__scopeTooltip:a,children:r,open:s,defaultOpen:n,onOpenChange:o,disableHoverableContent:d,delayDuration:c}=e,l=G(k,e.__scopeTooltip),p=O(a),[m,u]=i.useState(null),f=Je(),b=i.useRef(0),g=d??l.disableHoverableContent,h=c??l.delayDuration,j=i.useRef(!1),[v,w]=Ze({prop:s,defaultProp:n??!1,onChange:Y=>{Y?(l.onOpen(),document.dispatchEvent(new CustomEvent(V))):l.onClose(),o?.(Y)},caller:k}),T=i.useMemo(()=>v?j.current?"delayed-open":"instant-open":"closed",[v]),N=i.useCallback(()=>{window.clearTimeout(b.current),b.current=0,j.current=!1,w(!0)},[w]),E=i.useCallback(()=>{window.clearTimeout(b.current),b.current=0,w(!1)},[w]),W=i.useCallback(()=>{window.clearTimeout(b.current),b.current=window.setTimeout(()=>{j.current=!0,w(!0),b.current=0},h)},[h,w]);return i.useEffect(()=>()=>{b.current&&(window.clearTimeout(b.current),b.current=0)},[]),t.jsx(qe,{...p,children:t.jsx(St,{scope:a,contentId:f,open:v,stateAttribute:T,trigger:m,onTriggerChange:u,onTriggerEnter:i.useCallback(()=>{l.isOpenDelayedRef.current?W():N()},[l.isOpenDelayedRef,W,N]),onTriggerLeave:i.useCallback(()=>{g?E():(window.clearTimeout(b.current),b.current=0)},[E,g]),onOpen:N,onClose:E,disableHoverableContent:g,children:r})})};me.displayName=k;var q="TooltipTrigger",be=i.forwardRef((e,a)=>{const{__scopeTooltip:r,...s}=e,n=R(q,r),o=G(q,r),d=O(r),c=i.useRef(null),l=te(a,c,n.onTriggerChange),p=i.useRef(!1),m=i.useRef(!1),u=i.useCallback(()=>p.current=!1,[]);return i.useEffect(()=>()=>document.removeEventListener("pointerup",u),[u]),t.jsx(Fe,{asChild:!0,...d,children:t.jsx(A.button,{"aria-describedby":n.open?n.contentId:void 0,"data-state":n.stateAttribute,...s,ref:l,onPointerMove:y(e.onPointerMove,f=>{f.pointerType!=="touch"&&!m.current&&!o.isPointerInTransitRef.current&&(n.onTriggerEnter(),m.current=!0)}),onPointerLeave:y(e.onPointerLeave,()=>{n.onTriggerLeave(),m.current=!1}),onPointerDown:y(e.onPointerDown,()=>{n.open&&n.onClose(),p.current=!0,document.addEventListener("pointerup",u,{once:!0})}),onFocus:y(e.onFocus,()=>{p.current||n.onOpen()}),onBlur:y(e.onBlur,n.onClose),onClick:y(e.onClick,n.onClose)})})});be.displayName=q;var U="TooltipPortal",[Tt,Nt]=I(U,{forceMount:void 0}),xe=e=>{const{__scopeTooltip:a,forceMount:r,children:s,container:n}=e,o=R(U,a);return t.jsx(Tt,{scope:a,forceMount:r,children:t.jsx(le,{present:r||o.open,children:t.jsx(Qe,{asChild:!0,container:n,children:s})})})};xe.displayName=U;var C="TooltipContent",he=i.forwardRef((e,a)=>{const r=Nt(C,e.__scopeTooltip),{forceMount:s=r.forceMount,side:n="top",...o}=e,d=R(C,e.__scopeTooltip);return t.jsx(le,{present:s||d.open,children:d.disableHoverableContent?t.jsx(ge,{side:n,...o,ref:a}):t.jsx(Et,{side:n,...o,ref:a})})}),Et=i.forwardRef((e,a)=>{const r=R(C,e.__scopeTooltip),s=G(C,e.__scopeTooltip),n=i.useRef(null),o=te(a,n),[d,c]=i.useState(null),{trigger:l,onClose:p}=r,m=n.current,{onPointerInTransitChange:u}=s,f=i.useCallback(()=>{c(null),u(!1)},[u]),b=i.useCallback((g,h)=>{const j=g.currentTarget,v={x:g.clientX,y:g.clientY},w=Rt(v,j.getBoundingClientRect()),T=Mt(v,w),N=Lt(h.getBoundingClientRect()),E=Dt([...T,...N]);c(E),u(!0)},[u]);return i.useEffect(()=>()=>f(),[f]),i.useEffect(()=>{if(l&&m){const g=j=>b(j,m),h=j=>b(j,l);return l.addEventListener("pointerleave",g),m.addEventListener("pointerleave",h),()=>{l.removeEventListener("pointerleave",g),m.removeEventListener("pointerleave",h)}}},[l,m,b,f]),i.useEffect(()=>{if(d){const g=h=>{const j=h.target,v={x:h.clientX,y:h.clientY},w=l?.contains(j)||m?.contains(j),T=!Pt(v,d);w?f():T&&(f(),p())};return document.addEventListener("pointermove",g),()=>document.removeEventListener("pointermove",g)}},[l,m,d,p,f]),t.jsx(ge,{...e,ref:o})}),[_t,kt]=I(k,{isInside:!1}),At=De("TooltipContent"),ge=i.forwardRef((e,a)=>{const{__scopeTooltip:r,children:s,"aria-label":n,onEscapeKeyDown:o,onPointerDownOutside:d,...c}=e,l=R(C,r),p=O(r),{onClose:m}=l;return i.useEffect(()=>(document.addEventListener(V,m),()=>document.removeEventListener(V,m)),[m]),i.useEffect(()=>{if(l.trigger){const u=f=>{f.target?.contains(l.trigger)&&m()};return window.addEventListener("scroll",u,{capture:!0}),()=>window.removeEventListener("scroll",u,{capture:!0})}},[l.trigger,m]),t.jsx(Ue,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:o,onPointerDownOutside:d,onFocusOutside:u=>u.preventDefault(),onDismiss:m,children:t.jsxs(Ke,{"data-state":l.stateAttribute,...p,...c,ref:a,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[t.jsx(At,{children:s}),t.jsx(_t,{scope:r,isInside:!0,children:t.jsx(jt,{id:l.contentId,role:"tooltip",children:n||s})})]})})});he.displayName=C;var ve="TooltipArrow",we=i.forwardRef((e,a)=>{const{__scopeTooltip:r,...s}=e,n=O(r);return kt(ve,r).isInside?null:t.jsx(Ge,{...n,...s,ref:a})});we.displayName=ve;function Rt(e,a){const r=Math.abs(a.top-e.y),s=Math.abs(a.bottom-e.y),n=Math.abs(a.right-e.x),o=Math.abs(a.left-e.x);switch(Math.min(r,s,n,o)){case o:return"left";case n:return"right";case r:return"top";case s:return"bottom";default:throw new Error("unreachable")}}function Mt(e,a,r=5){const s=[];switch(a){case"top":s.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":s.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":s.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":s.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r});break}return s}function Lt(e){const{top:a,right:r,bottom:s,left:n}=e;return[{x:n,y:a},{x:r,y:a},{x:r,y:s},{x:n,y:s}]}function Pt(e,a){const{x:r,y:s}=e;let n=!1;for(let o=0,d=a.length-1;o<a.length;d=o++){const c=a[o],l=a[d],p=c.x,m=c.y,u=l.x,f=l.y;m>s!=f>s&&r<(u-p)*(s-m)/(f-m)+p&&(n=!n)}return n}function Dt(e){const a=e.slice();return a.sort((r,s)=>r.x<s.x?-1:r.x>s.x?1:r.y<s.y?-1:r.y>s.y?1:0),It(a)}function It(e){if(e.length<=1)return e.slice();const a=[];for(let s=0;s<e.length;s++){const n=e[s];for(;a.length>=2;){const o=a[a.length-1],d=a[a.length-2];if((o.x-d.x)*(n.y-d.y)>=(o.y-d.y)*(n.x-d.x))a.pop();else break}a.push(n)}a.pop();const r=[];for(let s=e.length-1;s>=0;s--){const n=e[s];for(;r.length>=2;){const o=r[r.length-1],d=r[r.length-2];if((o.x-d.x)*(n.y-d.y)>=(o.y-d.y)*(n.x-d.x))r.pop();else break}r.push(n)}return r.pop(),a.length===1&&r.length===1&&a[0].x===r[0].x&&a[0].y===r[0].y?a:a.concat(r)}var Ot=pe,zt=me,Bt=be,Ht=xe,$t=he,Vt=we;function je({delayDuration:e=0,...a}){return t.jsx(Ot,{"data-slot":"tooltip-provider",delayDuration:e,...a})}function qt({...e}){return t.jsx(je,{children:t.jsx(zt,{"data-slot":"tooltip",...e})})}function Ft({...e}){return t.jsx(Bt,{"data-slot":"tooltip-trigger",...e})}function Gt({className:e,sideOffset:a=0,children:r,...s}){return t.jsx(Ht,{children:t.jsxs($t,{"data-slot":"tooltip-content",sideOffset:a,className:x("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...s,children:[r,t.jsx(Vt,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}const Ut="sidebar_state",Kt=3600*24*7,Wt="16rem",Yt="18rem",Xt="3rem",Jt="b",ye=i.createContext(null);function S(){const e=i.useContext(ye);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}function Ga({defaultOpen:e=!0,open:a,onOpenChange:r,className:s,style:n,children:o,...d}){const c=gt(),[l,p]=i.useState(!1),[m,u]=i.useState(e),f=a??m,b=i.useCallback(v=>{const w=typeof v=="function"?v(f):v;r?r(w):u(w),document.cookie=`${Ut}=${w}; path=/; max-age=${Kt}`},[r,f]),g=i.useCallback(()=>c?p(v=>!v):b(v=>!v),[c,b,p]);i.useEffect(()=>{const v=w=>{w.key===Jt&&(w.metaKey||w.ctrlKey)&&(w.preventDefault(),g())};return window.addEventListener("keydown",v),()=>window.removeEventListener("keydown",v)},[g]);const h=f?"expanded":"collapsed",j=i.useMemo(()=>({state:h,open:f,setOpen:b,isMobile:c,openMobile:l,setOpenMobile:p,toggleSidebar:g}),[h,f,b,c,l,p,g]);return t.jsx(ye.Provider,{value:j,children:t.jsx(je,{delayDuration:0,children:t.jsx("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":Wt,"--sidebar-width-icon":Xt,...n},className:x("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",s),...d,children:o})})})}function Qt({side:e="left",variant:a="sidebar",collapsible:r="offcanvas",className:s,children:n,...o}){const{isMobile:d,state:c,openMobile:l,setOpenMobile:p}=S();return r==="none"?t.jsx("div",{"data-slot":"sidebar",className:x("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",s),...o,children:n}):d?t.jsx(ze,{open:l,onOpenChange:p,...o,children:t.jsxs(Be,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":Yt},side:e,children:[t.jsxs(He,{className:"sr-only",children:[t.jsx($e,{children:"Sidebar"}),t.jsx(Ve,{children:"Displays the mobile sidebar."})]}),t.jsx("div",{className:"flex h-full w-full flex-col",children:n})]})}):t.jsxs("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":c,"data-collapsible":c==="collapsed"?r:"","data-variant":a,"data-side":e,"data-slot":"sidebar",children:[t.jsx("div",{"data-slot":"sidebar-gap",className:x("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",a==="floating"||a==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),t.jsx("div",{"data-slot":"sidebar-container",className:x("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",a==="floating"||a==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",s),...o,children:t.jsx("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:n})})]})}function Ua({className:e,onClick:a,...r}){const{toggleSidebar:s}=S();return t.jsxs(Oe,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:x("size-7",e),onClick:n=>{a?.(n),s()},...r,children:[t.jsx(mt,{}),t.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function Zt({className:e,...a}){const{toggleSidebar:r}=S();return t.jsx("button",{"data-sidebar":"rail","data-slot":"sidebar-rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:r,title:"Toggle Sidebar",className:x("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex","in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...a})}function Ka({className:e,...a}){return t.jsx("main",{"data-slot":"sidebar-inset",className:x("bg-background relative flex min-w-0 flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",e),...a})}function ea({className:e,...a}){return t.jsx("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:x("flex flex-col gap-2 p-2 ",e),...a})}function ta({className:e,...a}){return t.jsx("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:x("flex flex-col gap-2 p-2",e),...a})}function aa({className:e,...a}){return t.jsx("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:x("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...a})}function ra({className:e,...a}){return t.jsx("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:x("relative flex w-full min-w-0 flex-col p-2",e),...a})}function M({className:e,...a}){return t.jsx("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:x("flex w-full min-w-0 flex-col gap-1",e),...a})}function L({className:e,...a}){return t.jsx("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:x("group/menu-item relative",e),...a})}const na=Ie("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function P({asChild:e=!1,isActive:a=!1,variant:r="default",size:s="default",tooltip:n,className:o,...d}){const c=e?F:"button",{isMobile:l,state:p}=S(),m=t.jsx(c,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":s,"data-active":a,className:x(na({variant:r,size:s}),o),...d});return n?(typeof n=="string"&&(n={children:n}),t.jsxs(qt,{children:[t.jsx(Ft,{asChild:!0,children:m}),t.jsx(Gt,{side:"right",align:"center",hidden:p!=="collapsed"||l,...n})]})):m}function sa({className:e,...a}){return t.jsx("ul",{"data-slot":"sidebar-menu-sub","data-sidebar":"menu-sub",className:x("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...a})}function oa({className:e,...a}){return t.jsx("li",{"data-slot":"sidebar-menu-sub-item","data-sidebar":"menu-sub-item",className:x("group/menu-sub-item relative",e),...a})}function ia({asChild:e=!1,size:a="md",isActive:r=!1,className:s,...n}){const o=e?F:"a";return t.jsx(o,{"data-slot":"sidebar-menu-sub-button","data-sidebar":"menu-sub-button","data-size":a,"data-active":r,className:x("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground",a==="sm"&&"text-xs",a==="md"&&"text-sm","group-data-[collapsible=icon]:hidden",s),...n})}function la({items:e}){const{handleSetCurrentBread:a}=et(),{addTab:r}=at(),s=n=>{r(n)};return t.jsx(ra,{children:t.jsx(M,{children:e.filter(n=>n.meta).sort((n,o)=>n.order-o.order).map(n=>t.jsx(bt,{asChild:!0,defaultOpen:n.meta.hideTab,className:"group/collapsible",children:t.jsxs(L,{children:[t.jsx(xt,{asChild:!0,children:t.jsxs(P,{tooltip:n.meta.label,children:[t.jsx("div",{className:"text-foreground p-1 rounded",children:n.meta.icon}),t.jsx("span",{className:"text-foreground font-bold",children:n.meta.label}),t.jsx(de,{className:"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"})]})}),t.jsx(ht,{children:t.jsx(sa,{children:n.children?.filter(o=>o.meta)?.map(o=>t.jsx(oa,{children:t.jsx(ia,{asChild:!0,children:t.jsx(Ae,{to:o.meta.key,onClick:()=>{a(o.meta.key),s(o)},children:t.jsx("span",{className:"text-foreground",children:o.meta.label})})})},o.meta.label))})})]})},n.meta.label))})})}var B={exports:{}},H={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var X;function da(){if(X)return H;X=1;var e=ke();function a(u,f){return u===f&&(u!==0||1/u===1/f)||u!==u&&f!==f}var r=typeof Object.is=="function"?Object.is:a,s=e.useState,n=e.useEffect,o=e.useLayoutEffect,d=e.useDebugValue;function c(u,f){var b=f(),g=s({inst:{value:b,getSnapshot:f}}),h=g[0].inst,j=g[1];return o(function(){h.value=b,h.getSnapshot=f,l(h)&&j({inst:h})},[u,b,f]),n(function(){return l(h)&&j({inst:h}),u(function(){l(h)&&j({inst:h})})},[u]),d(b),b}function l(u){var f=u.getSnapshot;u=u.value;try{var b=f();return!r(u,b)}catch{return!0}}function p(u,f){return f()}var m=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?p:c;return H.useSyncExternalStore=e.useSyncExternalStore!==void 0?e.useSyncExternalStore:m,H}var J;function ca(){return J||(J=1,B.exports=da()),B.exports}var ua=ca();function fa(){return ua.useSyncExternalStore(pa,()=>!0,()=>!1)}function pa(){return()=>{}}var K="Avatar",[ma,Wa]=ae(K),[ba,Ce]=ma(K),Se=i.forwardRef((e,a)=>{const{__scopeAvatar:r,...s}=e,[n,o]=i.useState("idle");return t.jsx(ba,{scope:r,imageLoadingStatus:n,onImageLoadingStatusChange:o,children:t.jsx(A.span,{...s,ref:a})})});Se.displayName=K;var Te="AvatarImage",Ne=i.forwardRef((e,a)=>{const{__scopeAvatar:r,src:s,onLoadingStatusChange:n=()=>{},...o}=e,d=Ce(Te,r),c=xa(s,o),l=rt(p=>{n(p),d.onImageLoadingStatusChange(p)});return $(()=>{c!=="idle"&&l(c)},[c,l]),c==="loaded"?t.jsx(A.img,{...o,ref:a,src:s}):null});Ne.displayName=Te;var Ee="AvatarFallback",_e=i.forwardRef((e,a)=>{const{__scopeAvatar:r,delayMs:s,...n}=e,o=Ce(Ee,r),[d,c]=i.useState(s===void 0);return i.useEffect(()=>{if(s!==void 0){const l=window.setTimeout(()=>c(!0),s);return()=>window.clearTimeout(l)}},[s]),d&&o.imageLoadingStatus!=="loaded"?t.jsx(A.span,{...n,ref:a}):null});_e.displayName=Ee;function Q(e,a){return e?a?(e.src!==a&&(e.src=a),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}function xa(e,{referrerPolicy:a,crossOrigin:r}){const s=fa(),n=i.useRef(null),o=s?(n.current||(n.current=new window.Image),n.current):null,[d,c]=i.useState(()=>Q(o,e));return $(()=>{c(Q(o,e))},[o,e]),$(()=>{const l=u=>()=>{c(u)};if(!o)return;const p=l("loaded"),m=l("error");return o.addEventListener("load",p),o.addEventListener("error",m),a&&(o.referrerPolicy=a),typeof r=="string"&&(o.crossOrigin=r),()=>{o.removeEventListener("load",p),o.removeEventListener("error",m)}},[o,r,a]),d}var ha=Se,ga=Ne,va=_e;function Z({className:e,...a}){return t.jsx(ha,{"data-slot":"avatar",className:x("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...a})}function wa({className:e,...a}){return t.jsx(ga,{"data-slot":"avatar-image",className:x("aspect-square size-full",e),...a})}function ee({className:e,...a}){return t.jsx(va,{"data-slot":"avatar-fallback",className:x("bg-muted flex size-full items-center justify-center rounded-full",e),...a})}function ja({user:e}){const{isMobile:a}=S();return!e||!e.name||!e.email||!e.avatar?t.jsx(M,{children:t.jsx(L,{children:t.jsxs(P,{size:"lg",disabled:!0,children:[t.jsx(Z,{className:"h-8 w-8 rounded-lg",children:t.jsx(ee,{className:"rounded-lg",children:"--"})}),t.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[t.jsx("span",{className:"truncate font-medium",children:"加载中..."}),t.jsx("span",{className:"truncate text-xs",children:"--"})]})]})})}):t.jsx(M,{children:t.jsx(L,{children:t.jsxs(ne,{children:[t.jsx(se,{asChild:!0,children:t.jsxs(P,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[t.jsxs(Z,{className:"h-8 w-8 rounded-lg",children:[t.jsx(wa,{src:e.avatar,alt:e.name}),t.jsx(ee,{className:"rounded-lg",children:"CN"})]}),t.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[t.jsx("span",{className:"truncate font-medium",children:e.name}),t.jsx("span",{className:"truncate text-xs",children:e.email})]}),t.jsx(ce,{className:"ml-auto size-4"})]})}),t.jsxs(oe,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",side:a?"bottom":"right",align:"end",sideOffset:4,children:[t.jsxs(We,{children:[t.jsxs(_,{children:[t.jsx(nt,{}),"账户"]}),t.jsxs(_,{children:[t.jsx(lt,{}),"通知"]})]}),t.jsx(ie,{}),t.jsxs(_,{children:[t.jsx(ft,{}),"登出"]})]})]})})})}function ya({teams:e}){const{isMobile:a}=S(),[r,s]=i.useState(e[0]);return r?t.jsx(M,{children:t.jsx(L,{children:t.jsxs(ne,{children:[t.jsx(se,{asChild:!0,children:t.jsxs(P,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground bg-primary ",children:[t.jsx("div",{className:"text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg bg-background",children:t.jsx(ct,{className:"size-4 text-foreground"})}),t.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[t.jsx("span",{className:"truncate text-background font-bold",children:r.name}),t.jsx("span",{className:"truncate text-xs",children:r.plan})]}),t.jsx(ce,{className:"ml-auto"})]})}),t.jsxs(oe,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg ",align:"start",side:a?"bottom":"right",sideOffset:4,children:[t.jsx(Ye,{className:"text-muted-foreground text-xs",children:"项目"}),e.map((n,o)=>t.jsxs(_,{onClick:()=>s(n),className:"gap-2 p-2",children:[t.jsx("div",{className:"flex size-6 items-center justify-center rounded-md border",children:t.jsx(n.logo,{className:"size-3.5 shrink-0"})}),n.name,t.jsxs(Xe,{children:["⌘",o+1]})]},n.name)),t.jsx(ie,{}),t.jsxs(_,{className:"gap-2 p-2 ",children:[t.jsx("div",{className:"flex size-6 items-center justify-center rounded-md border bg-transparent",children:t.jsx(st,{className:"size-4"})}),t.jsx("div",{className:"text-muted-foreground font-medium",children:"Add team"})]})]})]})})}):null}function Ya({...e}){const{sidebarNavItems:a,sidebarTeams:r}=tt(),{userProfile:s}=Re();return t.jsxs(Qt,{collapsible:"icon",...e,children:[t.jsx(ea,{children:t.jsx(ya,{teams:r()})}),t.jsx(aa,{children:t.jsx(ot,{className:"h-full",children:t.jsx(la,{items:a})})}),t.jsx(ta,{children:t.jsx(ja,{user:s})}),t.jsx(Zt,{})]})}function Xa({...e}){return t.jsx("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e})}function Ja({className:e,...a}){return t.jsx("ol",{"data-slot":"breadcrumb-list",className:x("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",e),...a})}function Qa({className:e,...a}){return t.jsx("li",{"data-slot":"breadcrumb-item",className:x("inline-flex items-center gap-1.5",e),...a})}function Za({asChild:e,className:a,...r}){const s=e?F:"a";return t.jsx(s,{"data-slot":"breadcrumb-link",className:x("hover:text-foreground transition-colors",a),...r})}function er({className:e,...a}){return t.jsx("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:x("text-foreground font-normal",e),...a})}function tr({children:e,className:a,...r}){return t.jsx("span",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:x("[&>svg]:size-3.5",a),...r,children:e??t.jsx(de,{})})}export{Ya as A,Xa as B,Ga as S,Ka as a,Ua as b,Ja as c,Qa as d,er as e,Za as f,tr as g};
