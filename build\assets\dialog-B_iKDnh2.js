import{j as f,o as e,R as b}from"./index-hfMliPo3.js";import{c as X,P as N,B as Y}from"./button-BDk51iWJ.js";import{D as je,a as Te,b as Ce,c as Ie,d as Fe,e as we,f as _e}from"./dialog-DC2GGX8r.js";import{c as Pe}from"./index-BMNOPYCw.js";import{u as Ge,e as Se,c as U}from"./index-BcAf74l_.js";import{u as Re}from"./index-CRUxeMtj.js";import{u as Ee}from"./index-CEfSuq4R.js";import{u as M}from"./index-B6uarZlB.js";import{u as Z}from"./index-BkY3WHUe.js";import{I as k}from"./input-CuqQAyEP.js";import{u as ye,a as Ne,F as De,b as _,c as P,d as S,e as R,f as E,o as ke,s as y,g as Ae,n as W}from"./form-mGX1_jA3.js";import{t as Me}from"./index-lZgBQfjq.js";import{P as Oe}from"./permissiom-select-tree-Bvi6HCgs.js";function G(o,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(o?.(n),r===!1||!n.defaultPrevented)return t?.(n)}}var L="rovingFocusGroup.onEntryFocus",Ve={bubbles:!1,cancelable:!0},D="RovingFocusGroup",[K,ee,ze]=Pe(D),[Le,oe]=X(D,[ze]),[Ke,Ue]=Le(D),re=f.forwardRef((o,t)=>e.jsx(K.Provider,{scope:o.__scopeRovingFocusGroup,children:e.jsx(K.Slot,{scope:o.__scopeRovingFocusGroup,children:e.jsx(Be,{...o,ref:t})})}));re.displayName=D;var Be=f.forwardRef((o,t)=>{const{__scopeRovingFocusGroup:r,orientation:s,loop:n=!1,dir:a,currentTabStopId:l,defaultCurrentTabStopId:i,onCurrentTabStopIdChange:g,onEntryFocus:m,preventScrollOnEntryFocus:d=!1,...c}=o,T=f.useRef(null),u=Ge(t,T),v=Z(a),[j,p]=M({prop:l,defaultProp:i??null,onChange:g,caller:D}),[C,O]=f.useState(!1),h=Ee(m),I=ee(r),V=f.useRef(!1),[me,$]=f.useState(0);return f.useEffect(()=>{const x=T.current;if(x)return x.addEventListener(L,h),()=>x.removeEventListener(L,h)},[h]),e.jsx(Ke,{scope:r,orientation:s,dir:v,loop:n,currentTabStopId:j,onItemFocus:f.useCallback(x=>p(x),[p]),onItemShiftTab:f.useCallback(()=>O(!0),[]),onFocusableItemAdd:f.useCallback(()=>$(x=>x+1),[]),onFocusableItemRemove:f.useCallback(()=>$(x=>x-1),[]),children:e.jsx(N.div,{tabIndex:C||me===0?-1:0,"data-orientation":s,...c,ref:u,style:{outline:"none",...o.style},onMouseDown:G(o.onMouseDown,()=>{V.current=!0}),onFocus:G(o.onFocus,x=>{const ve=!V.current;if(x.target===x.currentTarget&&ve&&!C){const H=new CustomEvent(L,Ve);if(x.currentTarget.dispatchEvent(H),!H.defaultPrevented){const z=I().filter(w=>w.focusable),xe=z.find(w=>w.active),he=z.find(w=>w.id===j),be=[xe,he,...z].filter(Boolean).map(w=>w.ref.current);ne(be,d)}}V.current=!1}),onBlur:G(o.onBlur,()=>O(!1))})})}),te="RovingFocusGroupItem",se=f.forwardRef((o,t)=>{const{__scopeRovingFocusGroup:r,focusable:s=!0,active:n=!1,tabStopId:a,children:l,...i}=o,g=Re(),m=a||g,d=Ue(te,r),c=d.currentTabStopId===m,T=ee(r),{onFocusableItemAdd:u,onFocusableItemRemove:v,currentTabStopId:j}=d;return f.useEffect(()=>{if(s)return u(),()=>v()},[s,u,v]),e.jsx(K.ItemSlot,{scope:r,id:m,focusable:s,active:n,children:e.jsx(N.span,{tabIndex:c?0:-1,"data-orientation":d.orientation,...i,ref:t,onMouseDown:G(o.onMouseDown,p=>{s?d.onItemFocus(m):p.preventDefault()}),onFocus:G(o.onFocus,()=>d.onItemFocus(m)),onKeyDown:G(o.onKeyDown,p=>{if(p.key==="Tab"&&p.shiftKey){d.onItemShiftTab();return}if(p.target!==p.currentTarget)return;const C=Ye(p,d.orientation,d.dir);if(C!==void 0){if(p.metaKey||p.ctrlKey||p.altKey||p.shiftKey)return;p.preventDefault();let h=T().filter(I=>I.focusable).map(I=>I.ref.current);if(C==="last")h.reverse();else if(C==="prev"||C==="next"){C==="prev"&&h.reverse();const I=h.indexOf(p.currentTarget);h=d.loop?We(h,I+1):h.slice(I+1)}setTimeout(()=>ne(h))}}),children:typeof l=="function"?l({isCurrentTabStop:c,hasTabStop:j!=null}):l})})});se.displayName=te;var $e={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function He(o,t){return t!=="rtl"?o:o==="ArrowLeft"?"ArrowRight":o==="ArrowRight"?"ArrowLeft":o}function Ye(o,t,r){const s=He(o.key,r);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(s))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(s)))return $e[s]}function ne(o,t=!1){const r=document.activeElement;for(const s of o)if(s===r||(s.focus({preventScroll:t}),document.activeElement!==r))return}function We(o,t){return o.map((r,s)=>o[(t+s)%o.length])}var qe=re,Je=se;function Qe(o,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(o?.(n),r===!1||!n.defaultPrevented)return t?.(n)}}var ae="Toggle",ie=f.forwardRef((o,t)=>{const{pressed:r,defaultPressed:s,onPressedChange:n,...a}=o,[l,i]=M({prop:r,onChange:n,defaultProp:s??!1,caller:ae});return e.jsx(N.button,{type:"button","aria-pressed":l,"data-state":l?"on":"off","data-disabled":o.disabled?"":void 0,...a,ref:t,onClick:Qe(o.onClick,()=>{o.disabled||i(!l)})})});ie.displayName=ae;var F="ToggleGroup",[le,Fo]=X(F,[oe]),ce=oe(),B=b.forwardRef((o,t)=>{const{type:r,...s}=o;if(r==="single"){const n=s;return e.jsx(Xe,{...n,ref:t})}if(r==="multiple"){const n=s;return e.jsx(Ze,{...n,ref:t})}throw new Error(`Missing prop \`type\` expected on \`${F}\``)});B.displayName=F;var[ue,de]=le(F),Xe=b.forwardRef((o,t)=>{const{value:r,defaultValue:s,onValueChange:n=()=>{},...a}=o,[l,i]=M({prop:r,defaultProp:s??"",onChange:n,caller:F});return e.jsx(ue,{scope:o.__scopeToggleGroup,type:"single",value:b.useMemo(()=>l?[l]:[],[l]),onItemActivate:i,onItemDeactivate:b.useCallback(()=>i(""),[i]),children:e.jsx(fe,{...a,ref:t})})}),Ze=b.forwardRef((o,t)=>{const{value:r,defaultValue:s,onValueChange:n=()=>{},...a}=o,[l,i]=M({prop:r,defaultProp:s??[],onChange:n,caller:F}),g=b.useCallback(d=>i((c=[])=>[...c,d]),[i]),m=b.useCallback(d=>i((c=[])=>c.filter(T=>T!==d)),[i]);return e.jsx(ue,{scope:o.__scopeToggleGroup,type:"multiple",value:l,onItemActivate:g,onItemDeactivate:m,children:e.jsx(fe,{...a,ref:t})})});B.displayName=F;var[eo,oo]=le(F),fe=b.forwardRef((o,t)=>{const{__scopeToggleGroup:r,disabled:s=!1,rovingFocus:n=!0,orientation:a,dir:l,loop:i=!0,...g}=o,m=ce(r),d=Z(l),c={role:"group",dir:d,...g};return e.jsx(eo,{scope:r,rovingFocus:n,disabled:s,children:n?e.jsx(qe,{asChild:!0,...m,orientation:a,dir:d,loop:i,children:e.jsx(N.div,{...c,ref:t})}):e.jsx(N.div,{...c,ref:t})})}),A="ToggleGroupItem",pe=b.forwardRef((o,t)=>{const r=de(A,o.__scopeToggleGroup),s=oo(A,o.__scopeToggleGroup),n=ce(o.__scopeToggleGroup),a=r.value.includes(o.value),l=s.disabled||o.disabled,i={...o,pressed:a,disabled:l},g=b.useRef(null);return s.rovingFocus?e.jsx(Je,{asChild:!0,...n,focusable:!l,active:a,ref:g,children:e.jsx(q,{...i,ref:t})}):e.jsx(q,{...i,ref:t})});pe.displayName=A;var q=b.forwardRef((o,t)=>{const{__scopeToggleGroup:r,value:s,...n}=o,a=de(A,r),l={role:"radio","aria-checked":o.pressed,"aria-pressed":void 0},i=a.type==="single"?l:void 0;return e.jsx(ie,{...i,...n,ref:t,onPressedChange:g=>{g?a.onItemActivate(s):a.onItemDeactivate(s)}})}),ro=B,to=pe;const so=Se("inline-flex items-center justify-center gap-2 rounded-md text-sm font-medium hover:bg-muted hover:text-muted-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] outline-none transition-[color,box-shadow] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive whitespace-nowrap",{variants:{variant:{default:"bg-transparent",outline:"border border-input bg-transparent shadow-xs hover:bg-accent hover:text-accent-foreground"},size:{default:"h-9 px-2 min-w-9",sm:"h-8 px-1.5 min-w-8",lg:"h-10 px-2.5 min-w-10"}},defaultVariants:{variant:"default",size:"default"}}),ge=f.createContext({size:"default",variant:"default"});function no({className:o,variant:t,size:r,children:s,...n}){return e.jsx(ro,{"data-slot":"toggle-group","data-variant":t,"data-size":r,className:U("group/toggle-group flex w-fit items-center rounded-md data-[variant=outline]:shadow-xs",o),...n,children:e.jsx(ge.Provider,{value:{variant:t,size:r},children:s})})}function J({className:o,children:t,variant:r,size:s,...n}){const a=f.useContext(ge);return e.jsx(to,{"data-slot":"toggle-group-item","data-variant":a.variant||r,"data-size":a.size||s,className:U(so({variant:a.variant||r,size:a.size||s}),"min-w-0 flex-1 shrink-0 rounded-none shadow-none first:rounded-l-md last:rounded-r-md focus:z-10 focus-visible:z-10 data-[variant=outline]:border-l-0 data-[variant=outline]:first:border-l",o),...n,children:t})}function ao({className:o,...t}){return e.jsx("textarea",{"data-slot":"textarea",className:U("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",o),...t})}function io(o){const t=[];function r(s){s.forEach(n=>{n.children&&r(n.children),t.push(n)})}return r(o),t}const Q=ke({name:y().min(2,{message:"用户名最短需要2个字符"}),label:y().min(2,{message:"用户名最短需要2个字符"}),status:W().min(0).max(1),order:W(),id:y(),permissionIds:Ae(y()).optional(),desc:y().optional()});function lo({open:o,onClose:t,updateItem:r,handleCreate:s,handleEdit:n}){const a=!!r,[l,i]=f.useState([]),[g,m]=f.useState("1");f.useEffect(()=>{if(a){const v=io(r.permission).map(j=>j.id);i(v)}return()=>{i([])}},[a,r]);const d=()=>a?Q.extend({}):Q,c=ye({resolver:Ne(d()),defaultValues:{id:"",name:"",label:"",status:0,order:0,permissionIds:[]}});f.useEffect(()=>{r?c.reset({id:r.id,name:r.name,label:r.label,status:r.status,order:r.order,desc:r.desc}):c.reset({id:"",name:"",label:"",status:0,order:0,permissionIds:[],desc:""})},[r,c]),f.useEffect(()=>{o||(c.reset({id:"",name:"",label:"",status:0,order:0,permissionIds:[],desc:""}),i([]),m("1"))},[o,c]);const T=u=>{const v=d(),j={...u,permissionIds:l},p=v.safeParse(j);p.success?(a?n(j):s(j),t?.()):(Me.error("表单提交失败，请检查字段！"),console.log("验证错误:",p.error.issues))};return e.jsx(je,{open:o,onOpenChange:t,children:e.jsxs(Te,{className:"sm:max-w-[800px] ",children:[e.jsxs(Ce,{children:[e.jsx(Ie,{children:a?"编辑角色":"新增角色"}),e.jsx(Fe,{children:a?"修改角色信息，点击保存完成更新。":"填写角色信息，点击保存完成创建。"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-5",children:[e.jsx(De,{...c,children:e.jsxs("form",{id:"create-user-form",onSubmit:c.handleSubmit(T),className:"space-y-3.5",children:[e.jsx(_,{control:c.control,name:"status",render:({field:u})=>e.jsx(P,{children:e.jsxs(no,{variant:"outline",type:"single",value:g,onValueChange:v=>{m(v),u.onChange(Number(v))},children:[e.jsx(J,{value:"1",children:e.jsx("p",{className:"italic text-xs ",children:"开启"})}),e.jsx(J,{value:"0",children:e.jsx("p",{className:"italic text-xs ",children:"禁用"})})]})})}),a&&e.jsx(_,{control:c.control,name:"id",render:({field:u})=>e.jsxs(P,{children:[e.jsx(S,{children:"ID"}),e.jsx(R,{children:e.jsx(k,{placeholder:u.value,className:"placeholder:text-sm focus-visible:ring-pink-200",...u,disabled:!0})}),e.jsx(E,{})]})}),e.jsx(_,{control:c.control,name:"name",render:({field:u})=>e.jsxs(P,{children:[e.jsx(S,{children:"名称"}),e.jsx(R,{children:e.jsx(k,{placeholder:"请输入名称",className:"placeholder:text-sm focus-visible:ring-pink-200",...u})}),e.jsx(E,{})]})}),e.jsx(_,{control:c.control,name:"label",render:({field:u})=>e.jsxs(P,{children:[e.jsx(S,{children:"标签"}),e.jsx(R,{children:e.jsx(k,{placeholder:"请输入标签",className:"placeholder:text-sm focus-visible:ring-pink-200",...u})}),e.jsx(E,{})]})}),e.jsx(_,{control:c.control,name:"order",render:({field:u})=>e.jsxs(P,{children:[e.jsx(S,{children:"顺序"}),e.jsx(R,{children:e.jsx(k,{type:"number",placeholder:"输入顺序",className:"placeholder:text-sm focus-visible:ring-pink-200",...u,onChange:v=>u.onChange(+v.target.value),value:u.value})}),e.jsx(E,{})]})}),e.jsx(_,{control:c.control,name:"desc",render:({field:u})=>e.jsxs(P,{children:[e.jsx(S,{children:"描述"}),e.jsx(R,{children:e.jsx(ao,{placeholder:"角色描述",className:"placeholder:text-sm focus-visible:ring-pink-200",...u})}),e.jsx(E,{})]})})]})}),e.jsx(Oe,{checked:l,setChecked:i})]}),e.jsxs(we,{children:[e.jsx(_e,{asChild:!0,children:e.jsx(Y,{variant:"outline",onClick:t,children:"取消"})}),e.jsx(Y,{type:"submit",form:"create-user-form",children:a?"更新":"保存"})]})]})})}const wo=Object.freeze(Object.defineProperty({__proto__:null,default:lo},Symbol.toStringTag,{value:"Module"}));export{lo as R,no as T,wo as _,J as a};
