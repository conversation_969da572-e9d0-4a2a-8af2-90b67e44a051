import{j as t,o as a}from"./index-hfMliPo3.js";import{c as o}from"./columns-Dw8Jh_mu.js";import{s as r}from"./mock-server-list-CdTKvwmG.js";import{S as s}from"./index-Cv7_PkAh.js";const c=r.map(e=>({...e,arichiveDate:new Date(e.arichiveDate)}));function i(){const[e]=t.useState(c);return a.jsx("div",{className:"container mx-auto py-10 h-[calc(100vh-105px)]",children:a.jsx(s,{columns:o,data:e})})}const u=Object.freeze(Object.defineProperty({__proto__:null,default:i},Symbol.toStringTag,{value:"Module"}));export{u as _};
