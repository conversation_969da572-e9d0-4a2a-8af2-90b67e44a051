import{o as t}from"./index-hfMliPo3.js";import{c as o}from"./index-BcAf74l_.js";function e({className:a,...r}){return t.jsx("div",{"data-slot":"card",className:o("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...r})}function n({className:a,...r}){return t.jsx("div",{"data-slot":"card-header",className:o("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...r})}function c({className:a,...r}){return t.jsx("div",{"data-slot":"card-title",className:o("leading-none font-semibold",a),...r})}function i({className:a,...r}){return t.jsx("div",{"data-slot":"card-content",className:o("px-6",a),...r})}export{e as C,i as a,n as b,c};
