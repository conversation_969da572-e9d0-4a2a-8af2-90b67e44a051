import{j as s,o as r}from"./index-hfMliPo3.js";import{P as l}from"./button-BDk51iWJ.js";import{c as n}from"./index-BcAf74l_.js";var i="Label",o=s.forwardRef((t,a)=>r.jsx(l.label,{...t,ref:a,onMouseDown:e=>{e.target.closest("button, input, select, textarea")||(t.onMouseDown?.(e),!e.defaultPrevented&&e.detail>1&&e.preventDefault())}}));o.displayName=i;var d=o;function f({className:t,...a}){return r.jsx(d,{"data-slot":"label",className:n("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}export{f as L};
