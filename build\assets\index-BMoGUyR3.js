import{j as a,o as t}from"./index-hfMliPo3.js";import{c as o}from"./columns-CPIvcQgm.js";import{s as r}from"./mock-server-list-CdTKvwmG.js";import{S as s}from"./index-Cv7_PkAh.js";const c=r.map(e=>({...e,arichiveDate:new Date(e.arichiveDate)}));function i(){const[e]=a.useState(c);return t.jsx("div",{className:"container mx-auto py-4",children:t.jsx(s,{columns:o,data:e})})}const u=Object.freeze(Object.defineProperty({__proto__:null,default:i},Symbol.toStringTag,{value:"Module"}));export{u as _};
