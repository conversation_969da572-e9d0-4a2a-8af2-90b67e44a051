import{R as r,o as d}from"./index-hfMliPo3.js";import{c as y}from"./button-BDk51iWJ.js";import{u as S,d as M}from"./index-BcAf74l_.js";function L(s){const a=s+"CollectionProvider",[A,E]=y(a),[N,m]=A(a,{collectionRef:{current:null},itemMap:new Map}),p=c=>{const{scope:e,children:l}=c,o=r.useRef(null),t=r.useRef(new Map).current;return d.jsx(N,{scope:e,itemMap:t,collectionRef:o,children:l})};p.displayName=a;const f=s+"CollectionSlot",T=M(f),C=r.forwardRef((c,e)=>{const{scope:l,children:o}=c,t=m(f,l),n=S(e,t.collectionRef);return d.jsx(T,{ref:n,children:o})});C.displayName=f;const u=s+"CollectionItemSlot",R="data-radix-collection-item",O=M(u),x=r.forwardRef((c,e)=>{const{scope:l,children:o,...t}=c,n=r.useRef(null),I=S(e,n),i=m(u,l);return r.useEffect(()=>(i.itemMap.set(n,{ref:n,...t}),()=>void i.itemMap.delete(n))),d.jsx(O,{[R]:"",ref:I,children:o})});x.displayName=u;function _(c){const e=m(s+"CollectionConsumer",c);return r.useCallback(()=>{const o=e.collectionRef.current;if(!o)return[];const t=Array.from(o.querySelectorAll(`[${R}]`));return Array.from(e.itemMap.values()).sort((i,v)=>t.indexOf(i.ref.current)-t.indexOf(v.ref.current))},[e.collectionRef,e.itemMap])}return[{Provider:p,Slot:C,ItemSlot:x},_,E]}export{L as c};
