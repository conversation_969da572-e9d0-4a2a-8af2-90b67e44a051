import{w as sd,x as ve,R as re,m as ps,j as c,y as ld,z as P,A as $,B as Xo,C as cd,v as za,D as oo,E as ar,o as It}from"./index-hfMliPo3.js";var Si={exports:{}};/*!
	Copyright (c) 2018 <PERSON>.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var bs;function ud(){return bs||(bs=1,function(e){(function(){var t={}.hasOwnProperty;function r(){for(var i="",a=0;a<arguments.length;a++){var s=arguments[a];s&&(i=o(i,n(s)))}return i}function n(i){if(typeof i=="string"||typeof i=="number")return i;if(typeof i!="object")return"";if(Array.isArray(i))return r.apply(null,i);if(i.toString!==Object.prototype.toString&&!i.toString.toString().includes("[native code]"))return i.toString();var a="";for(var s in i)t.call(i,s)&&i[s]&&(a=o(a,s));return a}function o(i,a){return a?i?i+" "+a:i+a:i}e.exports?(r.default=r,e.exports=r):window.classNames=r})()}(Si)),Si.exports}var dd=ud();const Q=sd(dd);function oe(){return oe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},oe.apply(null,arguments)}var fd=Symbol.for("react.element"),vd=Symbol.for("react.transitional.element"),md=Symbol.for("react.fragment");function Jl(e){return e&&ve(e)==="object"&&(e.$$typeof===fd||e.$$typeof===vd)&&e.type===md}function Wr(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=[];return re.Children.forEach(e,function(n){n==null&&!t.keepEmpty||(Array.isArray(n)?r=r.concat(Wr(n)):Jl(n)&&n.props?r=r.concat(Wr(n.props.children,t)):r.push(n))}),r}var Ui={},gd=function(t){};function hd(e,t){}function pd(e,t){}function bd(){Ui={}}function ec(e,t,r){!t&&!Ui[r]&&(e(!1,r),Ui[r]=!0)}function St(e,t){ec(hd,e,t)}function yd(e,t){ec(pd,e,t)}St.preMessage=gd;St.resetWarned=bd;St.noteOnce=yd;function qn(e){return e instanceof HTMLElement||e instanceof SVGElement}function Cd(e){return e&&ve(e)==="object"&&qn(e.nativeElement)?e.nativeElement:qn(e)?e:null}function Po(e){var t=Cd(e);if(t)return t;if(e instanceof re.Component){var r;return(r=ps.findDOMNode)===null||r===void 0?void 0:r.call(ps,e)}return null}var xi={exports:{}},je={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ys;function Sd(){if(ys)return je;ys=1;var e=Symbol.for("react.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),n=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),a=Symbol.for("react.context"),s=Symbol.for("react.server_context"),l=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),f=Symbol.for("react.memo"),m=Symbol.for("react.lazy"),p=Symbol.for("react.offscreen"),g;g=Symbol.for("react.module.reference");function h(v){if(typeof v=="object"&&v!==null){var y=v.$$typeof;switch(y){case e:switch(v=v.type,v){case r:case o:case n:case u:case d:return v;default:switch(v=v&&v.$$typeof,v){case s:case a:case l:case m:case f:case i:return v;default:return y}}case t:return y}}}return je.ContextConsumer=a,je.ContextProvider=i,je.Element=e,je.ForwardRef=l,je.Fragment=r,je.Lazy=m,je.Memo=f,je.Portal=t,je.Profiler=o,je.StrictMode=n,je.Suspense=u,je.SuspenseList=d,je.isAsyncMode=function(){return!1},je.isConcurrentMode=function(){return!1},je.isContextConsumer=function(v){return h(v)===a},je.isContextProvider=function(v){return h(v)===i},je.isElement=function(v){return typeof v=="object"&&v!==null&&v.$$typeof===e},je.isForwardRef=function(v){return h(v)===l},je.isFragment=function(v){return h(v)===r},je.isLazy=function(v){return h(v)===m},je.isMemo=function(v){return h(v)===f},je.isPortal=function(v){return h(v)===t},je.isProfiler=function(v){return h(v)===o},je.isStrictMode=function(v){return h(v)===n},je.isSuspense=function(v){return h(v)===u},je.isSuspenseList=function(v){return h(v)===d},je.isValidElementType=function(v){return typeof v=="string"||typeof v=="function"||v===r||v===o||v===n||v===u||v===d||v===p||typeof v=="object"&&v!==null&&(v.$$typeof===m||v.$$typeof===f||v.$$typeof===i||v.$$typeof===a||v.$$typeof===l||v.$$typeof===g||v.getModuleId!==void 0)},je.typeOf=h,je}var Cs;function xd(){return Cs||(Cs=1,xi.exports=Sd()),xi.exports}var wi=xd();function Qo(e,t,r){var n=c.useRef({});return(!("value"in n.current)||r(n.current.condition,t))&&(n.current.value=e(),n.current.condition=t),n.current.value}var wd=Number(c.version.split(".")[0]),ka=function(t,r){typeof t=="function"?t(r):ve(t)==="object"&&t&&"current"in t&&(t.current=r)},Zo=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var o=r.filter(Boolean);return o.length<=1?o[0]:function(i){r.forEach(function(a){ka(a,i)})}},io=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return Qo(function(){return Zo.apply(void 0,r)},r,function(o,i){return o.length!==i.length||o.every(function(a,s){return a!==i[s]})})},ao=function(t){var r,n;if(!t)return!1;if(tc(t)&&wd>=19)return!0;var o=wi.isMemo(t)?t.type.type:t.type;return!(typeof o=="function"&&!((r=o.prototype)!==null&&r!==void 0&&r.render)&&o.$$typeof!==wi.ForwardRef||typeof t=="function"&&!((n=t.prototype)!==null&&n!==void 0&&n.render)&&t.$$typeof!==wi.ForwardRef)};function tc(e){return c.isValidElement(e)&&!Jl(e)}var Yo=function(t){if(t&&tc(t)){var r=t;return r.props.propertyIsEnumerable("ref")?r.props.ref:r.ref}return null},Ki=c.createContext(null);function $d(e){var t=e.children,r=e.onBatchResize,n=c.useRef(0),o=c.useRef([]),i=c.useContext(Ki),a=c.useCallback(function(s,l,u){n.current+=1;var d=n.current;o.current.push({size:s,element:l,data:u}),Promise.resolve().then(function(){d===n.current&&(r?.(o.current),o.current=[])}),i?.(s,l,u)},[r,i]);return c.createElement(Ki.Provider,{value:a},t)}var rc=function(){if(typeof Map<"u")return Map;function e(t,r){var n=-1;return t.some(function(o,i){return o[0]===r?(n=i,!0):!1}),n}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(r){var n=e(this.__entries__,r),o=this.__entries__[n];return o&&o[1]},t.prototype.set=function(r,n){var o=e(this.__entries__,r);~o?this.__entries__[o][1]=n:this.__entries__.push([r,n])},t.prototype.delete=function(r){var n=this.__entries__,o=e(n,r);~o&&n.splice(o,1)},t.prototype.has=function(r){return!!~e(this.__entries__,r)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(r,n){n===void 0&&(n=null);for(var o=0,i=this.__entries__;o<i.length;o++){var a=i[o];r.call(n,a[1],a[0])}},t}()}(),Gi=typeof window<"u"&&typeof document<"u"&&window.document===document,Bo=function(){return typeof global<"u"&&global.Math===Math?global:typeof self<"u"&&self.Math===Math?self:typeof window<"u"&&window.Math===Math?window:Function("return this")()}(),Ed=function(){return typeof requestAnimationFrame=="function"?requestAnimationFrame.bind(Bo):function(e){return setTimeout(function(){return e(Date.now())},1e3/60)}}(),Od=2;function Id(e,t){var r=!1,n=!1,o=0;function i(){r&&(r=!1,e()),n&&s()}function a(){Ed(i)}function s(){var l=Date.now();if(r){if(l-o<Od)return;n=!0}else r=!0,n=!1,setTimeout(a,t);o=l}return s}var Pd=20,Rd=["top","right","bottom","left","width","height","size","weight"],Md=typeof MutationObserver<"u",Td=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=Id(this.refresh.bind(this),Pd)}return e.prototype.addObserver=function(t){~this.observers_.indexOf(t)||this.observers_.push(t),this.connected_||this.connect_()},e.prototype.removeObserver=function(t){var r=this.observers_,n=r.indexOf(t);~n&&r.splice(n,1),!r.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){var t=this.updateObservers_();t&&this.refresh()},e.prototype.updateObservers_=function(){var t=this.observers_.filter(function(r){return r.gatherActive(),r.hasActive()});return t.forEach(function(r){return r.broadcastActive()}),t.length>0},e.prototype.connect_=function(){!Gi||this.connected_||(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),Md?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){!Gi||!this.connected_||(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(t){var r=t.propertyName,n=r===void 0?"":r,o=Rd.some(function(i){return!!~n.indexOf(i)});o&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),nc=function(e,t){for(var r=0,n=Object.keys(t);r<n.length;r++){var o=n[r];Object.defineProperty(e,o,{value:t[o],enumerable:!1,writable:!1,configurable:!0})}return e},vn=function(e){var t=e&&e.ownerDocument&&e.ownerDocument.defaultView;return t||Bo},oc=Jo(0,0,0,0);function zo(e){return parseFloat(e)||0}function Ss(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];return t.reduce(function(n,o){var i=e["border-"+o+"-width"];return n+zo(i)},0)}function _d(e){for(var t=["top","right","bottom","left"],r={},n=0,o=t;n<o.length;n++){var i=o[n],a=e["padding-"+i];r[i]=zo(a)}return r}function Ad(e){var t=e.getBBox();return Jo(0,0,t.width,t.height)}function Fd(e){var t=e.clientWidth,r=e.clientHeight;if(!t&&!r)return oc;var n=vn(e).getComputedStyle(e),o=_d(n),i=o.left+o.right,a=o.top+o.bottom,s=zo(n.width),l=zo(n.height);if(n.boxSizing==="border-box"&&(Math.round(s+i)!==t&&(s-=Ss(n,"left","right")+i),Math.round(l+a)!==r&&(l-=Ss(n,"top","bottom")+a)),!Nd(e)){var u=Math.round(s+i)-t,d=Math.round(l+a)-r;Math.abs(u)!==1&&(s-=u),Math.abs(d)!==1&&(l-=d)}return Jo(o.left,o.top,s,l)}var jd=function(){return typeof SVGGraphicsElement<"u"?function(e){return e instanceof vn(e).SVGGraphicsElement}:function(e){return e instanceof vn(e).SVGElement&&typeof e.getBBox=="function"}}();function Nd(e){return e===vn(e).document.documentElement}function Ld(e){return Gi?jd(e)?Ad(e):Fd(e):oc}function Bd(e){var t=e.x,r=e.y,n=e.width,o=e.height,i=typeof DOMRectReadOnly<"u"?DOMRectReadOnly:Object,a=Object.create(i.prototype);return nc(a,{x:t,y:r,width:n,height:o,top:r,right:t+n,bottom:o+r,left:t}),a}function Jo(e,t,r,n){return{x:e,y:t,width:r,height:n}}var zd=function(){function e(t){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=Jo(0,0,0,0),this.target=t}return e.prototype.isActive=function(){var t=Ld(this.target);return this.contentRect_=t,t.width!==this.broadcastWidth||t.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var t=this.contentRect_;return this.broadcastWidth=t.width,this.broadcastHeight=t.height,t},e}(),kd=function(){function e(t,r){var n=Bd(r);nc(this,{target:t,contentRect:n})}return e}(),Hd=function(){function e(t,r,n){if(this.activeObservations_=[],this.observations_=new rc,typeof t!="function")throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=t,this.controller_=r,this.callbackCtx_=n}return e.prototype.observe=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(t instanceof vn(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var r=this.observations_;r.has(t)||(r.set(t,new zd(t)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(t instanceof vn(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var r=this.observations_;r.has(t)&&(r.delete(t),r.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var t=this;this.clearActive(),this.observations_.forEach(function(r){r.isActive()&&t.activeObservations_.push(r)})},e.prototype.broadcastActive=function(){if(this.hasActive()){var t=this.callbackCtx_,r=this.activeObservations_.map(function(n){return new kd(n.target,n.broadcastRect())});this.callback_.call(t,r,t),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),ic=typeof WeakMap<"u"?new WeakMap:new rc,ac=function(){function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var r=Td.getInstance(),n=new Hd(t,r,this);ic.set(this,n)}return e}();["observe","unobserve","disconnect"].forEach(function(e){ac.prototype[e]=function(){var t;return(t=ic.get(this))[e].apply(t,arguments)}});var Dd=function(){return typeof Bo.ResizeObserver<"u"?Bo.ResizeObserver:ac}(),Mr=new Map;function Vd(e){e.forEach(function(t){var r,n=t.target;(r=Mr.get(n))===null||r===void 0||r.forEach(function(o){return o(n)})})}var sc=new Dd(Vd);function Wd(e,t){Mr.has(e)||(Mr.set(e,new Set),sc.observe(e)),Mr.get(e).add(t)}function qd(e,t){Mr.has(e)&&(Mr.get(e).delete(t),Mr.get(e).size||(sc.unobserve(e),Mr.delete(e)))}function st(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function xs(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ld(n.key),n)}}function lt(e,t,r){return t&&xs(e.prototype,t),r&&xs(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Un(e,t){return Un=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,n){return r.__proto__=n,r},Un(e,t)}function yr(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Un(e,t)}function Kn(e){return Kn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Kn(e)}function Ha(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ha=function(){return!!e})()}function fe(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ud(e,t){if(t&&(ve(t)=="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return fe(e)}function Cr(e){var t=Ha();return function(){var r,n=Kn(e);if(t){var o=Kn(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return Ud(this,r)}}var Kd=function(e){yr(r,e);var t=Cr(r);function r(){return st(this,r),t.apply(this,arguments)}return lt(r,[{key:"render",value:function(){return this.props.children}}]),r}(c.Component);function Gd(e,t){var r=e.children,n=e.disabled,o=c.useRef(null),i=c.useRef(null),a=c.useContext(Ki),s=typeof r=="function",l=s?r(o):r,u=c.useRef({width:-1,height:-1,offsetWidth:-1,offsetHeight:-1}),d=!s&&c.isValidElement(l)&&ao(l),f=d?Yo(l):null,m=io(f,o),p=function(){var y;return Po(o.current)||(o.current&&ve(o.current)==="object"?Po((y=o.current)===null||y===void 0?void 0:y.nativeElement):null)||Po(i.current)};c.useImperativeHandle(t,function(){return p()});var g=c.useRef(e);g.current=e;var h=c.useCallback(function(v){var y=g.current,b=y.onResize,C=y.data,w=v.getBoundingClientRect(),S=w.width,x=w.height,E=v.offsetWidth,O=v.offsetHeight,I=Math.floor(S),R=Math.floor(x);if(u.current.width!==I||u.current.height!==R||u.current.offsetWidth!==E||u.current.offsetHeight!==O){var A={width:I,height:R,offsetWidth:E,offsetHeight:O};u.current=A;var M=E===Math.round(S)?S:E,T=O===Math.round(x)?x:O,_=P(P({},A),{},{offsetWidth:M,offsetHeight:T});a?.(_,v,C),b&&Promise.resolve().then(function(){b(_,v)})}},[]);return c.useEffect(function(){var v=p();return v&&!n&&Wd(v,h),function(){return qd(v,h)}},[o.current,n]),c.createElement(Kd,{ref:i},d?c.cloneElement(l,{ref:m}):l)}var Xd=c.forwardRef(Gd),Qd="rc-observer-key";function Zd(e,t){var r=e.children,n=typeof r=="function"?[r]:Wr(r);return n.map(function(o,i){var a=o?.key||"".concat(Qd,"-").concat(i);return c.createElement(Xd,oe({},e,{key:a,ref:i===0?t:void 0}),o)})}var so=c.forwardRef(Zd);so.Collection=$d;function Xi(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function Yd(e){if(Array.isArray(e))return Xi(e)}function lc(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Da(e,t){if(e){if(typeof e=="string")return Xi(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Xi(e,t):void 0}}function Jd(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function q(e){return Yd(e)||lc(e)||Da(e)||Jd()}var cc=function(t){return+setTimeout(t,16)},uc=function(t){return clearTimeout(t)};typeof window<"u"&&"requestAnimationFrame"in window&&(cc=function(t){return window.requestAnimationFrame(t)},uc=function(t){return window.cancelAnimationFrame(t)});var ws=0,Va=new Map;function dc(e){Va.delete(e)}var Tt=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;ws+=1;var n=ws;function o(i){if(i===0)dc(n),t();else{var a=cc(function(){o(i-1)});Va.set(n,a)}}return o(r),n};Tt.cancel=function(e){var t=Va.get(e);return dc(e),uc(t)};function fc(e){if(Array.isArray(e))return e}function ef(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,o,i,a,s=[],l=!0,u=!1;try{if(i=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(d){u=!0,o=d}finally{try{if(!l&&r.return!=null&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw o}}return s}}function vc(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function j(e,t){return fc(e)||ef(e,t)||Da(e,t)||vc()}function Gn(e){for(var t=0,r,n=0,o=e.length;o>=4;++n,o-=4)r=e.charCodeAt(n)&255|(e.charCodeAt(++n)&255)<<8|(e.charCodeAt(++n)&255)<<16|(e.charCodeAt(++n)&255)<<24,r=(r&65535)*1540483477+((r>>>16)*59797<<16),r^=r>>>24,t=(r&65535)*1540483477+((r>>>16)*59797<<16)^(t&65535)*1540483477+((t>>>16)*59797<<16);switch(o){case 3:t^=(e.charCodeAt(n+2)&255)<<16;case 2:t^=(e.charCodeAt(n+1)&255)<<8;case 1:t^=e.charCodeAt(n)&255,t=(t&65535)*1540483477+((t>>>16)*59797<<16)}return t^=t>>>13,t=(t&65535)*1540483477+((t>>>16)*59797<<16),((t^t>>>15)>>>0).toString(36)}function _t(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}function tf(e,t){if(!e)return!1;if(e.contains)return e.contains(t);for(var r=t;r;){if(r===e)return!0;r=r.parentNode}return!1}var $s="data-rc-order",Es="data-rc-priority",rf="rc-util-key",Qi=new Map;function mc(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=e.mark;return t?t.startsWith("data-")?t:"data-".concat(t):rf}function ei(e){if(e.attachTo)return e.attachTo;var t=document.querySelector("head");return t||document.body}function nf(e){return e==="queue"?"prependQueue":e?"prepend":"append"}function Wa(e){return Array.from((Qi.get(e)||e).children).filter(function(t){return t.tagName==="STYLE"})}function gc(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!_t())return null;var r=t.csp,n=t.prepend,o=t.priority,i=o===void 0?0:o,a=nf(n),s=a==="prependQueue",l=document.createElement("style");l.setAttribute($s,a),s&&i&&l.setAttribute(Es,"".concat(i)),r!=null&&r.nonce&&(l.nonce=r?.nonce),l.innerHTML=e;var u=ei(t),d=u.firstChild;if(n){if(s){var f=(t.styles||Wa(u)).filter(function(m){if(!["prepend","prependQueue"].includes(m.getAttribute($s)))return!1;var p=Number(m.getAttribute(Es)||0);return i>=p});if(f.length)return u.insertBefore(l,f[f.length-1].nextSibling),l}u.insertBefore(l,d)}else u.appendChild(l);return l}function hc(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=ei(t);return(t.styles||Wa(r)).find(function(n){return n.getAttribute(mc(t))===e})}function Xn(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=hc(e,t);if(r){var n=ei(t);n.removeChild(r)}}function of(e,t){var r=Qi.get(e);if(!r||!tf(document,r)){var n=gc("",t),o=n.parentNode;Qi.set(e,o),e.removeChild(n)}}function pr(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},n=ei(r),o=Wa(n),i=P(P({},r),{},{styles:o});of(n,i);var a=hc(t,i);if(a){var s,l;if((s=i.csp)!==null&&s!==void 0&&s.nonce&&a.nonce!==((l=i.csp)===null||l===void 0?void 0:l.nonce)){var u;a.nonce=(u=i.csp)===null||u===void 0?void 0:u.nonce}return a.innerHTML!==e&&(a.innerHTML=e),a}var d=gc(e,i);return d.setAttribute(mc(i),t),d}function af(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function ke(e,t){if(e==null)return{};var r,n,o=af(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function Qn(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,n=new Set;function o(i,a){var s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,l=n.has(i);if(St(!l,"Warning: There may be circular references"),l)return!1;if(i===a)return!0;if(r&&s>1)return!1;n.add(i);var u=s+1;if(Array.isArray(i)){if(!Array.isArray(a)||i.length!==a.length)return!1;for(var d=0;d<i.length;d++)if(!o(i[d],a[d],u))return!1;return!0}if(i&&a&&ve(i)==="object"&&ve(a)==="object"){var f=Object.keys(i);return f.length!==Object.keys(a).length?!1:f.every(function(m){return o(i[m],a[m],u)})}return!1}return o(e,t)}var sf="%";function Zi(e){return e.join(sf)}var lf=function(){function e(t){st(this,e),$(this,"instanceId",void 0),$(this,"cache",new Map),$(this,"extracted",new Set),this.instanceId=t}return lt(e,[{key:"get",value:function(r){return this.opGet(Zi(r))}},{key:"opGet",value:function(r){return this.cache.get(r)||null}},{key:"update",value:function(r,n){return this.opUpdate(Zi(r),n)}},{key:"opUpdate",value:function(r,n){var o=this.cache.get(r),i=n(o);i===null?this.cache.delete(r):this.cache.set(r,i)}}]),e}(),mn="data-token-hash",Ut="data-css-hash",Tr="__cssinjs_instance__";function cf(){var e=Math.random().toString(12).slice(2);if(typeof document<"u"&&document.head&&document.body){var t=document.body.querySelectorAll("style[".concat(Ut,"]"))||[],r=document.head.firstChild;Array.from(t).forEach(function(o){o[Tr]=o[Tr]||e,o[Tr]===e&&document.head.insertBefore(o,r)});var n={};Array.from(document.querySelectorAll("style[".concat(Ut,"]"))).forEach(function(o){var i=o.getAttribute(Ut);if(n[i]){if(o[Tr]===e){var a;(a=o.parentNode)===null||a===void 0||a.removeChild(o)}}else n[i]=!0})}return new lf(e)}var lo=c.createContext({hashPriority:"low",cache:cf(),defaultCache:!0});function uf(e,t){if(e.length!==t.length)return!1;for(var r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}var qa=function(){function e(){st(this,e),$(this,"cache",void 0),$(this,"keys",void 0),$(this,"cacheCallTimes",void 0),this.cache=new Map,this.keys=[],this.cacheCallTimes=0}return lt(e,[{key:"size",value:function(){return this.keys.length}},{key:"internalGet",value:function(r){var n,o,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,a={map:this.cache};return r.forEach(function(s){if(!a)a=void 0;else{var l;a=(l=a)===null||l===void 0||(l=l.map)===null||l===void 0?void 0:l.get(s)}}),(n=a)!==null&&n!==void 0&&n.value&&i&&(a.value[1]=this.cacheCallTimes++),(o=a)===null||o===void 0?void 0:o.value}},{key:"get",value:function(r){var n;return(n=this.internalGet(r,!0))===null||n===void 0?void 0:n[0]}},{key:"has",value:function(r){return!!this.internalGet(r)}},{key:"set",value:function(r,n){var o=this;if(!this.has(r)){if(this.size()+1>e.MAX_CACHE_SIZE+e.MAX_CACHE_OFFSET){var i=this.keys.reduce(function(u,d){var f=j(u,2),m=f[1];return o.internalGet(d)[1]<m?[d,o.internalGet(d)[1]]:u},[this.keys[0],this.cacheCallTimes]),a=j(i,1),s=a[0];this.delete(s)}this.keys.push(r)}var l=this.cache;r.forEach(function(u,d){if(d===r.length-1)l.set(u,{value:[n,o.cacheCallTimes++]});else{var f=l.get(u);f?f.map||(f.map=new Map):l.set(u,{map:new Map}),l=l.get(u).map}})}},{key:"deleteByPath",value:function(r,n){var o=r.get(n[0]);if(n.length===1){var i;return o.map?r.set(n[0],{map:o.map}):r.delete(n[0]),(i=o.value)===null||i===void 0?void 0:i[0]}var a=this.deleteByPath(o.map,n.slice(1));return(!o.map||o.map.size===0)&&!o.value&&r.delete(n[0]),a}},{key:"delete",value:function(r){if(this.has(r))return this.keys=this.keys.filter(function(n){return!uf(n,r)}),this.deleteByPath(this.cache,r)}}]),e}();$(qa,"MAX_CACHE_SIZE",20);$(qa,"MAX_CACHE_OFFSET",5);var Os=0,pc=function(){function e(t){st(this,e),$(this,"derivatives",void 0),$(this,"id",void 0),this.derivatives=Array.isArray(t)?t:[t],this.id=Os,t.length===0&&(t.length>0,void 0),Os+=1}return lt(e,[{key:"getDerivativeToken",value:function(r){return this.derivatives.reduce(function(n,o){return o(r,n)},void 0)}}]),e}(),$i=new qa;function ko(e){var t=Array.isArray(e)?e:[e];return $i.has(t)||$i.set(t,new pc(t)),$i.get(t)}var df=new WeakMap,Ei={};function ff(e,t){for(var r=df,n=0;n<t.length;n+=1){var o=t[n];r.has(o)||r.set(o,new WeakMap),r=r.get(o)}return r.has(Ei)||r.set(Ei,e()),r.get(Ei)}var Is=new WeakMap;function kn(e){var t=Is.get(e)||"";return t||(Object.keys(e).forEach(function(r){var n=e[r];t+=r,n instanceof pc?t+=n.id:n&&ve(n)==="object"?t+=kn(n):t+=n}),t=Gn(t),Is.set(e,t)),t}function Ps(e,t){return Gn("".concat(t,"_").concat(kn(e)))}var Yi=_t();function ie(e){return typeof e=="number"?"".concat(e,"px"):e}function Ho(e,t,r){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},o=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1;if(o)return e;var i=P(P({},n),{},$($({},mn,t),Ut,r)),a=Object.keys(i).map(function(s){var l=i[s];return l?"".concat(s,'="').concat(l,'"'):null}).filter(function(s){return s}).join(" ");return"<style ".concat(a,">").concat(e,"</style>")}var Ro=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";return"--".concat(r?"".concat(r,"-"):"").concat(t).replace(/([a-z0-9])([A-Z])/g,"$1-$2").replace(/([A-Z]+)([A-Z][a-z0-9]+)/g,"$1-$2").replace(/([a-z])([A-Z0-9])/g,"$1-$2").toLowerCase()},vf=function(t,r,n){return Object.keys(t).length?".".concat(r).concat(n!=null&&n.scope?".".concat(n.scope):"","{").concat(Object.entries(t).map(function(o){var i=j(o,2),a=i[0],s=i[1];return"".concat(a,":").concat(s,";")}).join(""),"}"):""},bc=function(t,r,n){var o={},i={};return Object.entries(t).forEach(function(a){var s,l,u=j(a,2),d=u[0],f=u[1];if(n!=null&&(s=n.preserve)!==null&&s!==void 0&&s[d])i[d]=f;else if((typeof f=="string"||typeof f=="number")&&!(n!=null&&(l=n.ignore)!==null&&l!==void 0&&l[d])){var m,p=Ro(d,n?.prefix);o[p]=typeof f=="number"&&!(n!=null&&(m=n.unitless)!==null&&m!==void 0&&m[d])?"".concat(f,"px"):String(f),i[d]="var(".concat(p,")")}}),[i,vf(o,r,{scope:n?.scope})]},Rs=_t()?c.useLayoutEffect:c.useEffect,yt=function(t,r){var n=c.useRef(!0);Rs(function(){return t(n.current)},r),Rs(function(){return n.current=!1,function(){n.current=!0}},[])},Ms=function(t,r){yt(function(n){if(!n)return t()},r)},mf=P({},Xo),Ts=mf.useInsertionEffect,gf=function(t,r,n){c.useMemo(t,n),yt(function(){return r(!0)},n)},hf=Ts?function(e,t,r){return Ts(function(){return e(),t()},r)}:gf,pf=P({},Xo),bf=pf.useInsertionEffect,yf=function(t){var r=[],n=!1;function o(i){n||r.push(i)}return c.useEffect(function(){return n=!1,function(){n=!0,r.length&&r.forEach(function(i){return i()})}},t),o},Cf=function(){return function(t){t()}},Sf=typeof bf<"u"?yf:Cf;function Ua(e,t,r,n,o){var i=c.useContext(lo),a=i.cache,s=[e].concat(q(t)),l=Zi(s),u=Sf([l]),d=function(g){a.opUpdate(l,function(h){var v=h||[void 0,void 0],y=j(v,2),b=y[0],C=b===void 0?0:b,w=y[1],S=w,x=S||r(),E=[C,x];return g?g(E):E})};c.useMemo(function(){d()},[l]);var f=a.opGet(l),m=f[1];return hf(function(){o?.(m)},function(p){return d(function(g){var h=j(g,2),v=h[0],y=h[1];return p&&v===0&&o?.(m),[v+1,y]}),function(){a.opUpdate(l,function(g){var h=g||[],v=j(h,2),y=v[0],b=y===void 0?0:y,C=v[1],w=b-1;return w===0?(u(function(){(p||!a.opGet(l))&&n?.(C,!1)}),null):[b-1,C]})}},[l]),m}var xf={},wf="css",Hr=new Map;function $f(e){Hr.set(e,(Hr.get(e)||0)+1)}function Ef(e,t){if(typeof document<"u"){var r=document.querySelectorAll("style[".concat(mn,'="').concat(e,'"]'));r.forEach(function(n){if(n[Tr]===t){var o;(o=n.parentNode)===null||o===void 0||o.removeChild(n)}})}}var Of=0;function If(e,t){Hr.set(e,(Hr.get(e)||0)-1);var r=new Set;Hr.forEach(function(n,o){n<=0&&r.add(o)}),Hr.size-r.size>Of&&r.forEach(function(n){Ef(n,t),Hr.delete(n)})}var yc=function(t,r,n,o){var i=n.getDerivativeToken(t),a=P(P({},i),r);return o&&(a=o(a)),a},Cc="token";function Pf(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},n=c.useContext(lo),o=n.cache.instanceId,i=n.container,a=r.salt,s=a===void 0?"":a,l=r.override,u=l===void 0?xf:l,d=r.formatToken,f=r.getComputedToken,m=r.cssVar,p=ff(function(){return Object.assign.apply(Object,[{}].concat(q(t)))},t),g=kn(p),h=kn(u),v=m?kn(m):"",y=Ua(Cc,[s,e.id,g,h,v],function(){var b,C=f?f(p,u,e):yc(p,u,e,d),w=P({},C),S="";if(m){var x=bc(C,m.key,{prefix:m.prefix,ignore:m.ignore,unitless:m.unitless,preserve:m.preserve}),E=j(x,2);C=E[0],S=E[1]}var O=Ps(C,s);C._tokenKey=O,w._tokenKey=Ps(w,s);var I=(b=m?.key)!==null&&b!==void 0?b:O;C._themeKey=I,$f(I);var R="".concat(wf,"-").concat(Gn(O));return C._hashId=R,[C,R,w,S,m?.key||""]},function(b){If(b[0]._themeKey,o)},function(b){var C=j(b,4),w=C[0],S=C[3];if(m&&S){var x=pr(S,Gn("css-variables-".concat(w._themeKey)),{mark:Ut,prepend:"queue",attachTo:i,priority:-999});x[Tr]=o,x.setAttribute(mn,w._themeKey)}});return y}var Rf=function(t,r,n){var o=j(t,5),i=o[2],a=o[3],s=o[4],l=n||{},u=l.plain;if(!a)return null;var d=i._tokenKey,f=-999,m={"data-rc-order":"prependQueue","data-rc-priority":"".concat(f)},p=Ho(a,s,d,m,u);return[f,d,p]},Mf={animationIterationCount:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},Sc="comm",xc="rule",wc="decl",Tf="@import",_f="@namespace",Af="@keyframes",Ff="@layer",$c=Math.abs,Ka=String.fromCharCode;function Ec(e){return e.trim()}function Mo(e,t,r){return e.replace(t,r)}function jf(e,t,r){return e.indexOf(t,r)}function dn(e,t){return e.charCodeAt(t)|0}function gn(e,t,r){return e.slice(t,r)}function or(e){return e.length}function Nf(e){return e.length}function Co(e,t){return t.push(e),e}var ti=1,hn=1,Oc=0,Dt=0,tt=0,Cn="";function Ga(e,t,r,n,o,i,a,s){return{value:e,root:t,parent:r,type:n,props:o,children:i,line:ti,column:hn,length:a,return:"",siblings:s}}function Lf(){return tt}function Bf(){return tt=Dt>0?dn(Cn,--Dt):0,hn--,tt===10&&(hn=1,ti--),tt}function Kt(){return tt=Dt<Oc?dn(Cn,Dt++):0,hn++,tt===10&&(hn=1,ti++),tt}function _r(){return dn(Cn,Dt)}function To(){return Dt}function ri(e,t){return gn(Cn,e,t)}function Zn(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function zf(e){return ti=hn=1,Oc=or(Cn=e),Dt=0,[]}function kf(e){return Cn="",e}function Oi(e){return Ec(ri(Dt-1,Ji(e===91?e+2:e===40?e+1:e)))}function Hf(e){for(;(tt=_r())&&tt<33;)Kt();return Zn(e)>2||Zn(tt)>3?"":" "}function Df(e,t){for(;--t&&Kt()&&!(tt<48||tt>102||tt>57&&tt<65||tt>70&&tt<97););return ri(e,To()+(t<6&&_r()==32&&Kt()==32))}function Ji(e){for(;Kt();)switch(tt){case e:return Dt;case 34:case 39:e!==34&&e!==39&&Ji(tt);break;case 40:e===41&&Ji(e);break;case 92:Kt();break}return Dt}function Vf(e,t){for(;Kt()&&e+tt!==57;)if(e+tt===84&&_r()===47)break;return"/*"+ri(t,Dt-1)+"*"+Ka(e===47?e:Kt())}function Wf(e){for(;!Zn(_r());)Kt();return ri(e,Dt)}function qf(e){return kf(_o("",null,null,null,[""],e=zf(e),0,[0],e))}function _o(e,t,r,n,o,i,a,s,l){for(var u=0,d=0,f=a,m=0,p=0,g=0,h=1,v=1,y=1,b=0,C="",w=o,S=i,x=n,E=C;v;)switch(g=b,b=Kt()){case 40:if(g!=108&&dn(E,f-1)==58){jf(E+=Mo(Oi(b),"&","&\f"),"&\f",$c(u?s[u-1]:0))!=-1&&(y=-1);break}case 34:case 39:case 91:E+=Oi(b);break;case 9:case 10:case 13:case 32:E+=Hf(g);break;case 92:E+=Df(To()-1,7);continue;case 47:switch(_r()){case 42:case 47:Co(Uf(Vf(Kt(),To()),t,r,l),l),(Zn(g||1)==5||Zn(_r()||1)==5)&&or(E)&&gn(E,-1,void 0)!==" "&&(E+=" ");break;default:E+="/"}break;case 123*h:s[u++]=or(E)*y;case 125*h:case 59:case 0:switch(b){case 0:case 125:v=0;case 59+d:y==-1&&(E=Mo(E,/\f/g,"")),p>0&&(or(E)-f||h===0&&g===47)&&Co(p>32?As(E+";",n,r,f-1,l):As(Mo(E," ","")+";",n,r,f-2,l),l);break;case 59:E+=";";default:if(Co(x=_s(E,t,r,u,d,o,s,C,w=[],S=[],f,i),i),b===123)if(d===0)_o(E,t,x,x,w,i,f,s,S);else{switch(m){case 99:if(dn(E,3)===110)break;case 108:if(dn(E,2)===97)break;default:d=0;case 100:case 109:case 115:}d?_o(e,x,x,n&&Co(_s(e,x,x,0,0,o,s,C,o,w=[],f,S),S),o,S,f,s,n?w:S):_o(E,x,x,x,[""],S,0,s,S)}}u=d=p=0,h=y=1,C=E="",f=a;break;case 58:f=1+or(E),p=g;default:if(h<1){if(b==123)--h;else if(b==125&&h++==0&&Bf()==125)continue}switch(E+=Ka(b),b*h){case 38:y=d>0?1:(E+="\f",-1);break;case 44:s[u++]=(or(E)-1)*y,y=1;break;case 64:_r()===45&&(E+=Oi(Kt())),m=_r(),d=f=or(C=E+=Wf(To())),b++;break;case 45:g===45&&or(E)==2&&(h=0)}}return i}function _s(e,t,r,n,o,i,a,s,l,u,d,f){for(var m=o-1,p=o===0?i:[""],g=Nf(p),h=0,v=0,y=0;h<n;++h)for(var b=0,C=gn(e,m+1,m=$c(v=a[h])),w=e;b<g;++b)(w=Ec(v>0?p[b]+" "+C:Mo(C,/&\f/g,p[b])))&&(l[y++]=w);return Ga(e,t,r,o===0?xc:s,l,u,d,f)}function Uf(e,t,r,n){return Ga(e,t,r,Sc,Ka(Lf()),gn(e,2,-2),0,n)}function As(e,t,r,n,o){return Ga(e,t,r,wc,gn(e,0,n),gn(e,n+1,-1),n,o)}function ea(e,t){for(var r="",n=0;n<e.length;n++)r+=t(e[n],n,e,t)||"";return r}function Kf(e,t,r,n){switch(e.type){case Ff:if(e.children.length)break;case Tf:case _f:case wc:return e.return=e.return||e.value;case Sc:return"";case Af:return e.return=e.value+"{"+ea(e.children,n)+"}";case xc:if(!or(e.value=e.props.join(",")))return""}return or(r=ea(e.children,n))?e.return=e.value+"{"+r+"}":""}var Fs="data-ant-cssinjs-cache-path",Ic="_FILE_STYLE__",Vr,Pc=!0;function Gf(){if(!Vr&&(Vr={},_t())){var e=document.createElement("div");e.className=Fs,e.style.position="fixed",e.style.visibility="hidden",e.style.top="-9999px",document.body.appendChild(e);var t=getComputedStyle(e).content||"";t=t.replace(/^"/,"").replace(/"$/,""),t.split(";").forEach(function(o){var i=o.split(":"),a=j(i,2),s=a[0],l=a[1];Vr[s]=l});var r=document.querySelector("style[".concat(Fs,"]"));if(r){var n;Pc=!1,(n=r.parentNode)===null||n===void 0||n.removeChild(r)}document.body.removeChild(e)}}function Xf(e){return Gf(),!!Vr[e]}function Qf(e){var t=Vr[e],r=null;if(t&&_t())if(Pc)r=Ic;else{var n=document.querySelector("style[".concat(Ut,'="').concat(Vr[e],'"]'));n?r=n.innerHTML:delete Vr[e]}return[r,t]}var Zf="_skip_check_",Rc="_multi_value_";function Ao(e){var t=ea(qf(e),Kf);return t.replace(/\{%%%\:[^;];}/g,";")}function Yf(e){return ve(e)==="object"&&e&&(Zf in e||Rc in e)}function js(e,t,r){if(!t)return e;var n=".".concat(t),o=r==="low"?":where(".concat(n,")"):n,i=e.split(",").map(function(a){var s,l=a.trim().split(/\s+/),u=l[0]||"",d=((s=u.match(/^\w+/))===null||s===void 0?void 0:s[0])||"";return u="".concat(d).concat(o).concat(u.slice(d.length)),[u].concat(q(l.slice(1))).join(" ")});return i.join(",")}var Jf=function e(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{root:!0,parentSelectors:[]},o=n.root,i=n.injectHash,a=n.parentSelectors,s=r.hashId,l=r.layer;r.path;var u=r.hashPriority,d=r.transformers,f=d===void 0?[]:d;r.linters;var m="",p={};function g(y){var b=y.getName(s);if(!p[b]){var C=e(y.style,r,{root:!1,parentSelectors:a}),w=j(C,1),S=w[0];p[b]="@keyframes ".concat(y.getName(s)).concat(S)}}function h(y){var b=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];return y.forEach(function(C){Array.isArray(C)?h(C,b):C&&b.push(C)}),b}var v=h(Array.isArray(t)?t:[t]);return v.forEach(function(y){var b=typeof y=="string"&&!o?{}:y;if(typeof b=="string")m+="".concat(b,`
`);else if(b._keyframe)g(b);else{var C=f.reduce(function(w,S){var x;return(S==null||(x=S.visit)===null||x===void 0?void 0:x.call(S,w))||w},b);Object.keys(C).forEach(function(w){var S=C[w];if(ve(S)==="object"&&S&&(w!=="animationName"||!S._keyframe)&&!Yf(S)){var x=!1,E=w.trim(),O=!1;(o||i)&&s?E.startsWith("@")?x=!0:E==="&"?E=js("",s,u):E=js(w,s,u):o&&!s&&(E==="&"||E==="")&&(E="",O=!0);var I=e(S,r,{root:O,injectHash:x,parentSelectors:[].concat(q(a),[E])}),R=j(I,2),A=R[0],M=R[1];p=P(P({},p),M),m+="".concat(E).concat(A)}else{let N=function(F,L){var B=F.replace(/[A-Z]/g,function(K){return"-".concat(K.toLowerCase())}),V=L;!Mf[F]&&typeof V=="number"&&V!==0&&(V="".concat(V,"px")),F==="animationName"&&L!==null&&L!==void 0&&L._keyframe&&(g(L),V=L.getName(s)),m+="".concat(B,":").concat(V,";")};var T,_=(T=S?.value)!==null&&T!==void 0?T:S;ve(S)==="object"&&S!==null&&S!==void 0&&S[Rc]&&Array.isArray(_)?_.forEach(function(F){N(w,F)}):N(w,_)}})}}),o?l&&(m&&(m="@layer ".concat(l.name," {").concat(m,"}")),l.dependencies&&(p["@layer ".concat(l.name)]=l.dependencies.map(function(y){return"@layer ".concat(y,", ").concat(l.name,";")}).join(`
`))):m="{".concat(m,"}"),[m,p]};function Mc(e,t){return Gn("".concat(e.join("%")).concat(t))}function ev(){return null}var Tc="style";function ta(e,t){var r=e.token,n=e.path,o=e.hashId,i=e.layer,a=e.nonce,s=e.clientOnly,l=e.order,u=l===void 0?0:l,d=c.useContext(lo),f=d.autoClear;d.mock;var m=d.defaultCache,p=d.hashPriority,g=d.container,h=d.ssrInline,v=d.transformers,y=d.linters,b=d.cache,C=d.layer,w=r._tokenKey,S=[w];C&&S.push("layer"),S.push.apply(S,q(n));var x=Yi,E=Ua(Tc,S,function(){var M=S.join("|");if(Xf(M)){var T=Qf(M),_=j(T,2),N=_[0],F=_[1];if(N)return[N,w,F,{},s,u]}var L=t(),B=Jf(L,{hashId:o,hashPriority:p,layer:C?i:void 0,path:n.join("-"),transformers:v,linters:y}),V=j(B,2),K=V[0],U=V[1],z=Ao(K),X=Mc(S,z);return[z,w,X,U,s,u]},function(M,T){var _=j(M,3),N=_[2];(T||f)&&Yi&&Xn(N,{mark:Ut,attachTo:g})},function(M){var T=j(M,4),_=T[0];T[1];var N=T[2],F=T[3];if(x&&_!==Ic){var L={mark:Ut,prepend:C?!1:"queue",attachTo:g,priority:u},B=typeof a=="function"?a():a;B&&(L.csp={nonce:B});var V=[],K=[];Object.keys(F).forEach(function(z){z.startsWith("@layer")?V.push(z):K.push(z)}),V.forEach(function(z){pr(Ao(F[z]),"_layer-".concat(z),P(P({},L),{},{prepend:!0}))});var U=pr(_,N,L);U[Tr]=b.instanceId,U.setAttribute(mn,w),K.forEach(function(z){pr(Ao(F[z]),"_effect-".concat(z),L)})}}),O=j(E,3),I=O[0],R=O[1],A=O[2];return function(M){var T;return!h||x||!m?T=c.createElement(ev,null):T=c.createElement("style",oe({},$($({},mn,R),Ut,A),{dangerouslySetInnerHTML:{__html:I}})),c.createElement(c.Fragment,null,T,M)}}var tv=function(t,r,n){var o=j(t,6),i=o[0],a=o[1],s=o[2],l=o[3],u=o[4],d=o[5],f=n||{},m=f.plain;if(u)return null;var p=i,g={"data-rc-order":"prependQueue","data-rc-priority":"".concat(d)};return p=Ho(i,a,s,g,m),l&&Object.keys(l).forEach(function(h){if(!r[h]){r[h]=!0;var v=Ao(l[h]),y=Ho(v,a,"_effect-".concat(h),g,m);h.startsWith("@layer")?p=y+p:p+=y}}),[d,s,p]},_c="cssVar",rv=function(t,r){var n=t.key,o=t.prefix,i=t.unitless,a=t.ignore,s=t.token,l=t.scope,u=l===void 0?"":l,d=c.useContext(lo),f=d.cache.instanceId,m=d.container,p=s._tokenKey,g=[].concat(q(t.path),[n,u,p]),h=Ua(_c,g,function(){var v=r(),y=bc(v,n,{prefix:o,unitless:i,ignore:a,scope:u}),b=j(y,2),C=b[0],w=b[1],S=Mc(g,w);return[C,w,S,n]},function(v){var y=j(v,3),b=y[2];Yi&&Xn(b,{mark:Ut,attachTo:m})},function(v){var y=j(v,3),b=y[1],C=y[2];if(b){var w=pr(b,C,{mark:Ut,prepend:"queue",attachTo:m,priority:-999});w[Tr]=f,w.setAttribute(mn,n)}});return h},nv=function(t,r,n){var o=j(t,4),i=o[1],a=o[2],s=o[3],l=n||{},u=l.plain;if(!i)return null;var d=-999,f={"data-rc-order":"prependQueue","data-rc-priority":"".concat(d)},m=Ho(i,s,a,f,u);return[d,a,m]};$($($({},Tc,tv),Cc,Rf),_c,nv);var rt=function(){function e(t,r){st(this,e),$(this,"name",void 0),$(this,"style",void 0),$(this,"_keyframe",!0),this.name=t,this.style=r}return lt(e,[{key:"getName",value:function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return r?"".concat(r,"-").concat(this.name):this.name}}]),e}();function Jr(e){return e.notSplit=!0,e}Jr(["borderTop","borderBottom"]),Jr(["borderTop"]),Jr(["borderBottom"]),Jr(["borderLeft","borderRight"]),Jr(["borderLeft"]),Jr(["borderRight"]);var Xa=c.createContext({});function ov(e){return fc(e)||lc(e)||Da(e)||vc()}function ir(e,t){for(var r=e,n=0;n<t.length;n+=1){if(r==null)return;r=r[t[n]]}return r}function Ac(e,t,r,n){if(!t.length)return r;var o=ov(t),i=o[0],a=o.slice(1),s;return!e&&typeof i=="number"?s=[]:Array.isArray(e)?s=q(e):s=P({},e),n&&r===void 0&&a.length===1?delete s[i][a[0]]:s[i]=Ac(s[i],a,r,n),s}function Wt(e,t,r){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;return t.length&&n&&r===void 0&&!ir(e,t.slice(0,-1))?e:Ac(e,t,r,n)}function iv(e){return ve(e)==="object"&&e!==null&&Object.getPrototypeOf(e)===Object.prototype}function Ns(e){return Array.isArray(e)?[]:{}}var av=typeof Reflect>"u"?Object.keys:Reflect.ownKeys;function ln(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=Ns(t[0]);return t.forEach(function(o){function i(a,s){var l=new Set(s),u=ir(o,a),d=Array.isArray(u);if(d||iv(u)){if(!l.has(u)){l.add(u);var f=ir(n,a);d?n=Wt(n,a,[]):(!f||ve(f)!=="object")&&(n=Wt(n,a,Ns(u))),av(u).forEach(function(m){i([].concat(q(a),[m]),l)})}}else n=Wt(n,a,u)}i([])}),n}function sv(){}const lv=c.createContext({}),Fc=()=>{const e=()=>{};return e.deprecated=sv,e},cv=c.createContext(void 0),Pt="${label} is not a valid ${type}",ni={Modal:{okText:"OK",cancelText:"Cancel",justOkText:"OK"},Form:{optional:"(optional)",defaultValidateMessages:{default:"Field validation error for ${label}",required:"Please enter ${label}",enum:"${label} must be one of [${enum}]",whitespace:"${label} cannot be a blank character",date:{format:"${label} date format is invalid",parse:"${label} cannot be converted to a date",invalid:"${label} is an invalid date"},types:{string:Pt,method:Pt,array:Pt,object:Pt,number:Pt,date:Pt,boolean:Pt,integer:Pt,float:Pt,regexp:Pt,email:Pt,url:Pt,hex:Pt},string:{len:"${label} must be ${len} characters",min:"${label} must be at least ${min} characters",max:"${label} must be up to ${max} characters",range:"${label} must be between ${min}-${max} characters"},number:{len:"${label} must be equal to ${len}",min:"${label} must be minimum ${min}",max:"${label} must be maximum ${max}",range:"${label} must be between ${min}-${max}"},array:{len:"Must be ${len} ${label}",min:"At least ${min} ${label}",max:"At most ${max} ${label}",range:"The amount of ${label} must be between ${min}-${max}"},pattern:{mismatch:"${label} does not match the pattern ${pattern}"}}}};Object.assign({},ni.Modal);let Fo=[];const Ls=()=>Fo.reduce((e,t)=>Object.assign(Object.assign({},e),t),ni.Modal);function uv(e){if(e){const t=Object.assign({},e);return Fo.push(t),Ls(),()=>{Fo=Fo.filter(r=>r!==t),Ls()}}Object.assign({},ni.Modal)}const jc=c.createContext(void 0),dv="internalMark",fv=e=>{const{locale:t={},children:r,_ANT_MARK__:n}=e;c.useEffect(()=>uv(t?.Modal),[t]);const o=c.useMemo(()=>Object.assign(Object.assign({},t),{exist:!0}),[t]);return c.createElement(jc.Provider,{value:o},r)},Qa={blue:"#1677FF",purple:"#722ED1",cyan:"#13C2C2",green:"#52C41A",magenta:"#EB2F96",pink:"#EB2F96",red:"#F5222D",orange:"#FA8C16",yellow:"#FADB14",volcano:"#FA541C",geekblue:"#2F54EB",gold:"#FAAD14",lime:"#A0D911"},pn=Object.assign(Object.assign({},Qa),{colorPrimary:"#1677ff",colorSuccess:"#52c41a",colorWarning:"#faad14",colorError:"#ff4d4f",colorInfo:"#1677ff",colorLink:"",colorTextBase:"",colorBgBase:"",fontFamily:`-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
'Noto Color Emoji'`,fontFamilyCode:"'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace",fontSize:14,lineWidth:1,lineType:"solid",motionUnit:.1,motionBase:0,motionEaseOutCirc:"cubic-bezier(0.08, 0.82, 0.17, 1)",motionEaseInOutCirc:"cubic-bezier(0.78, 0.14, 0.15, 0.86)",motionEaseOut:"cubic-bezier(0.215, 0.61, 0.355, 1)",motionEaseInOut:"cubic-bezier(0.645, 0.045, 0.355, 1)",motionEaseOutBack:"cubic-bezier(0.12, 0.4, 0.29, 1.46)",motionEaseInBack:"cubic-bezier(0.71, -0.46, 0.88, 0.6)",motionEaseInQuint:"cubic-bezier(0.755, 0.05, 0.855, 0.06)",motionEaseOutQuint:"cubic-bezier(0.23, 1, 0.32, 1)",borderRadius:6,sizeUnit:4,sizeStep:4,sizePopupArrow:16,controlHeight:32,zIndexBase:0,zIndexPopupBase:1e3,opacityImage:1,wireframe:!1,motion:!0}),dt=Math.round;function Ii(e,t){const r=e.replace(/^[^(]*\((.*)/,"$1").replace(/\).*/,"").match(/\d*\.?\d+%?/g)||[],n=r.map(o=>parseFloat(o));for(let o=0;o<3;o+=1)n[o]=t(n[o]||0,r[o]||"",o);return r[3]?n[3]=r[3].includes("%")?n[3]/100:n[3]:n[3]=1,n}const Bs=(e,t,r)=>r===0?e:e/100;function Tn(e,t){const r=t||255;return e>r?r:e<0?0:e}class Ve{constructor(t){$(this,"isValid",!0),$(this,"r",0),$(this,"g",0),$(this,"b",0),$(this,"a",1),$(this,"_h",void 0),$(this,"_s",void 0),$(this,"_l",void 0),$(this,"_v",void 0),$(this,"_max",void 0),$(this,"_min",void 0),$(this,"_brightness",void 0);function r(n){return n[0]in t&&n[1]in t&&n[2]in t}if(t)if(typeof t=="string"){let o=function(i){return n.startsWith(i)};const n=t.trim();/^#?[A-F\d]{3,8}$/i.test(n)?this.fromHexString(n):o("rgb")?this.fromRgbString(n):o("hsl")?this.fromHslString(n):(o("hsv")||o("hsb"))&&this.fromHsvString(n)}else if(t instanceof Ve)this.r=t.r,this.g=t.g,this.b=t.b,this.a=t.a,this._h=t._h,this._s=t._s,this._l=t._l,this._v=t._v;else if(r("rgb"))this.r=Tn(t.r),this.g=Tn(t.g),this.b=Tn(t.b),this.a=typeof t.a=="number"?Tn(t.a,1):1;else if(r("hsl"))this.fromHsl(t);else if(r("hsv"))this.fromHsv(t);else throw new Error("@ant-design/fast-color: unsupported input "+JSON.stringify(t))}setR(t){return this._sc("r",t)}setG(t){return this._sc("g",t)}setB(t){return this._sc("b",t)}setA(t){return this._sc("a",t,1)}setHue(t){const r=this.toHsv();return r.h=t,this._c(r)}getLuminance(){function t(i){const a=i/255;return a<=.03928?a/12.92:Math.pow((a+.055)/1.055,2.4)}const r=t(this.r),n=t(this.g),o=t(this.b);return .2126*r+.7152*n+.0722*o}getHue(){if(typeof this._h>"u"){const t=this.getMax()-this.getMin();t===0?this._h=0:this._h=dt(60*(this.r===this.getMax()?(this.g-this.b)/t+(this.g<this.b?6:0):this.g===this.getMax()?(this.b-this.r)/t+2:(this.r-this.g)/t+4))}return this._h}getSaturation(){if(typeof this._s>"u"){const t=this.getMax()-this.getMin();t===0?this._s=0:this._s=t/this.getMax()}return this._s}getLightness(){return typeof this._l>"u"&&(this._l=(this.getMax()+this.getMin())/510),this._l}getValue(){return typeof this._v>"u"&&(this._v=this.getMax()/255),this._v}getBrightness(){return typeof this._brightness>"u"&&(this._brightness=(this.r*299+this.g*587+this.b*114)/1e3),this._brightness}darken(t=10){const r=this.getHue(),n=this.getSaturation();let o=this.getLightness()-t/100;return o<0&&(o=0),this._c({h:r,s:n,l:o,a:this.a})}lighten(t=10){const r=this.getHue(),n=this.getSaturation();let o=this.getLightness()+t/100;return o>1&&(o=1),this._c({h:r,s:n,l:o,a:this.a})}mix(t,r=50){const n=this._c(t),o=r/100,i=s=>(n[s]-this[s])*o+this[s],a={r:dt(i("r")),g:dt(i("g")),b:dt(i("b")),a:dt(i("a")*100)/100};return this._c(a)}tint(t=10){return this.mix({r:255,g:255,b:255,a:1},t)}shade(t=10){return this.mix({r:0,g:0,b:0,a:1},t)}onBackground(t){const r=this._c(t),n=this.a+r.a*(1-this.a),o=i=>dt((this[i]*this.a+r[i]*r.a*(1-this.a))/n);return this._c({r:o("r"),g:o("g"),b:o("b"),a:n})}isDark(){return this.getBrightness()<128}isLight(){return this.getBrightness()>=128}equals(t){return this.r===t.r&&this.g===t.g&&this.b===t.b&&this.a===t.a}clone(){return this._c(this)}toHexString(){let t="#";const r=(this.r||0).toString(16);t+=r.length===2?r:"0"+r;const n=(this.g||0).toString(16);t+=n.length===2?n:"0"+n;const o=(this.b||0).toString(16);if(t+=o.length===2?o:"0"+o,typeof this.a=="number"&&this.a>=0&&this.a<1){const i=dt(this.a*255).toString(16);t+=i.length===2?i:"0"+i}return t}toHsl(){return{h:this.getHue(),s:this.getSaturation(),l:this.getLightness(),a:this.a}}toHslString(){const t=this.getHue(),r=dt(this.getSaturation()*100),n=dt(this.getLightness()*100);return this.a!==1?`hsla(${t},${r}%,${n}%,${this.a})`:`hsl(${t},${r}%,${n}%)`}toHsv(){return{h:this.getHue(),s:this.getSaturation(),v:this.getValue(),a:this.a}}toRgb(){return{r:this.r,g:this.g,b:this.b,a:this.a}}toRgbString(){return this.a!==1?`rgba(${this.r},${this.g},${this.b},${this.a})`:`rgb(${this.r},${this.g},${this.b})`}toString(){return this.toRgbString()}_sc(t,r,n){const o=this.clone();return o[t]=Tn(r,n),o}_c(t){return new this.constructor(t)}getMax(){return typeof this._max>"u"&&(this._max=Math.max(this.r,this.g,this.b)),this._max}getMin(){return typeof this._min>"u"&&(this._min=Math.min(this.r,this.g,this.b)),this._min}fromHexString(t){const r=t.replace("#","");function n(o,i){return parseInt(r[o]+r[i||o],16)}r.length<6?(this.r=n(0),this.g=n(1),this.b=n(2),this.a=r[3]?n(3)/255:1):(this.r=n(0,1),this.g=n(2,3),this.b=n(4,5),this.a=r[6]?n(6,7)/255:1)}fromHsl({h:t,s:r,l:n,a:o}){if(this._h=t%360,this._s=r,this._l=n,this.a=typeof o=="number"?o:1,r<=0){const m=dt(n*255);this.r=m,this.g=m,this.b=m}let i=0,a=0,s=0;const l=t/60,u=(1-Math.abs(2*n-1))*r,d=u*(1-Math.abs(l%2-1));l>=0&&l<1?(i=u,a=d):l>=1&&l<2?(i=d,a=u):l>=2&&l<3?(a=u,s=d):l>=3&&l<4?(a=d,s=u):l>=4&&l<5?(i=d,s=u):l>=5&&l<6&&(i=u,s=d);const f=n-u/2;this.r=dt((i+f)*255),this.g=dt((a+f)*255),this.b=dt((s+f)*255)}fromHsv({h:t,s:r,v:n,a:o}){this._h=t%360,this._s=r,this._v=n,this.a=typeof o=="number"?o:1;const i=dt(n*255);if(this.r=i,this.g=i,this.b=i,r<=0)return;const a=t/60,s=Math.floor(a),l=a-s,u=dt(n*(1-r)*255),d=dt(n*(1-r*l)*255),f=dt(n*(1-r*(1-l))*255);switch(s){case 0:this.g=f,this.b=u;break;case 1:this.r=d,this.b=u;break;case 2:this.r=u,this.b=f;break;case 3:this.r=u,this.g=d;break;case 4:this.r=f,this.g=u;break;case 5:default:this.g=u,this.b=d;break}}fromHsvString(t){const r=Ii(t,Bs);this.fromHsv({h:r[0],s:r[1],v:r[2],a:r[3]})}fromHslString(t){const r=Ii(t,Bs);this.fromHsl({h:r[0],s:r[1],l:r[2],a:r[3]})}fromRgbString(t){const r=Ii(t,(n,o)=>o.includes("%")?dt(n/100*255):n);this.r=r[0],this.g=r[1],this.b=r[2],this.a=r[3]}}var So=2,zs=.16,vv=.05,mv=.05,gv=.15,Nc=5,Lc=4,hv=[{index:7,amount:15},{index:6,amount:25},{index:5,amount:30},{index:5,amount:45},{index:5,amount:65},{index:5,amount:85},{index:4,amount:90},{index:3,amount:95},{index:2,amount:97},{index:1,amount:98}];function ks(e,t,r){var n;return Math.round(e.h)>=60&&Math.round(e.h)<=240?n=r?Math.round(e.h)-So*t:Math.round(e.h)+So*t:n=r?Math.round(e.h)+So*t:Math.round(e.h)-So*t,n<0?n+=360:n>=360&&(n-=360),n}function Hs(e,t,r){if(e.h===0&&e.s===0)return e.s;var n;return r?n=e.s-zs*t:t===Lc?n=e.s+zs:n=e.s+vv*t,n>1&&(n=1),r&&t===Nc&&n>.1&&(n=.1),n<.06&&(n=.06),Math.round(n*100)/100}function Ds(e,t,r){var n;return r?n=e.v+mv*t:n=e.v-gv*t,n=Math.max(0,Math.min(1,n)),Math.round(n*100)/100}function qr(e){for(var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=[],n=new Ve(e),o=n.toHsv(),i=Nc;i>0;i-=1){var a=new Ve({h:ks(o,i,!0),s:Hs(o,i,!0),v:Ds(o,i,!0)});r.push(a)}r.push(n);for(var s=1;s<=Lc;s+=1){var l=new Ve({h:ks(o,s),s:Hs(o,s),v:Ds(o,s)});r.push(l)}return t.theme==="dark"?hv.map(function(u){var d=u.index,f=u.amount;return new Ve(t.backgroundColor||"#141414").mix(r[d],f).toHexString()}):r.map(function(u){return u.toHexString()})}var Pi={red:"#F5222D",volcano:"#FA541C",orange:"#FA8C16",gold:"#FAAD14",yellow:"#FADB14",lime:"#A0D911",green:"#52C41A",cyan:"#13C2C2",blue:"#1677FF",geekblue:"#2F54EB",purple:"#722ED1",magenta:"#EB2F96",grey:"#666666"},ra=["#fff1f0","#ffccc7","#ffa39e","#ff7875","#ff4d4f","#f5222d","#cf1322","#a8071a","#820014","#5c0011"];ra.primary=ra[5];var na=["#fff2e8","#ffd8bf","#ffbb96","#ff9c6e","#ff7a45","#fa541c","#d4380d","#ad2102","#871400","#610b00"];na.primary=na[5];var oa=["#fff7e6","#ffe7ba","#ffd591","#ffc069","#ffa940","#fa8c16","#d46b08","#ad4e00","#873800","#612500"];oa.primary=oa[5];var ia=["#fffbe6","#fff1b8","#ffe58f","#ffd666","#ffc53d","#faad14","#d48806","#ad6800","#874d00","#613400"];ia.primary=ia[5];var aa=["#feffe6","#ffffb8","#fffb8f","#fff566","#ffec3d","#fadb14","#d4b106","#ad8b00","#876800","#614700"];aa.primary=aa[5];var sa=["#fcffe6","#f4ffb8","#eaff8f","#d3f261","#bae637","#a0d911","#7cb305","#5b8c00","#3f6600","#254000"];sa.primary=sa[5];var la=["#f6ffed","#d9f7be","#b7eb8f","#95de64","#73d13d","#52c41a","#389e0d","#237804","#135200","#092b00"];la.primary=la[5];var ca=["#e6fffb","#b5f5ec","#87e8de","#5cdbd3","#36cfc9","#13c2c2","#08979c","#006d75","#00474f","#002329"];ca.primary=ca[5];var Do=["#e6f4ff","#bae0ff","#91caff","#69b1ff","#4096ff","#1677ff","#0958d9","#003eb3","#002c8c","#001d66"];Do.primary=Do[5];var ua=["#f0f5ff","#d6e4ff","#adc6ff","#85a5ff","#597ef7","#2f54eb","#1d39c4","#10239e","#061178","#030852"];ua.primary=ua[5];var da=["#f9f0ff","#efdbff","#d3adf7","#b37feb","#9254de","#722ed1","#531dab","#391085","#22075e","#120338"];da.primary=da[5];var fa=["#fff0f6","#ffd6e7","#ffadd2","#ff85c0","#f759ab","#eb2f96","#c41d7f","#9e1068","#780650","#520339"];fa.primary=fa[5];var va=["#a6a6a6","#999999","#8c8c8c","#808080","#737373","#666666","#404040","#1a1a1a","#000000","#000000"];va.primary=va[5];var Ri={red:ra,volcano:na,orange:oa,gold:ia,yellow:aa,lime:sa,green:la,cyan:ca,blue:Do,geekblue:ua,purple:da,magenta:fa,grey:va};function Bc(e,{generateColorPalettes:t,generateNeutralColorPalettes:r}){const{colorSuccess:n,colorWarning:o,colorError:i,colorInfo:a,colorPrimary:s,colorBgBase:l,colorTextBase:u}=e,d=t(s),f=t(n),m=t(o),p=t(i),g=t(a),h=r(l,u),v=e.colorLink||e.colorInfo,y=t(v),b=new Ve(p[1]).mix(new Ve(p[3]),50).toHexString();return Object.assign(Object.assign({},h),{colorPrimaryBg:d[1],colorPrimaryBgHover:d[2],colorPrimaryBorder:d[3],colorPrimaryBorderHover:d[4],colorPrimaryHover:d[5],colorPrimary:d[6],colorPrimaryActive:d[7],colorPrimaryTextHover:d[8],colorPrimaryText:d[9],colorPrimaryTextActive:d[10],colorSuccessBg:f[1],colorSuccessBgHover:f[2],colorSuccessBorder:f[3],colorSuccessBorderHover:f[4],colorSuccessHover:f[4],colorSuccess:f[6],colorSuccessActive:f[7],colorSuccessTextHover:f[8],colorSuccessText:f[9],colorSuccessTextActive:f[10],colorErrorBg:p[1],colorErrorBgHover:p[2],colorErrorBgFilledHover:b,colorErrorBgActive:p[3],colorErrorBorder:p[3],colorErrorBorderHover:p[4],colorErrorHover:p[5],colorError:p[6],colorErrorActive:p[7],colorErrorTextHover:p[8],colorErrorText:p[9],colorErrorTextActive:p[10],colorWarningBg:m[1],colorWarningBgHover:m[2],colorWarningBorder:m[3],colorWarningBorderHover:m[4],colorWarningHover:m[4],colorWarning:m[6],colorWarningActive:m[7],colorWarningTextHover:m[8],colorWarningText:m[9],colorWarningTextActive:m[10],colorInfoBg:g[1],colorInfoBgHover:g[2],colorInfoBorder:g[3],colorInfoBorderHover:g[4],colorInfoHover:g[4],colorInfo:g[6],colorInfoActive:g[7],colorInfoTextHover:g[8],colorInfoText:g[9],colorInfoTextActive:g[10],colorLinkHover:y[4],colorLink:y[6],colorLinkActive:y[7],colorBgMask:new Ve("#000").setA(.45).toRgbString(),colorWhite:"#fff"})}const pv=e=>{let t=e,r=e,n=e,o=e;return e<6&&e>=5?t=e+1:e<16&&e>=6?t=e+2:e>=16&&(t=16),e<7&&e>=5?r=4:e<8&&e>=7?r=5:e<14&&e>=8?r=6:e<16&&e>=14?r=7:e>=16&&(r=8),e<6&&e>=2?n=1:e>=6&&(n=2),e>4&&e<8?o=4:e>=8&&(o=6),{borderRadius:e,borderRadiusXS:n,borderRadiusSM:r,borderRadiusLG:t,borderRadiusOuter:o}};function bv(e){const{motionUnit:t,motionBase:r,borderRadius:n,lineWidth:o}=e;return Object.assign({motionDurationFast:`${(r+t).toFixed(1)}s`,motionDurationMid:`${(r+t*2).toFixed(1)}s`,motionDurationSlow:`${(r+t*3).toFixed(1)}s`,lineWidthBold:o+1},pv(n))}const zc=e=>{const{controlHeight:t}=e;return{controlHeightSM:t*.75,controlHeightXS:t*.5,controlHeightLG:t*1.25}};function jo(e){return(e+8)/e}function yv(e){const t=Array.from({length:10}).map((r,n)=>{const o=n-1,i=e*Math.pow(Math.E,o/5),a=n>1?Math.floor(i):Math.ceil(i);return Math.floor(a/2)*2});return t[1]=e,t.map(r=>({size:r,lineHeight:jo(r)}))}const kc=e=>{const t=yv(e),r=t.map(d=>d.size),n=t.map(d=>d.lineHeight),o=r[1],i=r[0],a=r[2],s=n[1],l=n[0],u=n[2];return{fontSizeSM:i,fontSize:o,fontSizeLG:a,fontSizeXL:r[3],fontSizeHeading1:r[6],fontSizeHeading2:r[5],fontSizeHeading3:r[4],fontSizeHeading4:r[3],fontSizeHeading5:r[2],lineHeight:s,lineHeightLG:u,lineHeightSM:l,fontHeight:Math.round(s*o),fontHeightLG:Math.round(u*a),fontHeightSM:Math.round(l*i),lineHeightHeading1:n[6],lineHeightHeading2:n[5],lineHeightHeading3:n[4],lineHeightHeading4:n[3],lineHeightHeading5:n[2]}};function Cv(e){const{sizeUnit:t,sizeStep:r}=e;return{sizeXXL:t*(r+8),sizeXL:t*(r+4),sizeLG:t*(r+2),sizeMD:t*(r+1),sizeMS:t*r,size:t*r,sizeSM:t*(r-1),sizeXS:t*(r-2),sizeXXS:t*(r-3)}}const Bt=(e,t)=>new Ve(e).setA(t).toRgbString(),_n=(e,t)=>new Ve(e).darken(t).toHexString(),Sv=e=>{const t=qr(e);return{1:t[0],2:t[1],3:t[2],4:t[3],5:t[4],6:t[5],7:t[6],8:t[4],9:t[5],10:t[6]}},xv=(e,t)=>{const r=e||"#fff",n=t||"#000";return{colorBgBase:r,colorTextBase:n,colorText:Bt(n,.88),colorTextSecondary:Bt(n,.65),colorTextTertiary:Bt(n,.45),colorTextQuaternary:Bt(n,.25),colorFill:Bt(n,.15),colorFillSecondary:Bt(n,.06),colorFillTertiary:Bt(n,.04),colorFillQuaternary:Bt(n,.02),colorBgSolid:Bt(n,1),colorBgSolidHover:Bt(n,.75),colorBgSolidActive:Bt(n,.95),colorBgLayout:_n(r,4),colorBgContainer:_n(r,0),colorBgElevated:_n(r,0),colorBgSpotlight:Bt(n,.85),colorBgBlur:"transparent",colorBorder:_n(r,15),colorBorderSecondary:_n(r,6)}};function oi(e){Pi.pink=Pi.magenta,Ri.pink=Ri.magenta;const t=Object.keys(Qa).map(r=>{const n=e[r]===Pi[r]?Ri[r]:qr(e[r]);return Array.from({length:10},()=>1).reduce((o,i,a)=>(o[`${r}-${a+1}`]=n[a],o[`${r}${a+1}`]=n[a],o),{})}).reduce((r,n)=>(r=Object.assign(Object.assign({},r),n),r),{});return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},e),t),Bc(e,{generateColorPalettes:Sv,generateNeutralColorPalettes:xv})),kc(e.fontSize)),Cv(e)),zc(e)),bv(e))}const Za=ko(oi),Yn={token:pn,override:{override:pn},hashed:!0},Ya=re.createContext(Yn),Jn="ant",Ja="anticon",wv=(e,t)=>t||(e?`${Jn}-${e}`:Jn),vt=c.createContext({getPrefixCls:wv,iconPrefixCls:Ja}),{Consumer:iy}=vt,Vs={};function es(e){const t=c.useContext(vt),{getPrefixCls:r,direction:n,getPopupContainer:o}=t,i=t[e];return Object.assign(Object.assign({classNames:Vs,styles:Vs},i),{getPrefixCls:r,direction:n,getPopupContainer:o})}const $v=`-ant-${Date.now()}-${Math.random()}`;function Ev(e,t){const r={},n=(a,s)=>{let l=a.clone();return l=s?.(l)||l,l.toRgbString()},o=(a,s)=>{const l=new Ve(a),u=qr(l.toRgbString());r[`${s}-color`]=n(l),r[`${s}-color-disabled`]=u[1],r[`${s}-color-hover`]=u[4],r[`${s}-color-active`]=u[6],r[`${s}-color-outline`]=l.clone().setA(.2).toRgbString(),r[`${s}-color-deprecated-bg`]=u[0],r[`${s}-color-deprecated-border`]=u[2]};if(t.primaryColor){o(t.primaryColor,"primary");const a=new Ve(t.primaryColor),s=qr(a.toRgbString());s.forEach((u,d)=>{r[`primary-${d+1}`]=u}),r["primary-color-deprecated-l-35"]=n(a,u=>u.lighten(35)),r["primary-color-deprecated-l-20"]=n(a,u=>u.lighten(20)),r["primary-color-deprecated-t-20"]=n(a,u=>u.tint(20)),r["primary-color-deprecated-t-50"]=n(a,u=>u.tint(50)),r["primary-color-deprecated-f-12"]=n(a,u=>u.setA(u.a*.12));const l=new Ve(s[0]);r["primary-color-active-deprecated-f-30"]=n(l,u=>u.setA(u.a*.3)),r["primary-color-active-deprecated-d-02"]=n(l,u=>u.darken(2))}return t.successColor&&o(t.successColor,"success"),t.warningColor&&o(t.warningColor,"warning"),t.errorColor&&o(t.errorColor,"error"),t.infoColor&&o(t.infoColor,"info"),`
  :root {
    ${Object.keys(r).map(a=>`--${e}-${a}: ${r[a]};`).join(`
`)}
  }
  `.trim()}function Ov(e,t){const r=Ev(e,t);_t()&&pr(r,`${$v}-dynamic-theme`)}const Vo=c.createContext(!1),Iv=({children:e,disabled:t})=>{const r=c.useContext(Vo);return c.createElement(Vo.Provider,{value:t??r},e)},bn=c.createContext(void 0),Pv=({children:e,size:t})=>{const r=c.useContext(bn);return c.createElement(bn.Provider,{value:t||r},e)};function Rv(){const e=c.useContext(Vo),t=c.useContext(bn);return{componentDisabled:e,componentSize:t}}var Hc=lt(function e(){st(this,e)}),Dc="CALC_UNIT",Mv=new RegExp(Dc,"g");function Mi(e){return typeof e=="number"?"".concat(e).concat(Dc):e}var Tv=function(e){yr(r,e);var t=Cr(r);function r(n,o){var i;st(this,r),i=t.call(this),$(fe(i),"result",""),$(fe(i),"unitlessCssVar",void 0),$(fe(i),"lowPriority",void 0);var a=ve(n);return i.unitlessCssVar=o,n instanceof r?i.result="(".concat(n.result,")"):a==="number"?i.result=Mi(n):a==="string"&&(i.result=n),i}return lt(r,[{key:"add",value:function(o){return o instanceof r?this.result="".concat(this.result," + ").concat(o.getResult()):(typeof o=="number"||typeof o=="string")&&(this.result="".concat(this.result," + ").concat(Mi(o))),this.lowPriority=!0,this}},{key:"sub",value:function(o){return o instanceof r?this.result="".concat(this.result," - ").concat(o.getResult()):(typeof o=="number"||typeof o=="string")&&(this.result="".concat(this.result," - ").concat(Mi(o))),this.lowPriority=!0,this}},{key:"mul",value:function(o){return this.lowPriority&&(this.result="(".concat(this.result,")")),o instanceof r?this.result="".concat(this.result," * ").concat(o.getResult(!0)):(typeof o=="number"||typeof o=="string")&&(this.result="".concat(this.result," * ").concat(o)),this.lowPriority=!1,this}},{key:"div",value:function(o){return this.lowPriority&&(this.result="(".concat(this.result,")")),o instanceof r?this.result="".concat(this.result," / ").concat(o.getResult(!0)):(typeof o=="number"||typeof o=="string")&&(this.result="".concat(this.result," / ").concat(o)),this.lowPriority=!1,this}},{key:"getResult",value:function(o){return this.lowPriority||o?"(".concat(this.result,")"):this.result}},{key:"equal",value:function(o){var i=this,a=o||{},s=a.unit,l=!0;return typeof s=="boolean"?l=s:Array.from(this.unitlessCssVar).some(function(u){return i.result.includes(u)})&&(l=!1),this.result=this.result.replace(Mv,l?"px":""),typeof this.lowPriority<"u"?"calc(".concat(this.result,")"):this.result}}]),r}(Hc),_v=function(e){yr(r,e);var t=Cr(r);function r(n){var o;return st(this,r),o=t.call(this),$(fe(o),"result",0),n instanceof r?o.result=n.result:typeof n=="number"&&(o.result=n),o}return lt(r,[{key:"add",value:function(o){return o instanceof r?this.result+=o.result:typeof o=="number"&&(this.result+=o),this}},{key:"sub",value:function(o){return o instanceof r?this.result-=o.result:typeof o=="number"&&(this.result-=o),this}},{key:"mul",value:function(o){return o instanceof r?this.result*=o.result:typeof o=="number"&&(this.result*=o),this}},{key:"div",value:function(o){return o instanceof r?this.result/=o.result:typeof o=="number"&&(this.result/=o),this}},{key:"equal",value:function(){return this.result}}]),r}(Hc),Av=function(t,r){var n=t==="css"?Tv:_v;return function(o){return new n(o,r)}},Ws=function(t,r){return"".concat([r,t.replace(/([A-Z]+)([A-Z][a-z]+)/g,"$1-$2").replace(/([a-z])([A-Z])/g,"$1-$2")].filter(Boolean).join("-"))};function Ct(e){var t=c.useRef();t.current=e;var r=c.useCallback(function(){for(var n,o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return(n=t.current)===null||n===void 0?void 0:n.call.apply(n,[t].concat(i))},[]);return r}function eo(e){var t=c.useRef(!1),r=c.useState(e),n=j(r,2),o=n[0],i=n[1];c.useEffect(function(){return t.current=!1,function(){t.current=!0}},[]);function a(s,l){l&&t.current||i(s)}return[o,a]}function Ti(e){return e!==void 0}function Hn(e,t){var r=t||{},n=r.defaultValue,o=r.value,i=r.onChange,a=r.postState,s=eo(function(){return Ti(o)?o:Ti(n)?typeof n=="function"?n():n:typeof e=="function"?e():e}),l=j(s,2),u=l[0],d=l[1],f=o!==void 0?o:u,m=a?a(f):f,p=Ct(i),g=eo([f]),h=j(g,2),v=h[0],y=h[1];Ms(function(){var C=v[0];u!==C&&p(u,C)},[v]),Ms(function(){Ti(o)||d(o)},[o]);var b=Ct(function(C,w){d(C,w),y([f],w)});return[m,b]}function qs(e,t,r,n){var o=P({},t[e]);if(n!=null&&n.deprecatedTokens){var i=n.deprecatedTokens;i.forEach(function(s){var l=j(s,2),u=l[0],d=l[1];if(o!=null&&o[u]||o!=null&&o[d]){var f;(f=o[d])!==null&&f!==void 0||(o[d]=o?.[u])}})}var a=P(P({},r),o);return Object.keys(a).forEach(function(s){a[s]===t[s]&&delete a[s]}),a}var Vc=typeof CSSINJS_STATISTIC<"u",ma=!0;function sr(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!Vc)return Object.assign.apply(Object,[{}].concat(t));ma=!1;var n={};return t.forEach(function(o){if(ve(o)==="object"){var i=Object.keys(o);i.forEach(function(a){Object.defineProperty(n,a,{configurable:!0,enumerable:!0,get:function(){return o[a]}})})}}),ma=!0,n}var Us={};function Fv(){}var jv=function(t){var r,n=t,o=Fv;return Vc&&typeof Proxy<"u"&&(r=new Set,n=new Proxy(t,{get:function(a,s){if(ma){var l;(l=r)===null||l===void 0||l.add(s)}return a[s]}}),o=function(a,s){var l;Us[a]={global:Array.from(r),component:P(P({},(l=Us[a])===null||l===void 0?void 0:l.component),s)}}),{token:n,keys:r,flush:o}};function Ks(e,t,r){if(typeof r=="function"){var n;return r(sr(t,(n=t[e])!==null&&n!==void 0?n:{}))}return r??{}}function Nv(e){return e==="js"?{max:Math.max,min:Math.min}:{max:function(){for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return"max(".concat(n.map(function(i){return ie(i)}).join(","),")")},min:function(){for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return"min(".concat(n.map(function(i){return ie(i)}).join(","),")")}}}var Lv=1e3*60*10,Bv=function(){function e(){st(this,e),$(this,"map",new Map),$(this,"objectIDMap",new WeakMap),$(this,"nextID",0),$(this,"lastAccessBeat",new Map),$(this,"accessBeat",0)}return lt(e,[{key:"set",value:function(r,n){this.clear();var o=this.getCompositeKey(r);this.map.set(o,n),this.lastAccessBeat.set(o,Date.now())}},{key:"get",value:function(r){var n=this.getCompositeKey(r),o=this.map.get(n);return this.lastAccessBeat.set(n,Date.now()),this.accessBeat+=1,o}},{key:"getCompositeKey",value:function(r){var n=this,o=r.map(function(i){return i&&ve(i)==="object"?"obj_".concat(n.getObjectID(i)):"".concat(ve(i),"_").concat(i)});return o.join("|")}},{key:"getObjectID",value:function(r){if(this.objectIDMap.has(r))return this.objectIDMap.get(r);var n=this.nextID;return this.objectIDMap.set(r,n),this.nextID+=1,n}},{key:"clear",value:function(){var r=this;if(this.accessBeat>1e4){var n=Date.now();this.lastAccessBeat.forEach(function(o,i){n-o>Lv&&(r.map.delete(i),r.lastAccessBeat.delete(i))}),this.accessBeat=0}}}]),e}(),Gs=new Bv;function zv(e,t){return re.useMemo(function(){var r=Gs.get(t);if(r)return r;var n=e();return Gs.set(t,n),n},t)}var kv=function(){return{}};function Hv(e){var t=e.useCSP,r=t===void 0?kv:t,n=e.useToken,o=e.usePrefix,i=e.getResetStyles,a=e.getCommonStyle,s=e.getCompUnitless;function l(m,p,g,h){var v=Array.isArray(m)?m[0]:m;function y(O){return"".concat(String(v)).concat(O.slice(0,1).toUpperCase()).concat(O.slice(1))}var b=h?.unitless||{},C=typeof s=="function"?s(m):{},w=P(P({},C),{},$({},y("zIndexPopup"),!0));Object.keys(b).forEach(function(O){w[y(O)]=b[O]});var S=P(P({},h),{},{unitless:w,prefixToken:y}),x=d(m,p,g,S),E=u(v,g,S);return function(O){var I=arguments.length>1&&arguments[1]!==void 0?arguments[1]:O,R=x(O,I),A=j(R,2),M=A[1],T=E(I),_=j(T,2),N=_[0],F=_[1];return[N,M,F]}}function u(m,p,g){var h=g.unitless,v=g.injectStyle,y=v===void 0?!0:v,b=g.prefixToken,C=g.ignore,w=function(E){var O=E.rootCls,I=E.cssVar,R=I===void 0?{}:I,A=n(),M=A.realToken;return rv({path:[m],prefix:R.prefix,key:R.key,unitless:h,ignore:C,token:M,scope:O},function(){var T=Ks(m,M,p),_=qs(m,M,T,{deprecatedTokens:g?.deprecatedTokens});return Object.keys(T).forEach(function(N){_[b(N)]=_[N],delete _[N]}),_}),null},S=function(E){var O=n(),I=O.cssVar;return[function(R){return y&&I?re.createElement(re.Fragment,null,re.createElement(w,{rootCls:E,cssVar:I,component:m}),R):R},I?.key]};return S}function d(m,p,g){var h=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},v=Array.isArray(m)?m:[m,m],y=j(v,1),b=y[0],C=v.join("-"),w=e.layer||{name:"antd"};return function(S){var x=arguments.length>1&&arguments[1]!==void 0?arguments[1]:S,E=n(),O=E.theme,I=E.realToken,R=E.hashId,A=E.token,M=E.cssVar,T=o(),_=T.rootPrefixCls,N=T.iconPrefixCls,F=r(),L=M?"css":"js",B=zv(function(){var H=new Set;return M&&Object.keys(h.unitless||{}).forEach(function(k){H.add(Ro(k,M.prefix)),H.add(Ro(k,Ws(b,M.prefix)))}),Av(L,H)},[L,b,M?.prefix]),V=Nv(L),K=V.max,U=V.min,z={theme:O,token:A,hashId:R,nonce:function(){return F.nonce},clientOnly:h.clientOnly,layer:w,order:h.order||-999};typeof i=="function"&&ta(P(P({},z),{},{clientOnly:!1,path:["Shared",_]}),function(){return i(A,{prefix:{rootPrefixCls:_,iconPrefixCls:N},csp:F})});var X=ta(P(P({},z),{},{path:[C,S,N]}),function(){if(h.injectStyle===!1)return[];var H=jv(A),k=H.token,ae=H.flush,se=Ks(b,I,g),Y=".".concat(S),G=qs(b,I,se,{deprecatedTokens:h.deprecatedTokens});M&&se&&ve(se)==="object"&&Object.keys(se).forEach(function(be){se[be]="var(".concat(Ro(be,Ws(b,M.prefix)),")")});var ue=sr(k,{componentCls:Y,prefixCls:S,iconCls:".".concat(N),antCls:".".concat(_),calc:B,max:K,min:U},M?se:G),Se=p(ue,{hashId:R,prefixCls:S,rootPrefixCls:_,iconPrefixCls:N});ae(b,G);var me=typeof a=="function"?a(ue,S,x,h.resetFont):null;return[h.resetStyle===!1?null:me,Se]});return[X,R]}}function f(m,p,g){var h=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},v=d(m,p,g,P({resetStyle:!1,order:-998},h)),y=function(C){var w=C.prefixCls,S=C.rootCls,x=S===void 0?w:S;return v(w,x),null};return y}return{genStyleHooks:l,genSubStyleComponent:f,genComponentStyleHook:d}}const Ur=["blue","purple","cyan","green","magenta","pink","red","orange","yellow","volcano","geekblue","lime","gold"],Dv="5.26.6";function _i(e){return e>=0&&e<=255}function Ln(e,t){const{r,g:n,b:o,a:i}=new Ve(e).toRgb();if(i<1)return e;const{r:a,g:s,b:l}=new Ve(t).toRgb();for(let u=.01;u<=1;u+=.01){const d=Math.round((r-a*(1-u))/u),f=Math.round((n-s*(1-u))/u),m=Math.round((o-l*(1-u))/u);if(_i(d)&&_i(f)&&_i(m))return new Ve({r:d,g:f,b:m,a:Math.round(u*100)/100}).toRgbString()}return new Ve({r,g:n,b:o,a:1}).toRgbString()}var Vv=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function ts(e){const{override:t}=e,r=Vv(e,["override"]),n=Object.assign({},t);Object.keys(pn).forEach(m=>{delete n[m]});const o=Object.assign(Object.assign({},r),n),i=480,a=576,s=768,l=992,u=1200,d=1600;return o.motion===!1&&(o.motionDurationFast="0s",o.motionDurationMid="0s",o.motionDurationSlow="0s"),Object.assign(Object.assign(Object.assign({},o),{colorFillContent:o.colorFillSecondary,colorFillContentHover:o.colorFill,colorFillAlter:o.colorFillQuaternary,colorBgContainerDisabled:o.colorFillTertiary,colorBorderBg:o.colorBgContainer,colorSplit:Ln(o.colorBorderSecondary,o.colorBgContainer),colorTextPlaceholder:o.colorTextQuaternary,colorTextDisabled:o.colorTextQuaternary,colorTextHeading:o.colorText,colorTextLabel:o.colorTextSecondary,colorTextDescription:o.colorTextTertiary,colorTextLightSolid:o.colorWhite,colorHighlight:o.colorError,colorBgTextHover:o.colorFillSecondary,colorBgTextActive:o.colorFill,colorIcon:o.colorTextTertiary,colorIconHover:o.colorText,colorErrorOutline:Ln(o.colorErrorBg,o.colorBgContainer),colorWarningOutline:Ln(o.colorWarningBg,o.colorBgContainer),fontSizeIcon:o.fontSizeSM,lineWidthFocus:o.lineWidth*3,lineWidth:o.lineWidth,controlOutlineWidth:o.lineWidth*2,controlInteractiveSize:o.controlHeight/2,controlItemBgHover:o.colorFillTertiary,controlItemBgActive:o.colorPrimaryBg,controlItemBgActiveHover:o.colorPrimaryBgHover,controlItemBgActiveDisabled:o.colorFill,controlTmpOutline:o.colorFillQuaternary,controlOutline:Ln(o.colorPrimaryBg,o.colorBgContainer),lineType:o.lineType,borderRadius:o.borderRadius,borderRadiusXS:o.borderRadiusXS,borderRadiusSM:o.borderRadiusSM,borderRadiusLG:o.borderRadiusLG,fontWeightStrong:600,opacityLoading:.65,linkDecoration:"none",linkHoverDecoration:"none",linkFocusDecoration:"none",controlPaddingHorizontal:12,controlPaddingHorizontalSM:8,paddingXXS:o.sizeXXS,paddingXS:o.sizeXS,paddingSM:o.sizeSM,padding:o.size,paddingMD:o.sizeMD,paddingLG:o.sizeLG,paddingXL:o.sizeXL,paddingContentHorizontalLG:o.sizeLG,paddingContentVerticalLG:o.sizeMS,paddingContentHorizontal:o.sizeMS,paddingContentVertical:o.sizeSM,paddingContentHorizontalSM:o.size,paddingContentVerticalSM:o.sizeXS,marginXXS:o.sizeXXS,marginXS:o.sizeXS,marginSM:o.sizeSM,margin:o.size,marginMD:o.sizeMD,marginLG:o.sizeLG,marginXL:o.sizeXL,marginXXL:o.sizeXXL,boxShadow:`
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowSecondary:`
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowTertiary:`
      0 1px 2px 0 rgba(0, 0, 0, 0.03),
      0 1px 6px -1px rgba(0, 0, 0, 0.02),
      0 2px 4px 0 rgba(0, 0, 0, 0.02)
    `,screenXS:i,screenXSMin:i,screenXSMax:a-1,screenSM:a,screenSMMin:a,screenSMMax:s-1,screenMD:s,screenMDMin:s,screenMDMax:l-1,screenLG:l,screenLGMin:l,screenLGMax:u-1,screenXL:u,screenXLMin:u,screenXLMax:d-1,screenXXL:d,screenXXLMin:d,boxShadowPopoverArrow:"2px 2px 5px rgba(0, 0, 0, 0.05)",boxShadowCard:`
      0 1px 2px -2px ${new Ve("rgba(0, 0, 0, 0.16)").toRgbString()},
      0 3px 6px 0 ${new Ve("rgba(0, 0, 0, 0.12)").toRgbString()},
      0 5px 12px 4px ${new Ve("rgba(0, 0, 0, 0.09)").toRgbString()}
    `,boxShadowDrawerRight:`
      -6px 0 16px 0 rgba(0, 0, 0, 0.08),
      -3px 0 6px -4px rgba(0, 0, 0, 0.12),
      -9px 0 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowDrawerLeft:`
      6px 0 16px 0 rgba(0, 0, 0, 0.08),
      3px 0 6px -4px rgba(0, 0, 0, 0.12),
      9px 0 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowDrawerUp:`
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowDrawerDown:`
      0 -6px 16px 0 rgba(0, 0, 0, 0.08),
      0 -3px 6px -4px rgba(0, 0, 0, 0.12),
      0 -9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowTabsOverflowLeft:"inset 10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowRight:"inset -10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowTop:"inset 0 10px 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowBottom:"inset 0 -10px 8px -8px rgba(0, 0, 0, 0.08)"}),n)}var Xs=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Wc={lineHeight:!0,lineHeightSM:!0,lineHeightLG:!0,lineHeightHeading1:!0,lineHeightHeading2:!0,lineHeightHeading3:!0,lineHeightHeading4:!0,lineHeightHeading5:!0,opacityLoading:!0,fontWeightStrong:!0,zIndexPopupBase:!0,zIndexBase:!0,opacityImage:!0},Wv={size:!0,sizeSM:!0,sizeLG:!0,sizeMD:!0,sizeXS:!0,sizeXXS:!0,sizeMS:!0,sizeXL:!0,sizeXXL:!0,sizeUnit:!0,sizeStep:!0,motionBase:!0,motionUnit:!0},qv={screenXS:!0,screenXSMin:!0,screenXSMax:!0,screenSM:!0,screenSMMin:!0,screenSMMax:!0,screenMD:!0,screenMDMin:!0,screenMDMax:!0,screenLG:!0,screenLGMin:!0,screenLGMax:!0,screenXL:!0,screenXLMin:!0,screenXLMax:!0,screenXXL:!0,screenXXLMin:!0},qc=(e,t,r)=>{const n=r.getDerivativeToken(e),{override:o}=t,i=Xs(t,["override"]);let a=Object.assign(Object.assign({},n),{override:o});return a=ts(a),i&&Object.entries(i).forEach(([s,l])=>{const{theme:u}=l,d=Xs(l,["theme"]);let f=d;u&&(f=qc(Object.assign(Object.assign({},a),d),{override:d},u)),a[s]=f}),a};function Sr(){const{token:e,hashed:t,theme:r,override:n,cssVar:o}=re.useContext(Ya),i=`${Dv}-${t||""}`,a=r||Za,[s,l,u]=Pf(a,[pn,e],{salt:i,override:n,getComputedToken:qc,formatToken:ts,cssVar:o&&{prefix:o.prefix,key:o.key,unitless:Wc,ignore:Wv,preserve:qv}});return[a,u,t?l:"",s,o]}const Uv={overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"},Uc=(e,t=!1)=>({boxSizing:"border-box",margin:0,padding:0,color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight,listStyle:"none",fontFamily:t?"inherit":e.fontFamily}),rs=()=>({display:"inline-flex",alignItems:"center",color:"inherit",fontStyle:"normal",lineHeight:0,textAlign:"center",textTransform:"none",verticalAlign:"-0.125em",textRendering:"optimizeLegibility","-webkit-font-smoothing":"antialiased","-moz-osx-font-smoothing":"grayscale","> *":{lineHeight:1},svg:{display:"inline-block"}}),Qs=()=>({"&::before":{display:"table",content:'""'},"&::after":{display:"table",clear:"both",content:'""'}}),Kv=e=>({a:{color:e.colorLink,textDecoration:e.linkDecoration,backgroundColor:"transparent",outline:"none",cursor:"pointer",transition:`color ${e.motionDurationSlow}`,"-webkit-text-decoration-skip":"objects","&:hover":{color:e.colorLinkHover},"&:active":{color:e.colorLinkActive},"&:active, &:hover":{textDecoration:e.linkHoverDecoration,outline:0},"&:focus":{textDecoration:e.linkFocusDecoration,outline:0},"&[disabled]":{color:e.colorTextDisabled,cursor:"not-allowed"}}}),Gv=(e,t,r,n)=>{const o=`[class^="${t}"], [class*=" ${t}"]`,i=r?`.${r}`:o,a={boxSizing:"border-box","&::before, &::after":{boxSizing:"border-box"}};let s={};return n!==!1&&(s={fontFamily:e.fontFamily,fontSize:e.fontSize}),{[i]:Object.assign(Object.assign(Object.assign({},s),a),{[o]:a})}},Kc=(e,t)=>({outline:`${ie(e.lineWidthFocus)} solid ${e.colorPrimaryBorder}`,outlineOffset:t??1,transition:"outline-offset 0s, outline 0s"}),Xv=(e,t)=>({"&:focus-visible":Object.assign({},Kc(e,t))}),Gc=e=>({[`.${e}`]:Object.assign(Object.assign({},rs()),{[`.${e} .${e}-icon`]:{display:"block"}})}),{genStyleHooks:co,genComponentStyleHook:Qv,genSubStyleComponent:Zv}=Hv({usePrefix:()=>{const{getPrefixCls:e,iconPrefixCls:t}=c.useContext(vt);return{rootPrefixCls:e(),iconPrefixCls:t}},useToken:()=>{const[e,t,r,n,o]=Sr();return{theme:e,realToken:t,hashId:r,token:n,cssVar:o}},useCSP:()=>{const{csp:e}=c.useContext(vt);return e??{}},getResetStyles:(e,t)=>{var r;const n=Kv(e);return[n,{"&":n},Gc((r=t?.prefix.iconPrefixCls)!==null&&r!==void 0?r:Ja)]},getCommonStyle:Gv,getCompUnitless:()=>Wc});function Yv(e,t){return Ur.reduce((r,n)=>{const o=e[`${n}1`],i=e[`${n}3`],a=e[`${n}6`],s=e[`${n}7`];return Object.assign(Object.assign({},r),t(n,{lightColor:o,lightBorderColor:i,darkColor:a,textColor:s}))},{})}const Jv=(e,t)=>{const[r,n]=Sr();return ta({token:n,hashId:"",path:["ant-design-icons",e],nonce:()=>t?.nonce,layer:{name:"antd"}},()=>[Gc(e)])},em=Object.assign({},Xo),{useId:Zs}=em,tm=()=>"",rm=typeof Zs>"u"?tm:Zs;function nm(e,t,r){var n;Fc();const o=e||{},i=o.inherit===!1||!t?Object.assign(Object.assign({},Yn),{hashed:(n=t?.hashed)!==null&&n!==void 0?n:Yn.hashed,cssVar:t?.cssVar}):t,a=rm();return Qo(()=>{var s,l;if(!e)return t;const u=Object.assign({},i.components);Object.keys(e.components||{}).forEach(m=>{u[m]=Object.assign(Object.assign({},u[m]),e.components[m])});const d=`css-var-${a.replace(/:/g,"")}`,f=((s=o.cssVar)!==null&&s!==void 0?s:i.cssVar)&&Object.assign(Object.assign(Object.assign({prefix:r?.prefixCls},typeof i.cssVar=="object"?i.cssVar:{}),typeof o.cssVar=="object"?o.cssVar:{}),{key:typeof o.cssVar=="object"&&((l=o.cssVar)===null||l===void 0?void 0:l.key)||d});return Object.assign(Object.assign(Object.assign({},i),o),{token:Object.assign(Object.assign({},i.token),o.token),components:u,cssVar:f})},[o,i],(s,l)=>s.some((u,d)=>{const f=l[d];return!Qn(u,f,!0)}))}var om=["children"],Xc=c.createContext({});function im(e){var t=e.children,r=ke(e,om);return c.createElement(Xc.Provider,{value:r},t)}var am=function(e){yr(r,e);var t=Cr(r);function r(){return st(this,r),t.apply(this,arguments)}return lt(r,[{key:"render",value:function(){return this.props.children}}]),r}(c.Component);function sm(e){var t=c.useReducer(function(s){return s+1},0),r=j(t,2),n=r[1],o=c.useRef(e),i=Ct(function(){return o.current}),a=Ct(function(s){o.current=typeof s=="function"?s(o.current):s,n()});return[i,a]}var Rr="none",xo="appear",wo="enter",$o="leave",Ys="none",qt="prepare",cn="start",un="active",ns="end",Qc="prepared";function Js(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit".concat(e)]="webkit".concat(t),r["Moz".concat(e)]="moz".concat(t),r["ms".concat(e)]="MS".concat(t),r["O".concat(e)]="o".concat(t.toLowerCase()),r}function lm(e,t){var r={animationend:Js("Animation","AnimationEnd"),transitionend:Js("Transition","TransitionEnd")};return e&&("AnimationEvent"in t||delete r.animationend.animation,"TransitionEvent"in t||delete r.transitionend.transition),r}var cm=lm(_t(),typeof window<"u"?window:{}),Zc={};if(_t()){var um=document.createElement("div");Zc=um.style}var Eo={};function Yc(e){if(Eo[e])return Eo[e];var t=cm[e];if(t)for(var r=Object.keys(t),n=r.length,o=0;o<n;o+=1){var i=r[o];if(Object.prototype.hasOwnProperty.call(t,i)&&i in Zc)return Eo[e]=t[i],Eo[e]}return""}var Jc=Yc("animationend"),eu=Yc("transitionend"),tu=!!(Jc&&eu),el=Jc||"animationend",tl=eu||"transitionend";function rl(e,t){if(!e)return null;if(ve(e)==="object"){var r=t.replace(/-\w/g,function(n){return n[1].toUpperCase()});return e[r]}return"".concat(e,"-").concat(t)}const dm=function(e){var t=c.useRef();function r(o){o&&(o.removeEventListener(tl,e),o.removeEventListener(el,e))}function n(o){t.current&&t.current!==o&&r(t.current),o&&o!==t.current&&(o.addEventListener(tl,e),o.addEventListener(el,e),t.current=o)}return c.useEffect(function(){return function(){r(t.current)}},[]),[n,r]};var ru=_t()?c.useLayoutEffect:c.useEffect;const fm=function(){var e=c.useRef(null);function t(){Tt.cancel(e.current)}function r(n){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:2;t();var i=Tt(function(){o<=1?n({isCanceled:function(){return i!==e.current}}):r(n,o-1)});e.current=i}return c.useEffect(function(){return function(){t()}},[]),[r,t]};var vm=[qt,cn,un,ns],mm=[qt,Qc],nu=!1,gm=!0;function ou(e){return e===un||e===ns}const hm=function(e,t,r){var n=eo(Ys),o=j(n,2),i=o[0],a=o[1],s=fm(),l=j(s,2),u=l[0],d=l[1];function f(){a(qt,!0)}var m=t?mm:vm;return ru(function(){if(i!==Ys&&i!==ns){var p=m.indexOf(i),g=m[p+1],h=r(i);h===nu?a(g,!0):g&&u(function(v){function y(){v.isCanceled()||a(g,!0)}h===!0?y():Promise.resolve(h).then(y)})}},[e,i]),c.useEffect(function(){return function(){d()}},[]),[f,i]};function pm(e,t,r,n){var o=n.motionEnter,i=o===void 0?!0:o,a=n.motionAppear,s=a===void 0?!0:a,l=n.motionLeave,u=l===void 0?!0:l,d=n.motionDeadline,f=n.motionLeaveImmediately,m=n.onAppearPrepare,p=n.onEnterPrepare,g=n.onLeavePrepare,h=n.onAppearStart,v=n.onEnterStart,y=n.onLeaveStart,b=n.onAppearActive,C=n.onEnterActive,w=n.onLeaveActive,S=n.onAppearEnd,x=n.onEnterEnd,E=n.onLeaveEnd,O=n.onVisibleChanged,I=eo(),R=j(I,2),A=R[0],M=R[1],T=sm(Rr),_=j(T,2),N=_[0],F=_[1],L=eo(null),B=j(L,2),V=B[0],K=B[1],U=N(),z=c.useRef(!1),X=c.useRef(null);function H(){return r()}var k=c.useRef(!1);function ae(){F(Rr),K(null,!0)}var se=Ct(function(ce){var pe=N();if(pe!==Rr){var ye=H();if(!(ce&&!ce.deadline&&ce.target!==ye)){var te=k.current,qe;pe===xo&&te?qe=S?.(ye,ce):pe===wo&&te?qe=x?.(ye,ce):pe===$o&&te&&(qe=E?.(ye,ce)),te&&qe!==!1&&ae()}}}),Y=dm(se),G=j(Y,1),ue=G[0],Se=function(pe){switch(pe){case xo:return $($($({},qt,m),cn,h),un,b);case wo:return $($($({},qt,p),cn,v),un,C);case $o:return $($($({},qt,g),cn,y),un,w);default:return{}}},me=c.useMemo(function(){return Se(U)},[U]),be=hm(U,!e,function(ce){if(ce===qt){var pe=me[qt];return pe?pe(H()):nu}if(Ee in me){var ye;K(((ye=me[Ee])===null||ye===void 0?void 0:ye.call(me,H(),null))||null)}return Ee===un&&U!==Rr&&(ue(H()),d>0&&(clearTimeout(X.current),X.current=setTimeout(function(){se({deadline:!0})},d))),Ee===Qc&&ae(),gm}),he=j(be,2),$e=he[0],Ee=he[1],_e=ou(Ee);k.current=_e;var Oe=c.useRef(null);ru(function(){if(!(z.current&&Oe.current===t)){M(t);var ce=z.current;z.current=!0;var pe;!ce&&t&&s&&(pe=xo),ce&&t&&i&&(pe=wo),(ce&&!t&&u||!ce&&f&&!t&&u)&&(pe=$o);var ye=Se(pe);pe&&(e||ye[qt])?(F(pe),$e()):F(Rr),Oe.current=t}},[t]),c.useEffect(function(){(U===xo&&!s||U===wo&&!i||U===$o&&!u)&&F(Rr)},[s,i,u]),c.useEffect(function(){return function(){z.current=!1,clearTimeout(X.current)}},[]);var we=c.useRef(!1);c.useEffect(function(){A&&(we.current=!0),A!==void 0&&U===Rr&&((we.current||A)&&O?.(A),we.current=!0)},[A,U]);var He=V;return me[qt]&&Ee===cn&&(He=P({transition:"none"},He)),[U,Ee,He,A??t]}function bm(e){var t=e;ve(e)==="object"&&(t=e.transitionSupport);function r(o,i){return!!(o.motionName&&t&&i!==!1)}var n=c.forwardRef(function(o,i){var a=o.visible,s=a===void 0?!0:a,l=o.removeOnLeave,u=l===void 0?!0:l,d=o.forceRender,f=o.children,m=o.motionName,p=o.leavedClassName,g=o.eventProps,h=c.useContext(Xc),v=h.motion,y=r(o,v),b=c.useRef(),C=c.useRef();function w(){try{return b.current instanceof HTMLElement?b.current:Po(C.current)}catch{return null}}var S=pm(y,s,w,o),x=j(S,4),E=x[0],O=x[1],I=x[2],R=x[3],A=c.useRef(R);R&&(A.current=!0);var M=c.useCallback(function(B){b.current=B,ka(i,B)},[i]),T,_=P(P({},g),{},{visible:s});if(!f)T=null;else if(E===Rr)R?T=f(P({},_),M):!u&&A.current&&p?T=f(P(P({},_),{},{className:p}),M):d||!u&&!p?T=f(P(P({},_),{},{style:{display:"none"}}),M):T=null;else{var N;O===qt?N="prepare":ou(O)?N="active":O===cn&&(N="start");var F=rl(m,"".concat(E,"-").concat(N));T=f(P(P({},_),{},{className:Q(rl(m,E),$($({},F,F&&N),m,typeof m=="string")),style:I}),M)}if(c.isValidElement(T)&&ao(T)){var L=Yo(T);L||(T=c.cloneElement(T,{ref:M}))}return c.createElement(am,{ref:C},T)});return n.displayName="CSSMotion",n}const Sn=bm(tu);var ga="add",ha="keep",pa="remove",Ai="removed";function ym(e){var t;return e&&ve(e)==="object"&&"key"in e?t=e:t={key:e},P(P({},t),{},{key:String(t.key)})}function ba(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return e.map(ym)}function Cm(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],r=[],n=0,o=t.length,i=ba(e),a=ba(t);i.forEach(function(u){for(var d=!1,f=n;f<o;f+=1){var m=a[f];if(m.key===u.key){n<f&&(r=r.concat(a.slice(n,f).map(function(p){return P(P({},p),{},{status:ga})})),n=f),r.push(P(P({},m),{},{status:ha})),n+=1,d=!0;break}}d||r.push(P(P({},u),{},{status:pa}))}),n<o&&(r=r.concat(a.slice(n).map(function(u){return P(P({},u),{},{status:ga})})));var s={};r.forEach(function(u){var d=u.key;s[d]=(s[d]||0)+1});var l=Object.keys(s).filter(function(u){return s[u]>1});return l.forEach(function(u){r=r.filter(function(d){var f=d.key,m=d.status;return f!==u||m!==pa}),r.forEach(function(d){d.key===u&&(d.status=ha)})}),r}var Sm=["component","children","onVisibleChanged","onAllRemoved"],xm=["status"],wm=["eventProps","visible","children","motionName","motionAppear","motionEnter","motionLeave","motionLeaveImmediately","motionDeadline","removeOnLeave","leavedClassName","onAppearPrepare","onAppearStart","onAppearActive","onAppearEnd","onEnterStart","onEnterActive","onEnterEnd","onLeaveStart","onLeaveActive","onLeaveEnd"];function $m(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Sn,r=function(n){yr(i,n);var o=Cr(i);function i(){var a;st(this,i);for(var s=arguments.length,l=new Array(s),u=0;u<s;u++)l[u]=arguments[u];return a=o.call.apply(o,[this].concat(l)),$(fe(a),"state",{keyEntities:[]}),$(fe(a),"removeKey",function(d){a.setState(function(f){var m=f.keyEntities.map(function(p){return p.key!==d?p:P(P({},p),{},{status:Ai})});return{keyEntities:m}},function(){var f=a.state.keyEntities,m=f.filter(function(p){var g=p.status;return g!==Ai}).length;m===0&&a.props.onAllRemoved&&a.props.onAllRemoved()})}),a}return lt(i,[{key:"render",value:function(){var s=this,l=this.state.keyEntities,u=this.props,d=u.component,f=u.children,m=u.onVisibleChanged;u.onAllRemoved;var p=ke(u,Sm),g=d||c.Fragment,h={};return wm.forEach(function(v){h[v]=p[v],delete p[v]}),delete p.keys,c.createElement(g,p,l.map(function(v,y){var b=v.status,C=ke(v,xm),w=b===ga||b===ha;return c.createElement(t,oe({},h,{key:C.key,visible:w,eventProps:C,onVisibleChanged:function(x){m?.(x,{key:C.key}),x||s.removeKey(C.key)}}),function(S,x){return f(P(P({},S),{},{index:y}),x)})}))}}],[{key:"getDerivedStateFromProps",value:function(s,l){var u=s.keys,d=l.keyEntities,f=ba(u),m=Cm(d,f);return{keyEntities:m.filter(function(p){var g=d.find(function(h){var v=h.key;return p.key===v});return!(g&&g.status===Ai&&p.status===pa)})}}}]),i}(c.Component);return $(r,"defaultProps",{component:"div"}),r}$m(tu);const nl=c.createContext(!0);function Em(e){const t=c.useContext(nl),{children:r}=e,[,n]=Sr(),{motion:o}=n,i=c.useRef(!1);return i.current||(i.current=t!==o),i.current?c.createElement(nl.Provider,{value:o},c.createElement(im,{motion:o},r)):r}const Om=()=>null;var Im=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Pm=["getTargetContainer","getPopupContainer","renderEmpty","input","pagination","form","select","button"];let iu;function Rm(){return iu||Jn}function Mm(e){return Object.keys(e).some(t=>t.endsWith("Color"))}const Tm=e=>{const{prefixCls:t,iconPrefixCls:r,theme:n,holderRender:o}=e;t!==void 0&&(iu=t),n&&Mm(n)&&Ov(Rm(),n)},_m=e=>{const{children:t,csp:r,autoInsertSpaceInButton:n,alert:o,anchor:i,form:a,locale:s,componentSize:l,direction:u,space:d,splitter:f,virtual:m,dropdownMatchSelectWidth:p,popupMatchSelectWidth:g,popupOverflow:h,legacyLocale:v,parentContext:y,iconPrefixCls:b,theme:C,componentDisabled:w,segmented:S,statistic:x,spin:E,calendar:O,carousel:I,cascader:R,collapse:A,typography:M,checkbox:T,descriptions:_,divider:N,drawer:F,skeleton:L,steps:B,image:V,layout:K,list:U,mentions:z,modal:X,progress:H,result:k,slider:ae,breadcrumb:se,menu:Y,pagination:G,input:ue,textArea:Se,empty:me,badge:be,radio:he,rate:$e,switch:Ee,transfer:_e,avatar:Oe,message:we,tag:He,table:ce,card:pe,tabs:ye,timeline:te,timePicker:qe,upload:de,notification:ct,tree:Ge,colorPicker:Pe,datePicker:et,rangePicker:ge,flex:xe,wave:Be,dropdown:Re,warning:Me,tour:le,tooltip:Ne,popover:Ze,popconfirm:Xt,floatButtonGroup:Qt,variant:mt,inputNumber:cr,treeSelect:Ft}=e,Ye=c.useCallback((Ae,Fe)=>{const{prefixCls:gt}=e;if(Fe)return Fe;const nt=gt||y.getPrefixCls("");return Ae?`${nt}-${Ae}`:nt},[y.getPrefixCls,e.prefixCls]),Ue=b||y.iconPrefixCls||Ja,De=r||y.csp;Jv(Ue,De);const Z=nm(C,y.theme,{prefixCls:Ye("")}),J={csp:De,autoInsertSpaceInButton:n,alert:o,anchor:i,locale:s||v,direction:u,space:d,splitter:f,virtual:m,popupMatchSelectWidth:g??p,popupOverflow:h,getPrefixCls:Ye,iconPrefixCls:Ue,theme:Z,segmented:S,statistic:x,spin:E,calendar:O,carousel:I,cascader:R,collapse:A,typography:M,checkbox:T,descriptions:_,divider:N,drawer:F,skeleton:L,steps:B,image:V,input:ue,textArea:Se,layout:K,list:U,mentions:z,modal:X,progress:H,result:k,slider:ae,breadcrumb:se,menu:Y,pagination:G,empty:me,badge:be,radio:he,rate:$e,switch:Ee,transfer:_e,avatar:Oe,message:we,tag:He,table:ce,card:pe,tabs:ye,timeline:te,timePicker:qe,upload:de,notification:ct,tree:Ge,colorPicker:Pe,datePicker:et,rangePicker:ge,flex:xe,wave:Be,dropdown:Re,warning:Me,tour:le,tooltip:Ne,popover:Ze,popconfirm:Xt,floatButtonGroup:Qt,variant:mt,inputNumber:cr,treeSelect:Ft},W=Object.assign({},y);Object.keys(J).forEach(Ae=>{J[Ae]!==void 0&&(W[Ae]=J[Ae])}),Pm.forEach(Ae=>{const Fe=e[Ae];Fe&&(W[Ae]=Fe)}),typeof n<"u"&&(W.button=Object.assign({autoInsertSpace:n},W.button));const ne=Qo(()=>W,W,(Ae,Fe)=>{const gt=Object.keys(Ae),nt=Object.keys(Fe);return gt.length!==nt.length||gt.some(ot=>Ae[ot]!==Fe[ot])}),{layer:Xe}=c.useContext(lo),jt=c.useMemo(()=>({prefixCls:Ue,csp:De,layer:Xe?"antd":void 0}),[Ue,De,Xe]);let Te=c.createElement(c.Fragment,null,c.createElement(Om,{dropdownMatchSelectWidth:p}),t);const ur=c.useMemo(()=>{var Ae,Fe,gt,nt;return ln(((Ae=ni.Form)===null||Ae===void 0?void 0:Ae.defaultValidateMessages)||{},((gt=(Fe=ne.locale)===null||Fe===void 0?void 0:Fe.Form)===null||gt===void 0?void 0:gt.defaultValidateMessages)||{},((nt=ne.form)===null||nt===void 0?void 0:nt.validateMessages)||{},a?.validateMessages||{})},[ne,a?.validateMessages]);Object.keys(ur).length>0&&(Te=c.createElement(cv.Provider,{value:ur},Te)),s&&(Te=c.createElement(fv,{locale:s,_ANT_MARK__:dv},Te)),Te=c.createElement(Xa.Provider,{value:jt},Te),l&&(Te=c.createElement(Pv,{size:l},Te)),Te=c.createElement(Em,null,Te);const dr=c.useMemo(()=>{const Ae=Z||{},{algorithm:Fe,token:gt,components:nt,cssVar:ot}=Ae,Zt=Im(Ae,["algorithm","token","components","cssVar"]),Yt=Fe&&(!Array.isArray(Fe)||Fe.length>0)?ko(Fe):Za,xt={};Object.entries(nt||{}).forEach(([Vt,$t])=>{const ht=Object.assign({},$t);"algorithm"in ht&&(ht.algorithm===!0?ht.theme=Yt:(Array.isArray(ht.algorithm)||typeof ht.algorithm=="function")&&(ht.theme=ko(ht.algorithm)),delete ht.algorithm),xt[Vt]=ht});const wt=Object.assign(Object.assign({},pn),gt);return Object.assign(Object.assign({},Zt),{theme:Yt,token:wt,components:xt,override:Object.assign({override:wt},xt),cssVar:ot})},[Z]);return C&&(Te=c.createElement(Ya.Provider,{value:dr},Te)),ne.warning&&(Te=c.createElement(lv.Provider,{value:ne.warning},Te)),w!==void 0&&(Te=c.createElement(Iv,{disabled:w},Te)),c.createElement(vt.Provider,{value:ne},Te)},uo=e=>{const t=c.useContext(vt),r=c.useContext(jc);return c.createElement(_m,Object.assign({parentContext:t,legacyLocale:r},e))};uo.ConfigContext=vt;uo.SizeContext=bn;uo.config=Tm;uo.useConfig=Rv;Object.defineProperty(uo,"SizeContext",{get:()=>bn});function au(e){var t;return e==null||(t=e.getRootNode)===null||t===void 0?void 0:t.call(e)}function Am(e){return au(e)instanceof ShadowRoot}function Wo(e){return Am(e)?au(e):null}function Fm(e){return e.replace(/-(.)/g,function(t,r){return r.toUpperCase()})}function jm(e,t){St(e,"[@ant-design/icons] ".concat(t))}function ol(e){return ve(e)==="object"&&typeof e.name=="string"&&typeof e.theme=="string"&&(ve(e.icon)==="object"||typeof e.icon=="function")}function il(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return Object.keys(e).reduce(function(t,r){var n=e[r];switch(r){case"class":t.className=n,delete t.class;break;default:delete t[r],t[Fm(r)]=n}return t},{})}function ya(e,t,r){return r?re.createElement(e.tag,P(P({key:t},il(e.attrs)),r),(e.children||[]).map(function(n,o){return ya(n,"".concat(t,"-").concat(e.tag,"-").concat(o))})):re.createElement(e.tag,P({key:t},il(e.attrs)),(e.children||[]).map(function(n,o){return ya(n,"".concat(t,"-").concat(e.tag,"-").concat(o))}))}function su(e){return qr(e)[0]}function lu(e){return e?Array.isArray(e)?e:[e]:[]}var Nm=`
.anticon {
  display: inline-flex;
  align-items: center;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.anticon > * {
  line-height: 1;
}

.anticon svg {
  display: inline-block;
}

.anticon::before {
  display: none;
}

.anticon .anticon-icon {
  display: block;
}

.anticon[tabindex] {
  cursor: pointer;
}

.anticon-spin::before,
.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
`,Lm=function(t){var r=c.useContext(Xa),n=r.csp,o=r.prefixCls,i=r.layer,a=Nm;o&&(a=a.replace(/anticon/g,o)),i&&(a="@layer ".concat(i,` {
`).concat(a,`
}`)),c.useEffect(function(){var s=t.current,l=Wo(s);pr(a,"@ant-design-icons",{prepend:!i,csp:n,attachTo:l})},[])},Bm=["icon","className","onClick","style","primaryColor","secondaryColor"],Dn={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1};function zm(e){var t=e.primaryColor,r=e.secondaryColor;Dn.primaryColor=t,Dn.secondaryColor=r||su(t),Dn.calculated=!!r}function km(){return P({},Dn)}var xn=function(t){var r=t.icon,n=t.className,o=t.onClick,i=t.style,a=t.primaryColor,s=t.secondaryColor,l=ke(t,Bm),u=c.useRef(),d=Dn;if(a&&(d={primaryColor:a,secondaryColor:s||su(a)}),Lm(u),jm(ol(r),"icon should be icon definiton, but got ".concat(r)),!ol(r))return null;var f=r;return f&&typeof f.icon=="function"&&(f=P(P({},f),{},{icon:f.icon(d.primaryColor,d.secondaryColor)})),ya(f.icon,"svg-".concat(f.name),P(P({className:n,onClick:o,style:i,"data-icon":f.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},l),{},{ref:u}))};xn.displayName="IconReact";xn.getTwoToneColors=km;xn.setTwoToneColors=zm;function cu(e){var t=lu(e),r=j(t,2),n=r[0],o=r[1];return xn.setTwoToneColors({primaryColor:n,secondaryColor:o})}function Hm(){var e=xn.getTwoToneColors();return e.calculated?[e.primaryColor,e.secondaryColor]:e.primaryColor}var Dm=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];cu(Do.primary);var At=c.forwardRef(function(e,t){var r=e.className,n=e.icon,o=e.spin,i=e.rotate,a=e.tabIndex,s=e.onClick,l=e.twoToneColor,u=ke(e,Dm),d=c.useContext(Xa),f=d.prefixCls,m=f===void 0?"anticon":f,p=d.rootClassName,g=Q(p,m,$($({},"".concat(m,"-").concat(n.name),!!n.name),"".concat(m,"-spin"),!!o||n.name==="loading"),r),h=a;h===void 0&&s&&(h=-1);var v=i?{msTransform:"rotate(".concat(i,"deg)"),transform:"rotate(".concat(i,"deg)")}:void 0,y=lu(l),b=j(y,2),C=b[0],w=b[1];return c.createElement("span",oe({role:"img","aria-label":n.name},u,{ref:t,tabIndex:h,onClick:s,className:g}),c.createElement(xn,{icon:n,primaryColor:C,secondaryColor:w,style:v}))});At.displayName="AntdIcon";At.getTwoToneColor=Hm;At.setTwoToneColor=cu;function uu(e){return e&&re.isValidElement(e)&&e.type===re.Fragment}const Vm=(e,t,r)=>re.isValidElement(e)?re.cloneElement(e,typeof r=="function"?r(e.props||{}):r):t;function wn(e,t){return Vm(e,e,t)}const Wm=e=>{const[,,,,t]=Sr();return t?`${e}-css-var`:""};var xr={ENTER:13,ESC:27,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40},qm={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"}}]},name:"loading",theme:"outlined"},Um=function(t,r){return c.createElement(At,oe({},t,{ref:r,icon:qm}))},Km=c.forwardRef(Um);const du=re.createContext(void 0),zr=100,fu={Modal:zr,Drawer:zr,Popover:zr,Popconfirm:zr,Tooltip:zr,Tour:zr,FloatButton:zr},Gm={SelectLike:50,Dropdown:50,DatePicker:50,Menu:50,ImagePreview:1};function Xm(e){return e in fu}const vu=(e,t)=>{const[,r]=Sr(),n=re.useContext(du),o=Xm(e);let i;if(t!==void 0)i=[t,t];else{let a=n??0;o?a+=(n?0:r.zIndexPopupBase)+fu[e]:a+=Gm[e],i=[n===void 0?t:a,a]}return i},Fi=()=>({height:0,opacity:0}),al=e=>{const{scrollHeight:t}=e;return{height:t,opacity:1}},Qm=e=>({height:e?e.offsetHeight:0}),ji=(e,t)=>t?.deadline===!0||t.propertyName==="height",Zm=(e=Jn)=>({motionName:`${e}-motion-collapse`,onAppearStart:Fi,onEnterStart:Fi,onAppearActive:al,onEnterActive:al,onLeaveStart:Qm,onLeaveActive:Fi,onAppearEnd:ji,onEnterEnd:ji,onLeaveEnd:ji,motionDeadline:500}),Ym=(e,t,r)=>r!==void 0?r:`${e}-${t}`;function wr(e,t){var r=Object.assign({},e);return Array.isArray(t)&&t.forEach(function(n){delete r[n]}),r}const os=function(e){if(!e)return!1;if(e instanceof Element){if(e.offsetParent)return!0;if(e.getBBox){var t=e.getBBox(),r=t.width,n=t.height;if(r||n)return!0}if(e.getBoundingClientRect){var o=e.getBoundingClientRect(),i=o.width,a=o.height;if(i||a)return!0}}return!1},Jm=e=>{const{componentCls:t,colorPrimary:r}=e;return{[t]:{position:"absolute",background:"transparent",pointerEvents:"none",boxSizing:"border-box",color:`var(--wave-color, ${r})`,boxShadow:"0 0 0 0 currentcolor",opacity:.2,"&.wave-motion-appear":{transition:[`box-shadow 0.4s ${e.motionEaseOutCirc}`,`opacity 2s ${e.motionEaseOutCirc}`].join(","),"&-active":{boxShadow:"0 0 0 6px currentcolor",opacity:0},"&.wave-quick":{transition:[`box-shadow ${e.motionDurationSlow} ${e.motionEaseInOut}`,`opacity ${e.motionDurationSlow} ${e.motionEaseInOut}`].join(",")}}}}},eg=Qv("Wave",e=>[Jm(e)]),mu=`${Jn}-wave-target`;function Ni(e){return e&&e!=="#fff"&&e!=="#ffffff"&&e!=="rgb(255, 255, 255)"&&e!=="rgba(255, 255, 255, 1)"&&!/rgba\((?:\d*, ){3}0\)/.test(e)&&e!=="transparent"}function tg(e){const{borderTopColor:t,borderColor:r,backgroundColor:n}=getComputedStyle(e);return Ni(t)?t:Ni(r)?r:Ni(n)?n:null}function Li(e){return Number.isNaN(e)?0:e}const rg=e=>{const{className:t,target:r,component:n,registerUnmount:o}=e,i=c.useRef(null),a=c.useRef(null);c.useEffect(()=>{a.current=o()},[]);const[s,l]=c.useState(null),[u,d]=c.useState([]),[f,m]=c.useState(0),[p,g]=c.useState(0),[h,v]=c.useState(0),[y,b]=c.useState(0),[C,w]=c.useState(!1),S={left:f,top:p,width:h,height:y,borderRadius:u.map(O=>`${O}px`).join(" ")};s&&(S["--wave-color"]=s);function x(){const O=getComputedStyle(r);l(tg(r));const I=O.position==="static",{borderLeftWidth:R,borderTopWidth:A}=O;m(I?r.offsetLeft:Li(-parseFloat(R))),g(I?r.offsetTop:Li(-parseFloat(A))),v(r.offsetWidth),b(r.offsetHeight);const{borderTopLeftRadius:M,borderTopRightRadius:T,borderBottomLeftRadius:_,borderBottomRightRadius:N}=O;d([M,T,N,_].map(F=>Li(parseFloat(F))))}if(c.useEffect(()=>{if(r){const O=Tt(()=>{x(),w(!0)});let I;return typeof ResizeObserver<"u"&&(I=new ResizeObserver(x),I.observe(r)),()=>{Tt.cancel(O),I?.disconnect()}}},[]),!C)return null;const E=(n==="Checkbox"||n==="Radio")&&r?.classList.contains(mu);return c.createElement(Sn,{visible:!0,motionAppear:!0,motionName:"wave-motion",motionDeadline:5e3,onAppearEnd:(O,I)=>{var R,A;if(I.deadline||I.propertyName==="opacity"){const M=(R=i.current)===null||R===void 0?void 0:R.parentElement;(A=a.current)===null||A===void 0||A.call(a).then(()=>{M?.remove()})}return!1}},({className:O},I)=>c.createElement("div",{ref:Zo(i,I),className:Q(t,O,{"wave-quick":E}),style:S}))},ng=(e,t)=>{var r;const{component:n}=t;if(n==="Checkbox"&&!(!((r=e.querySelector("input"))===null||r===void 0)&&r.checked))return;const o=document.createElement("div");o.style.position="absolute",o.style.left="0px",o.style.top="0px",e?.insertBefore(o,e?.firstChild);const i=cd();let a=null;function s(){return a}a=i(c.createElement(rg,Object.assign({},t,{target:e,registerUnmount:s})),o)},og=(e,t,r)=>{const{wave:n}=c.useContext(vt),[,o,i]=Sr(),a=Ct(u=>{const d=e.current;if(n?.disabled||!d)return;const f=d.querySelector(`.${mu}`)||d,{showEffect:m}=n||{};(m||ng)(f,{className:t,token:o,component:r,event:u,hashId:i})}),s=c.useRef(null);return u=>{Tt.cancel(s.current),s.current=Tt(()=>{a(u)})}},ig=e=>{const{children:t,disabled:r,component:n}=e,{getPrefixCls:o}=c.useContext(vt),i=c.useRef(null),a=o("wave"),[,s]=eg(a),l=og(i,Q(a,s),n);if(re.useEffect(()=>{const d=i.current;if(!d||d.nodeType!==1||r)return;const f=m=>{!os(m.target)||!d.getAttribute||d.getAttribute("disabled")||d.disabled||d.className.includes("disabled")||d.className.includes("-leave")||l(m)};return d.addEventListener("click",f,!0),()=>{d.removeEventListener("click",f,!0)}},[r]),!re.isValidElement(t))return t??null;const u=ao(t)?Zo(Yo(t),i):i;return wn(t,{ref:u})},ag=e=>{const t=re.useContext(bn);return re.useMemo(()=>e?typeof e=="string"?e??t:typeof e=="function"?e(t):t:t,[e,t])},gu=c.createContext(null),sg=(e,t)=>{const r=c.useContext(gu),n=c.useMemo(()=>{if(!r)return"";const{compactDirection:o,isFirstItem:i,isLastItem:a}=r,s=o==="vertical"?"-vertical-":"-";return Q(`${e}-compact${s}item`,{[`${e}-compact${s}first-item`]:i,[`${e}-compact${s}last-item`]:a,[`${e}-compact${s}item-rtl`]:t==="rtl"})},[e,t,r]);return{compactSize:r?.compactSize,compactDirection:r?.compactDirection,compactItemClassnames:n}},lg=e=>{const{children:t}=e;return c.createElement(gu.Provider,{value:null},t)};var cg=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const hu=c.createContext(void 0),ug=e=>{const{getPrefixCls:t,direction:r}=c.useContext(vt),{prefixCls:n,size:o,className:i}=e,a=cg(e,["prefixCls","size","className"]),s=t("btn-group",n),[,,l]=Sr(),u=c.useMemo(()=>{switch(o){case"large":return"lg";case"small":return"sm";default:return""}},[o]),d=Q(s,{[`${s}-${u}`]:u,[`${s}-rtl`]:r==="rtl"},i,l);return c.createElement(hu.Provider,{value:o},c.createElement("div",Object.assign({},a,{className:d})))},sl=/^[\u4E00-\u9FA5]{2}$/,Ca=sl.test.bind(sl);function ll(e){return typeof e=="string"}function Bi(e){return e==="text"||e==="link"}function dg(e,t){if(e==null)return;const r=t?" ":"";return typeof e!="string"&&typeof e!="number"&&ll(e.type)&&Ca(e.props.children)?wn(e,{children:e.props.children.split("").join(r)}):ll(e)?Ca(e)?re.createElement("span",null,e.split("").join(r)):re.createElement("span",null,e):uu(e)?re.createElement("span",null,e):e}function fg(e,t){let r=!1;const n=[];return re.Children.forEach(e,o=>{const i=typeof o,a=i==="string"||i==="number";if(r&&a){const s=n.length-1,l=n[s];n[s]=`${l}${o}`}else n.push(o);r=a}),re.Children.map(n,o=>dg(o,t))}["default","primary","danger"].concat(q(Ur));const Sa=c.forwardRef((e,t)=>{const{className:r,style:n,children:o,prefixCls:i}=e,a=Q(`${i}-icon`,r);return re.createElement("span",{ref:t,className:a,style:n},o)}),cl=c.forwardRef((e,t)=>{const{prefixCls:r,className:n,style:o,iconClassName:i}=e,a=Q(`${r}-loading-icon`,n);return re.createElement(Sa,{prefixCls:r,className:a,style:o,ref:t},re.createElement(Km,{className:i}))}),zi=()=>({width:0,opacity:0,transform:"scale(0)"}),ki=e=>({width:e.scrollWidth,opacity:1,transform:"scale(1)"}),vg=e=>{const{prefixCls:t,loading:r,existIcon:n,className:o,style:i,mount:a}=e,s=!!r;return n?re.createElement(cl,{prefixCls:t,className:o,style:i}):re.createElement(Sn,{visible:s,motionName:`${t}-loading-icon-motion`,motionAppear:!a,motionEnter:!a,motionLeave:!a,removeOnLeave:!0,onAppearStart:zi,onAppearActive:ki,onEnterStart:zi,onEnterActive:ki,onLeaveStart:ki,onLeaveActive:zi},({className:l,style:u},d)=>{const f=Object.assign(Object.assign({},i),u);return re.createElement(cl,{prefixCls:t,className:Q(o,l),style:f,ref:d})})},ul=(e,t)=>({[`> span, > ${e}`]:{"&:not(:last-child)":{[`&, & > ${e}`]:{"&:not(:disabled)":{borderInlineEndColor:t}}},"&:not(:first-child)":{[`&, & > ${e}`]:{"&:not(:disabled)":{borderInlineStartColor:t}}}}}),mg=e=>{const{componentCls:t,fontSize:r,lineWidth:n,groupBorderColor:o,colorErrorHover:i}=e;return{[`${t}-group`]:[{position:"relative",display:"inline-flex",[`> span, > ${t}`]:{"&:not(:last-child)":{[`&, & > ${t}`]:{borderStartEndRadius:0,borderEndEndRadius:0}},"&:not(:first-child)":{marginInlineStart:e.calc(n).mul(-1).equal(),[`&, & > ${t}`]:{borderStartStartRadius:0,borderEndStartRadius:0}}},[t]:{position:"relative",zIndex:1,"&:hover, &:focus, &:active":{zIndex:2},"&[disabled]":{zIndex:0}},[`${t}-icon-only`]:{fontSize:r}},ul(`${t}-primary`,o),ul(`${t}-danger`,i)]}};var gg=["b"],hg=["v"],Hi=function(t){return Math.round(Number(t||0))},pg=function(t){if(t instanceof Ve)return t;if(t&&ve(t)==="object"&&"h"in t&&"b"in t){var r=t,n=r.b,o=ke(r,gg);return P(P({},o),{},{v:n})}return typeof t=="string"&&/hsb/.test(t)?t.replace(/hsb/,"hsv"):t},to=function(e){yr(r,e);var t=Cr(r);function r(n){return st(this,r),t.call(this,pg(n))}return lt(r,[{key:"toHsbString",value:function(){var o=this.toHsb(),i=Hi(o.s*100),a=Hi(o.b*100),s=Hi(o.h),l=o.a,u="hsb(".concat(s,", ").concat(i,"%, ").concat(a,"%)"),d="hsba(".concat(s,", ").concat(i,"%, ").concat(a,"%, ").concat(l.toFixed(l===0?0:2),")");return l===1?u:d}},{key:"toHsb",value:function(){var o=this.toHsv(),i=o.v,a=ke(o,hg);return P(P({},a),{},{b:i,a:this.a})}}]),r}(Ve),bg=function(t){return t instanceof to?t:new to(t)};bg("#1677ff");const yg=(e,t)=>e?.replace(/[^\w/]/g,"").slice(0,t?8:6)||"",Cg=(e,t)=>e?yg(e,t):"";let Sg=function(){function e(t){st(this,e);var r;if(this.cleared=!1,t instanceof e){this.metaColor=t.metaColor.clone(),this.colors=(r=t.colors)===null||r===void 0?void 0:r.map(o=>({color:new e(o.color),percent:o.percent})),this.cleared=t.cleared;return}const n=Array.isArray(t);n&&t.length?(this.colors=t.map(({color:o,percent:i})=>({color:new e(o),percent:i})),this.metaColor=new to(this.colors[0].color.metaColor)):this.metaColor=new to(n?"":t),(!t||n&&!this.colors)&&(this.metaColor=this.metaColor.setA(0),this.cleared=!0)}return lt(e,[{key:"toHsb",value:function(){return this.metaColor.toHsb()}},{key:"toHsbString",value:function(){return this.metaColor.toHsbString()}},{key:"toHex",value:function(){return Cg(this.toHexString(),this.metaColor.a<1)}},{key:"toHexString",value:function(){return this.metaColor.toHexString()}},{key:"toRgb",value:function(){return this.metaColor.toRgb()}},{key:"toRgbString",value:function(){return this.metaColor.toRgbString()}},{key:"isGradient",value:function(){return!!this.colors&&!this.cleared}},{key:"getColors",value:function(){return this.colors||[{color:this,percent:0}]}},{key:"toCssString",value:function(){const{colors:r}=this;return r?`linear-gradient(90deg, ${r.map(o=>`${o.color.toRgbString()} ${o.percent}%`).join(", ")})`:this.metaColor.toRgbString()}},{key:"equals",value:function(r){return!r||this.isGradient()!==r.isGradient()?!1:this.isGradient()?this.colors.length===r.colors.length&&this.colors.every((n,o)=>{const i=r.colors[o];return n.percent===i.percent&&n.color.equals(i.color)}):this.toHexString()===r.toHexString()}}])}();var xg={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"}}]},name:"right",theme:"outlined"},wg=function(t,r){return c.createElement(At,oe({},t,{ref:r,icon:xg}))},dl=c.forwardRef(wg);const $g=e=>({[e.componentCls]:{[`${e.antCls}-motion-collapse-legacy`]:{overflow:"hidden","&-active":{transition:`height ${e.motionDurationMid} ${e.motionEaseInOut},
        opacity ${e.motionDurationMid} ${e.motionEaseInOut} !important`}},[`${e.antCls}-motion-collapse`]:{overflow:"hidden",transition:`height ${e.motionDurationMid} ${e.motionEaseInOut},
        opacity ${e.motionDurationMid} ${e.motionEaseInOut} !important`}}}),Eg=e=>({animationDuration:e,animationFillMode:"both"}),Og=e=>({animationDuration:e,animationFillMode:"both"}),pu=(e,t,r,n,o=!1)=>{const i=o?"&":"";return{[`
      ${i}${e}-enter,
      ${i}${e}-appear
    `]:Object.assign(Object.assign({},Eg(n)),{animationPlayState:"paused"}),[`${i}${e}-leave`]:Object.assign(Object.assign({},Og(n)),{animationPlayState:"paused"}),[`
      ${i}${e}-enter${e}-enter-active,
      ${i}${e}-appear${e}-appear-active
    `]:{animationName:t,animationPlayState:"running"},[`${i}${e}-leave${e}-leave-active`]:{animationName:r,animationPlayState:"running",pointerEvents:"none"}}},Ig=new rt("antSlideUpIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1}}),Pg=new rt("antSlideUpOut",{"0%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0}}),Rg=new rt("antSlideDownIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1}}),Mg=new rt("antSlideDownOut",{"0%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0}}),Tg=new rt("antSlideLeftIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1}}),_g=new rt("antSlideLeftOut",{"0%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0}}),Ag=new rt("antSlideRightIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1}}),Fg=new rt("antSlideRightOut",{"0%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0}}),jg={"slide-up":{inKeyframes:Ig,outKeyframes:Pg},"slide-down":{inKeyframes:Rg,outKeyframes:Mg},"slide-left":{inKeyframes:Tg,outKeyframes:_g},"slide-right":{inKeyframes:Ag,outKeyframes:Fg}},fl=(e,t)=>{const{antCls:r}=e,n=`${r}-${t}`,{inKeyframes:o,outKeyframes:i}=jg[t];return[pu(n,o,i,e.motionDurationMid),{[`
      ${n}-enter,
      ${n}-appear
    `]:{transform:"scale(0)",transformOrigin:"0% 0%",opacity:0,animationTimingFunction:e.motionEaseOutQuint,"&-prepare":{transform:"scale(1)"}},[`${n}-leave`]:{animationTimingFunction:e.motionEaseInQuint}}]},Ng=new rt("antZoomIn",{"0%":{transform:"scale(0.2)",opacity:0},"100%":{transform:"scale(1)",opacity:1}}),Lg=new rt("antZoomOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0.2)",opacity:0}}),vl=new rt("antZoomBigIn",{"0%":{transform:"scale(0.8)",opacity:0},"100%":{transform:"scale(1)",opacity:1}}),ml=new rt("antZoomBigOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0.8)",opacity:0}}),Bg=new rt("antZoomUpIn",{"0%":{transform:"scale(0.8)",transformOrigin:"50% 0%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"50% 0%"}}),zg=new rt("antZoomUpOut",{"0%":{transform:"scale(1)",transformOrigin:"50% 0%"},"100%":{transform:"scale(0.8)",transformOrigin:"50% 0%",opacity:0}}),kg=new rt("antZoomLeftIn",{"0%":{transform:"scale(0.8)",transformOrigin:"0% 50%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"0% 50%"}}),Hg=new rt("antZoomLeftOut",{"0%":{transform:"scale(1)",transformOrigin:"0% 50%"},"100%":{transform:"scale(0.8)",transformOrigin:"0% 50%",opacity:0}}),Dg=new rt("antZoomRightIn",{"0%":{transform:"scale(0.8)",transformOrigin:"100% 50%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"100% 50%"}}),Vg=new rt("antZoomRightOut",{"0%":{transform:"scale(1)",transformOrigin:"100% 50%"},"100%":{transform:"scale(0.8)",transformOrigin:"100% 50%",opacity:0}}),Wg=new rt("antZoomDownIn",{"0%":{transform:"scale(0.8)",transformOrigin:"50% 100%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"50% 100%"}}),qg=new rt("antZoomDownOut",{"0%":{transform:"scale(1)",transformOrigin:"50% 100%"},"100%":{transform:"scale(0.8)",transformOrigin:"50% 100%",opacity:0}}),Ug={zoom:{inKeyframes:Ng,outKeyframes:Lg},"zoom-big":{inKeyframes:vl,outKeyframes:ml},"zoom-big-fast":{inKeyframes:vl,outKeyframes:ml},"zoom-left":{inKeyframes:kg,outKeyframes:Hg},"zoom-right":{inKeyframes:Dg,outKeyframes:Vg},"zoom-up":{inKeyframes:Bg,outKeyframes:zg},"zoom-down":{inKeyframes:Wg,outKeyframes:qg}},bu=(e,t)=>{const{antCls:r}=e,n=`${r}-${t}`,{inKeyframes:o,outKeyframes:i}=Ug[t];return[pu(n,o,i,t==="zoom-big-fast"?e.motionDurationFast:e.motionDurationMid),{[`
        ${n}-enter,
        ${n}-appear
      `]:{transform:"scale(0)",opacity:0,animationTimingFunction:e.motionEaseOutCirc,"&-prepare":{transform:"none"}},[`${n}-leave`]:{animationTimingFunction:e.motionEaseInOutCirc}}]},Kg=(e,t)=>{const{r,g:n,b:o,a:i}=e.toRgb(),a=new to(e.toRgbString()).onBackground(t).toHsv();return i<=.5?a.v>.5:r*.299+n*.587+o*.114>192},yu=e=>{const{paddingInline:t,onlyIconSize:r}=e;return sr(e,{buttonPaddingHorizontal:t,buttonPaddingVertical:0,buttonIconOnlyFontSize:r})},Cu=e=>{var t,r,n,o,i,a;const s=(t=e.contentFontSize)!==null&&t!==void 0?t:e.fontSize,l=(r=e.contentFontSizeSM)!==null&&r!==void 0?r:e.fontSize,u=(n=e.contentFontSizeLG)!==null&&n!==void 0?n:e.fontSizeLG,d=(o=e.contentLineHeight)!==null&&o!==void 0?o:jo(s),f=(i=e.contentLineHeightSM)!==null&&i!==void 0?i:jo(l),m=(a=e.contentLineHeightLG)!==null&&a!==void 0?a:jo(u),p=Kg(new Sg(e.colorBgSolid),"#fff")?"#000":"#fff",g=Ur.reduce((h,v)=>Object.assign(Object.assign({},h),{[`${v}ShadowColor`]:`0 ${ie(e.controlOutlineWidth)} 0 ${Ln(e[`${v}1`],e.colorBgContainer)}`}),{});return Object.assign(Object.assign({},g),{fontWeight:400,defaultShadow:`0 ${e.controlOutlineWidth}px 0 ${e.controlTmpOutline}`,primaryShadow:`0 ${e.controlOutlineWidth}px 0 ${e.controlOutline}`,dangerShadow:`0 ${e.controlOutlineWidth}px 0 ${e.colorErrorOutline}`,primaryColor:e.colorTextLightSolid,dangerColor:e.colorTextLightSolid,borderColorDisabled:e.colorBorder,defaultGhostColor:e.colorBgContainer,ghostBg:"transparent",defaultGhostBorderColor:e.colorBgContainer,paddingInline:e.paddingContentHorizontal-e.lineWidth,paddingInlineLG:e.paddingContentHorizontal-e.lineWidth,paddingInlineSM:8-e.lineWidth,onlyIconSize:"inherit",onlyIconSizeSM:"inherit",onlyIconSizeLG:"inherit",groupBorderColor:e.colorPrimaryHover,linkHoverBg:"transparent",textTextColor:e.colorText,textTextHoverColor:e.colorText,textTextActiveColor:e.colorText,textHoverBg:e.colorFillTertiary,defaultColor:e.colorText,defaultBg:e.colorBgContainer,defaultBorderColor:e.colorBorder,defaultBorderColorDisabled:e.colorBorder,defaultHoverBg:e.colorBgContainer,defaultHoverColor:e.colorPrimaryHover,defaultHoverBorderColor:e.colorPrimaryHover,defaultActiveBg:e.colorBgContainer,defaultActiveColor:e.colorPrimaryActive,defaultActiveBorderColor:e.colorPrimaryActive,solidTextColor:p,contentFontSize:s,contentFontSizeSM:l,contentFontSizeLG:u,contentLineHeight:d,contentLineHeightSM:f,contentLineHeightLG:m,paddingBlock:Math.max((e.controlHeight-s*d)/2-e.lineWidth,0),paddingBlockSM:Math.max((e.controlHeightSM-l*f)/2-e.lineWidth,0),paddingBlockLG:Math.max((e.controlHeightLG-u*m)/2-e.lineWidth,0)})},Gg=e=>{const{componentCls:t,iconCls:r,fontWeight:n,opacityLoading:o,motionDurationSlow:i,motionEaseInOut:a,marginXS:s,calc:l}=e;return{[t]:{outline:"none",position:"relative",display:"inline-flex",gap:e.marginXS,alignItems:"center",justifyContent:"center",fontWeight:n,whiteSpace:"nowrap",textAlign:"center",backgroundImage:"none",background:"transparent",border:`${ie(e.lineWidth)} ${e.lineType} transparent`,cursor:"pointer",transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`,userSelect:"none",touchAction:"manipulation",color:e.colorText,"&:disabled > *":{pointerEvents:"none"},[`${t}-icon > svg`]:rs(),"> a":{color:"currentColor"},"&:not(:disabled)":Xv(e),[`&${t}-two-chinese-chars::first-letter`]:{letterSpacing:"0.34em"},[`&${t}-two-chinese-chars > *:not(${r})`]:{marginInlineEnd:"-0.34em",letterSpacing:"0.34em"},[`&${t}-icon-only`]:{paddingInline:0,[`&${t}-compact-item`]:{flex:"none"},[`&${t}-round`]:{width:"auto"}},[`&${t}-loading`]:{opacity:o,cursor:"default"},[`${t}-loading-icon`]:{transition:["width","opacity","margin"].map(u=>`${u} ${i} ${a}`).join(",")},[`&:not(${t}-icon-end)`]:{[`${t}-loading-icon-motion`]:{"&-appear-start, &-enter-start":{marginInlineEnd:l(s).mul(-1).equal()},"&-appear-active, &-enter-active":{marginInlineEnd:0},"&-leave-start":{marginInlineEnd:0},"&-leave-active":{marginInlineEnd:l(s).mul(-1).equal()}}},"&-icon-end":{flexDirection:"row-reverse",[`${t}-loading-icon-motion`]:{"&-appear-start, &-enter-start":{marginInlineStart:l(s).mul(-1).equal()},"&-appear-active, &-enter-active":{marginInlineStart:0},"&-leave-start":{marginInlineStart:0},"&-leave-active":{marginInlineStart:l(s).mul(-1).equal()}}}}}},Su=(e,t,r)=>({[`&:not(:disabled):not(${e}-disabled)`]:{"&:hover":t,"&:active":r}}),Xg=e=>({minWidth:e.controlHeight,paddingInlineStart:0,paddingInlineEnd:0,borderRadius:"50%"}),Qg=e=>({borderRadius:e.controlHeight,paddingInlineStart:e.calc(e.controlHeight).div(2).equal(),paddingInlineEnd:e.calc(e.controlHeight).div(2).equal()}),Zg=e=>({cursor:"not-allowed",borderColor:e.borderColorDisabled,color:e.colorTextDisabled,background:e.colorBgContainerDisabled,boxShadow:"none"}),ii=(e,t,r,n,o,i,a,s)=>({[`&${e}-background-ghost`]:Object.assign(Object.assign({color:r||void 0,background:t,borderColor:n||void 0,boxShadow:"none"},Su(e,Object.assign({background:t},a),Object.assign({background:t},s))),{"&:disabled":{cursor:"not-allowed",color:o||void 0,borderColor:i||void 0}})}),Yg=e=>({[`&:disabled, &${e.componentCls}-disabled`]:Object.assign({},Zg(e))}),Jg=e=>({[`&:disabled, &${e.componentCls}-disabled`]:{cursor:"not-allowed",color:e.colorTextDisabled}}),ai=(e,t,r,n)=>{const i=n&&["link","text"].includes(n)?Jg:Yg;return Object.assign(Object.assign({},i(e)),Su(e.componentCls,t,r))},si=(e,t,r,n,o)=>({[`&${e.componentCls}-variant-solid`]:Object.assign({color:t,background:r},ai(e,n,o))}),li=(e,t,r,n,o)=>({[`&${e.componentCls}-variant-outlined, &${e.componentCls}-variant-dashed`]:Object.assign({borderColor:t,background:r},ai(e,n,o))}),ci=e=>({[`&${e.componentCls}-variant-dashed`]:{borderStyle:"dashed"}}),ui=(e,t,r,n)=>({[`&${e.componentCls}-variant-filled`]:Object.assign({boxShadow:"none",background:t},ai(e,r,n))}),lr=(e,t,r,n,o)=>({[`&${e.componentCls}-variant-${r}`]:Object.assign({color:t,boxShadow:"none"},ai(e,n,o,r))}),eh=e=>{const{componentCls:t}=e;return Ur.reduce((r,n)=>{const o=e[`${n}6`],i=e[`${n}1`],a=e[`${n}5`],s=e[`${n}2`],l=e[`${n}3`],u=e[`${n}7`];return Object.assign(Object.assign({},r),{[`&${t}-color-${n}`]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:o,boxShadow:e[`${n}ShadowColor`]},si(e,e.colorTextLightSolid,o,{background:a},{background:u})),li(e,o,e.colorBgContainer,{color:a,borderColor:a,background:e.colorBgContainer},{color:u,borderColor:u,background:e.colorBgContainer})),ci(e)),ui(e,i,{background:s},{background:l})),lr(e,o,"link",{color:a},{color:u})),lr(e,o,"text",{color:a,background:i},{color:u,background:l}))})},{})},th=e=>Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.defaultColor,boxShadow:e.defaultShadow},si(e,e.solidTextColor,e.colorBgSolid,{color:e.solidTextColor,background:e.colorBgSolidHover},{color:e.solidTextColor,background:e.colorBgSolidActive})),ci(e)),ui(e,e.colorFillTertiary,{background:e.colorFillSecondary},{background:e.colorFill})),ii(e.componentCls,e.ghostBg,e.defaultGhostColor,e.defaultGhostBorderColor,e.colorTextDisabled,e.colorBorder)),lr(e,e.textTextColor,"link",{color:e.colorLinkHover,background:e.linkHoverBg},{color:e.colorLinkActive})),rh=e=>Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorPrimary,boxShadow:e.primaryShadow},li(e,e.colorPrimary,e.colorBgContainer,{color:e.colorPrimaryTextHover,borderColor:e.colorPrimaryHover,background:e.colorBgContainer},{color:e.colorPrimaryTextActive,borderColor:e.colorPrimaryActive,background:e.colorBgContainer})),ci(e)),ui(e,e.colorPrimaryBg,{background:e.colorPrimaryBgHover},{background:e.colorPrimaryBorder})),lr(e,e.colorPrimaryText,"text",{color:e.colorPrimaryTextHover,background:e.colorPrimaryBg},{color:e.colorPrimaryTextActive,background:e.colorPrimaryBorder})),lr(e,e.colorPrimaryText,"link",{color:e.colorPrimaryTextHover,background:e.linkHoverBg},{color:e.colorPrimaryTextActive})),ii(e.componentCls,e.ghostBg,e.colorPrimary,e.colorPrimary,e.colorTextDisabled,e.colorBorder,{color:e.colorPrimaryHover,borderColor:e.colorPrimaryHover},{color:e.colorPrimaryActive,borderColor:e.colorPrimaryActive})),nh=e=>Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorError,boxShadow:e.dangerShadow},si(e,e.dangerColor,e.colorError,{background:e.colorErrorHover},{background:e.colorErrorActive})),li(e,e.colorError,e.colorBgContainer,{color:e.colorErrorHover,borderColor:e.colorErrorBorderHover},{color:e.colorErrorActive,borderColor:e.colorErrorActive})),ci(e)),ui(e,e.colorErrorBg,{background:e.colorErrorBgFilledHover},{background:e.colorErrorBgActive})),lr(e,e.colorError,"text",{color:e.colorErrorHover,background:e.colorErrorBg},{color:e.colorErrorHover,background:e.colorErrorBgActive})),lr(e,e.colorError,"link",{color:e.colorErrorHover},{color:e.colorErrorActive})),ii(e.componentCls,e.ghostBg,e.colorError,e.colorError,e.colorTextDisabled,e.colorBorder,{color:e.colorErrorHover,borderColor:e.colorErrorHover},{color:e.colorErrorActive,borderColor:e.colorErrorActive})),oh=e=>Object.assign(Object.assign({},lr(e,e.colorLink,"link",{color:e.colorLinkHover},{color:e.colorLinkActive})),ii(e.componentCls,e.ghostBg,e.colorInfo,e.colorInfo,e.colorTextDisabled,e.colorBorder,{color:e.colorInfoHover,borderColor:e.colorInfoHover},{color:e.colorInfoActive,borderColor:e.colorInfoActive})),ih=e=>{const{componentCls:t}=e;return Object.assign({[`${t}-color-default`]:th(e),[`${t}-color-primary`]:rh(e),[`${t}-color-dangerous`]:nh(e),[`${t}-color-link`]:oh(e)},eh(e))},ah=e=>Object.assign(Object.assign(Object.assign(Object.assign({},li(e,e.defaultBorderColor,e.defaultBg,{color:e.defaultHoverColor,borderColor:e.defaultHoverBorderColor,background:e.defaultHoverBg},{color:e.defaultActiveColor,borderColor:e.defaultActiveBorderColor,background:e.defaultActiveBg})),lr(e,e.textTextColor,"text",{color:e.textTextHoverColor,background:e.textHoverBg},{color:e.textTextActiveColor,background:e.colorBgTextActive})),si(e,e.primaryColor,e.colorPrimary,{background:e.colorPrimaryHover,color:e.primaryColor},{background:e.colorPrimaryActive,color:e.primaryColor})),lr(e,e.colorLink,"link",{color:e.colorLinkHover,background:e.linkHoverBg},{color:e.colorLinkActive})),is=(e,t="")=>{const{componentCls:r,controlHeight:n,fontSize:o,borderRadius:i,buttonPaddingHorizontal:a,iconCls:s,buttonPaddingVertical:l,buttonIconOnlyFontSize:u}=e;return[{[t]:{fontSize:o,height:n,padding:`${ie(l)} ${ie(a)}`,borderRadius:i,[`&${r}-icon-only`]:{width:n,[s]:{fontSize:u}}}},{[`${r}${r}-circle${t}`]:Xg(e)},{[`${r}${r}-round${t}`]:Qg(e)}]},sh=e=>{const t=sr(e,{fontSize:e.contentFontSize});return is(t,e.componentCls)},lh=e=>{const t=sr(e,{controlHeight:e.controlHeightSM,fontSize:e.contentFontSizeSM,padding:e.paddingXS,buttonPaddingHorizontal:e.paddingInlineSM,buttonPaddingVertical:0,borderRadius:e.borderRadiusSM,buttonIconOnlyFontSize:e.onlyIconSizeSM});return is(t,`${e.componentCls}-sm`)},ch=e=>{const t=sr(e,{controlHeight:e.controlHeightLG,fontSize:e.contentFontSizeLG,buttonPaddingHorizontal:e.paddingInlineLG,buttonPaddingVertical:0,borderRadius:e.borderRadiusLG,buttonIconOnlyFontSize:e.onlyIconSizeLG});return is(t,`${e.componentCls}-lg`)},uh=e=>{const{componentCls:t}=e;return{[t]:{[`&${t}-block`]:{width:"100%"}}}},dh=co("Button",e=>{const t=yu(e);return[Gg(t),sh(t),lh(t),ch(t),uh(t),ih(t),ah(t),mg(t)]},Cu,{unitless:{fontWeight:!0,contentLineHeight:!0,contentLineHeightSM:!0,contentLineHeightLG:!0}});function fh(e,t,r){const{focusElCls:n,focus:o,borderElCls:i}=r,a=i?"> *":"",s=["hover",o?"focus":null,"active"].filter(Boolean).map(l=>`&:${l} ${a}`).join(",");return{[`&-item:not(${t}-last-item)`]:{marginInlineEnd:e.calc(e.lineWidth).mul(-1).equal()},"&-item":Object.assign(Object.assign({[s]:{zIndex:2}},n?{[`&${n}`]:{zIndex:2}}:{}),{[`&[disabled] ${a}`]:{zIndex:0}})}}function vh(e,t,r){const{borderElCls:n}=r,o=n?`> ${n}`:"";return{[`&-item:not(${t}-first-item):not(${t}-last-item) ${o}`]:{borderRadius:0},[`&-item:not(${t}-last-item)${t}-first-item`]:{[`& ${o}, &${e}-sm ${o}, &${e}-lg ${o}`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`&-item:not(${t}-first-item)${t}-last-item`]:{[`& ${o}, &${e}-sm ${o}, &${e}-lg ${o}`]:{borderStartStartRadius:0,borderEndStartRadius:0}}}}function mh(e,t={focus:!0}){const{componentCls:r}=e,n=`${r}-compact`;return{[n]:Object.assign(Object.assign({},fh(e,n,t)),vh(r,n,t))}}function gh(e,t){return{[`&-item:not(${t}-last-item)`]:{marginBottom:e.calc(e.lineWidth).mul(-1).equal()},"&-item":{"&:hover,&:focus,&:active":{zIndex:2},"&[disabled]":{zIndex:0}}}}function hh(e,t){return{[`&-item:not(${t}-first-item):not(${t}-last-item)`]:{borderRadius:0},[`&-item${t}-first-item:not(${t}-last-item)`]:{[`&, &${e}-sm, &${e}-lg`]:{borderEndEndRadius:0,borderEndStartRadius:0}},[`&-item${t}-last-item:not(${t}-first-item)`]:{[`&, &${e}-sm, &${e}-lg`]:{borderStartStartRadius:0,borderStartEndRadius:0}}}}function ph(e){const t=`${e.componentCls}-compact-vertical`;return{[t]:Object.assign(Object.assign({},gh(e,t)),hh(e.componentCls,t))}}const bh=e=>{const{componentCls:t,colorPrimaryHover:r,lineWidth:n,calc:o}=e,i=o(n).mul(-1).equal(),a=s=>{const l=`${t}-compact${s?"-vertical":""}-item${t}-primary:not([disabled])`;return{[`${l} + ${l}::before`]:{position:"absolute",top:s?i:0,insetInlineStart:s?0:i,backgroundColor:r,content:'""',width:s?"100%":n,height:s?n:"100%"}}};return Object.assign(Object.assign({},a()),a(!0))},yh=Zv(["Button","compact"],e=>{const t=yu(e);return[mh(t),ph(t),bh(t)]},Cu);var Ch=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function Sh(e){if(typeof e=="object"&&e){let t=e?.delay;return t=!Number.isNaN(t)&&typeof t=="number"?t:0,{loading:t<=0,delay:t}}return{loading:!!e,delay:0}}const xh={default:["default","outlined"],primary:["primary","solid"],dashed:["default","dashed"],link:["link","link"],text:["default","text"]},wh=re.forwardRef((e,t)=>{var r,n;const{loading:o=!1,prefixCls:i,color:a,variant:s,type:l,danger:u=!1,shape:d="default",size:f,styles:m,disabled:p,className:g,rootClassName:h,children:v,icon:y,iconPosition:b="start",ghost:C=!1,block:w=!1,htmlType:S="button",classNames:x,style:E={},autoInsertSpace:O,autoFocus:I}=e,R=Ch(e,["loading","prefixCls","color","variant","type","danger","shape","size","styles","disabled","className","rootClassName","children","icon","iconPosition","ghost","block","htmlType","classNames","style","autoInsertSpace","autoFocus"]),A=l||"default",{button:M}=re.useContext(vt),[T,_]=c.useMemo(()=>{if(a&&s)return[a,s];if(l||u){const le=xh[A]||[];return u?["danger",le[1]]:le}return M?.color&&M?.variant?[M.color,M.variant]:["default","outlined"]},[l,a,s,u,M?.variant,M?.color]),F=T==="danger"?"dangerous":T,{getPrefixCls:L,direction:B,autoInsertSpace:V,className:K,style:U,classNames:z,styles:X}=es("button"),H=(r=O??V)!==null&&r!==void 0?r:!0,k=L("btn",i),[ae,se,Y]=dh(k),G=c.useContext(Vo),ue=p??G,Se=c.useContext(hu),me=c.useMemo(()=>Sh(o),[o]),[be,he]=c.useState(me.loading),[$e,Ee]=c.useState(!1),_e=c.useRef(null),Oe=io(t,_e),we=c.Children.count(v)===1&&!y&&!Bi(_),He=c.useRef(!0);re.useEffect(()=>(He.current=!1,()=>{He.current=!0}),[]),c.useLayoutEffect(()=>{let le=null;me.delay>0?le=setTimeout(()=>{le=null,he(!0)},me.delay):he(me.loading);function Ne(){le&&(clearTimeout(le),le=null)}return Ne},[me.delay,me.loading]),c.useEffect(()=>{if(!_e.current||!H)return;const le=_e.current.textContent||"";we&&Ca(le)?$e||Ee(!0):$e&&Ee(!1)}),c.useEffect(()=>{I&&_e.current&&_e.current.focus()},[]);const ce=re.useCallback(le=>{var Ne;if(be||ue){le.preventDefault();return}(Ne=e.onClick)===null||Ne===void 0||Ne.call(e,("href"in e,le))},[e.onClick,be,ue]),{compactSize:pe,compactItemClassnames:ye}=sg(k,B),te={large:"lg",small:"sm",middle:void 0},qe=ag(le=>{var Ne,Ze;return(Ze=(Ne=f??pe)!==null&&Ne!==void 0?Ne:Se)!==null&&Ze!==void 0?Ze:le}),de=qe&&(n=te[qe])!==null&&n!==void 0?n:"",ct=be?"loading":y,Ge=wr(R,["navigate"]),Pe=Q(k,se,Y,{[`${k}-${d}`]:d!=="default"&&d,[`${k}-${A}`]:A,[`${k}-dangerous`]:u,[`${k}-color-${F}`]:F,[`${k}-variant-${_}`]:_,[`${k}-${de}`]:de,[`${k}-icon-only`]:!v&&v!==0&&!!ct,[`${k}-background-ghost`]:C&&!Bi(_),[`${k}-loading`]:be,[`${k}-two-chinese-chars`]:$e&&H&&!be,[`${k}-block`]:w,[`${k}-rtl`]:B==="rtl",[`${k}-icon-end`]:b==="end"},ye,g,h,K),et=Object.assign(Object.assign({},U),E),ge=Q(x?.icon,z.icon),xe=Object.assign(Object.assign({},m?.icon||{}),X.icon||{}),Be=y&&!be?re.createElement(Sa,{prefixCls:k,className:ge,style:xe},y):o&&typeof o=="object"&&o.icon?re.createElement(Sa,{prefixCls:k,className:ge,style:xe},o.icon):re.createElement(vg,{existIcon:!!y,prefixCls:k,loading:be,mount:He.current}),Re=v||v===0?fg(v,we&&H):null;if(Ge.href!==void 0)return ae(re.createElement("a",Object.assign({},Ge,{className:Q(Pe,{[`${k}-disabled`]:ue}),href:ue?void 0:Ge.href,style:et,onClick:ce,ref:Oe,tabIndex:ue?-1:0}),Be,Re));let Me=re.createElement("button",Object.assign({},R,{type:S,className:Pe,style:et,onClick:ce,disabled:ue,ref:Oe}),Be,Re,ye&&re.createElement(yh,{prefixCls:k}));return Bi(_)||(Me=re.createElement(ig,{component:"Button",disabled:be},Me)),ae(Me)}),as=wh;as.Group=ug;as.__ANT_BUTTON=!0;var xu=c.createContext(null),gl=[];function $h(e,t){var r=c.useState(function(){if(!_t())return null;var g=document.createElement("div");return g}),n=j(r,1),o=n[0],i=c.useRef(!1),a=c.useContext(xu),s=c.useState(gl),l=j(s,2),u=l[0],d=l[1],f=a||(i.current?void 0:function(g){d(function(h){var v=[g].concat(q(h));return v})});function m(){o.parentElement||document.body.appendChild(o),i.current=!0}function p(){var g;(g=o.parentElement)===null||g===void 0||g.removeChild(o),i.current=!1}return yt(function(){return e?a?a(m):m():p(),p},[e]),yt(function(){u.length&&(u.forEach(function(g){return g()}),d(gl))},[u]),[o,f]}function Eh(e){var t="rc-scrollbar-measure-".concat(Math.random().toString(36).substring(7)),r=document.createElement("div");r.id=t;var n=r.style;n.position="absolute",n.left="0",n.top="0",n.width="100px",n.height="100px",n.overflow="scroll";var o,i;if(e){var a=getComputedStyle(e);n.scrollbarColor=a.scrollbarColor,n.scrollbarWidth=a.scrollbarWidth;var s=getComputedStyle(e,"::-webkit-scrollbar"),l=parseInt(s.width,10),u=parseInt(s.height,10);try{var d=l?"width: ".concat(s.width,";"):"",f=u?"height: ".concat(s.height,";"):"";pr(`
#`.concat(t,`::-webkit-scrollbar {
`).concat(d,`
`).concat(f,`
}`),t)}catch(g){console.error(g),o=l,i=u}}document.body.appendChild(r);var m=e&&o&&!isNaN(o)?o:r.offsetWidth-r.clientWidth,p=e&&i&&!isNaN(i)?i:r.offsetHeight-r.clientHeight;return document.body.removeChild(r),Xn(t),{width:m,height:p}}function Oh(e){return typeof document>"u"||!e||!(e instanceof Element)?{width:0,height:0}:Eh(e)}function Ih(){return document.body.scrollHeight>(window.innerHeight||document.documentElement.clientHeight)&&window.innerWidth>document.body.offsetWidth}var Ph="rc-util-locker-".concat(Date.now()),hl=0;function Rh(e){var t=!!e,r=c.useState(function(){return hl+=1,"".concat(Ph,"_").concat(hl)}),n=j(r,1),o=n[0];yt(function(){if(t){var i=Oh(document.body).width,a=Ih();pr(`
html body {
  overflow-y: hidden;
  `.concat(a?"width: calc(100% - ".concat(i,"px);"):"",`
}`),o)}else Xn(o);return function(){Xn(o)}},[t,o])}var Mh=!1;function Th(e){return Mh}var pl=function(t){return t===!1?!1:!_t()||!t?null:typeof t=="string"?document.querySelector(t):typeof t=="function"?t():t},wu=c.forwardRef(function(e,t){var r=e.open,n=e.autoLock,o=e.getContainer;e.debug;var i=e.autoDestroy,a=i===void 0?!0:i,s=e.children,l=c.useState(r),u=j(l,2),d=u[0],f=u[1],m=d||r;c.useEffect(function(){(a||r)&&f(r)},[r,a]);var p=c.useState(function(){return pl(o)}),g=j(p,2),h=g[0],v=g[1];c.useEffect(function(){var A=pl(o);v(A??null)});var y=$h(m&&!h),b=j(y,2),C=b[0],w=b[1],S=h??C;Rh(n&&r&&_t()&&(S===C||S===document.body));var x=null;if(s&&ao(s)&&t){var E=s;x=E.ref}var O=io(x,t);if(!m||!_t()||h===void 0)return null;var I=S===!1||Th(),R=s;return t&&(R=c.cloneElement(s,{ref:O})),c.createElement(xu.Provider,{value:w},I?R:za.createPortal(R,S))});function _h(){var e=P({},Xo);return e.useId}var bl=0,yl=_h();const $u=yl?function(t){var r=yl();return t||r}:function(t){var r=c.useState("ssr-id"),n=j(r,2),o=n[0],i=n[1];return c.useEffect(function(){var a=bl;bl+=1,i("rc_unique_".concat(a))},[]),t||o};var Dr="RC_FORM_INTERNAL_HOOKS",ze=function(){St(!1,"Can not find FormContext. Please make sure you wrap Field under Form.")},yn=c.createContext({getFieldValue:ze,getFieldsValue:ze,getFieldError:ze,getFieldWarning:ze,getFieldsError:ze,isFieldsTouched:ze,isFieldTouched:ze,isFieldValidating:ze,isFieldsValidating:ze,resetFields:ze,setFields:ze,setFieldValue:ze,setFieldsValue:ze,validateFields:ze,submit:ze,getInternalHooks:function(){return ze(),{dispatch:ze,initEntityValue:ze,registerField:ze,useSubscribe:ze,setInitialValues:ze,destroyForm:ze,setCallbacks:ze,registerWatch:ze,getFields:ze,setValidateMessages:ze,setPreserve:ze,getInitialValue:ze}}}),qo=c.createContext(null);function xa(e){return e==null?[]:Array.isArray(e)?e:[e]}function Ah(e){return e&&!!e._init}function wa(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){var t=JSON.parse(JSON.stringify(this));return t.clone=this.clone,t}}}var $a=wa();function Fh(e){try{return Function.toString.call(e).indexOf("[native code]")!==-1}catch{return typeof e=="function"}}function jh(e,t,r){if(Ha())return Reflect.construct.apply(null,arguments);var n=[null];n.push.apply(n,t);var o=new(e.bind.apply(e,n));return r&&Un(o,r.prototype),o}function Ea(e){var t=typeof Map=="function"?new Map:void 0;return Ea=function(n){if(n===null||!Fh(n))return n;if(typeof n!="function")throw new TypeError("Super expression must either be null or a function");if(t!==void 0){if(t.has(n))return t.get(n);t.set(n,o)}function o(){return jh(n,arguments,Kn(this).constructor)}return o.prototype=Object.create(n.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),Un(o,n)},Ea(e)}var Nh=/%[sdj%]/g,Lh=function(){};function Oa(e){if(!e||!e.length)return null;var t={};return e.forEach(function(r){var n=r.field;t[n]=t[n]||[],t[n].push(r)}),t}function Mt(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var o=0,i=r.length;if(typeof e=="function")return e.apply(null,r);if(typeof e=="string"){var a=e.replace(Nh,function(s){if(s==="%%")return"%";if(o>=i)return s;switch(s){case"%s":return String(r[o++]);case"%d":return Number(r[o++]);case"%j":try{return JSON.stringify(r[o++])}catch{return"[Circular]"}break;default:return s}});return a}return e}function Bh(e){return e==="string"||e==="url"||e==="hex"||e==="email"||e==="date"||e==="pattern"}function at(e,t){return!!(e==null||t==="array"&&Array.isArray(e)&&!e.length||Bh(t)&&typeof e=="string"&&!e)}function zh(e,t,r){var n=[],o=0,i=e.length;function a(s){n.push.apply(n,q(s||[])),o++,o===i&&r(n)}e.forEach(function(s){t(s,a)})}function Cl(e,t,r){var n=0,o=e.length;function i(a){if(a&&a.length){r(a);return}var s=n;n=n+1,s<o?t(e[s],i):r([])}i([])}function kh(e){var t=[];return Object.keys(e).forEach(function(r){t.push.apply(t,q(e[r]||[]))}),t}var Sl=function(e){yr(r,e);var t=Cr(r);function r(n,o){var i;return st(this,r),i=t.call(this,"Async Validation Error"),$(fe(i),"errors",void 0),$(fe(i),"fields",void 0),i.errors=n,i.fields=o,i}return lt(r)}(Ea(Error));function Hh(e,t,r,n,o){if(t.first){var i=new Promise(function(m,p){var g=function(y){return n(y),y.length?p(new Sl(y,Oa(y))):m(o)},h=kh(e);Cl(h,r,g)});return i.catch(function(m){return m}),i}var a=t.firstFields===!0?Object.keys(e):t.firstFields||[],s=Object.keys(e),l=s.length,u=0,d=[],f=new Promise(function(m,p){var g=function(v){if(d.push.apply(d,v),u++,u===l)return n(d),d.length?p(new Sl(d,Oa(d))):m(o)};s.length||(n(d),m(o)),s.forEach(function(h){var v=e[h];a.indexOf(h)!==-1?Cl(v,r,g):zh(v,r,g)})});return f.catch(function(m){return m}),f}function Dh(e){return!!(e&&e.message!==void 0)}function Vh(e,t){for(var r=e,n=0;n<t.length;n++){if(r==null)return r;r=r[t[n]]}return r}function xl(e,t){return function(r){var n;return e.fullFields?n=Vh(t,e.fullFields):n=t[r.field||e.fullField],Dh(r)?(r.field=r.field||e.fullField,r.fieldValue=n,r):{message:typeof r=="function"?r():r,fieldValue:n,field:r.field||e.fullField}}}function wl(e,t){if(t){for(var r in t)if(t.hasOwnProperty(r)){var n=t[r];ve(n)==="object"&&ve(e[r])==="object"?e[r]=P(P({},e[r]),n):e[r]=n}}return e}var en="enum",Wh=function(t,r,n,o,i){t[en]=Array.isArray(t[en])?t[en]:[],t[en].indexOf(r)===-1&&o.push(Mt(i.messages[en],t.fullField,t[en].join(", ")))},qh=function(t,r,n,o,i){if(t.pattern){if(t.pattern instanceof RegExp)t.pattern.lastIndex=0,t.pattern.test(r)||o.push(Mt(i.messages.pattern.mismatch,t.fullField,r,t.pattern));else if(typeof t.pattern=="string"){var a=new RegExp(t.pattern);a.test(r)||o.push(Mt(i.messages.pattern.mismatch,t.fullField,r,t.pattern))}}},Uh=function(t,r,n,o,i){var a=typeof t.len=="number",s=typeof t.min=="number",l=typeof t.max=="number",u=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,d=r,f=null,m=typeof r=="number",p=typeof r=="string",g=Array.isArray(r);if(m?f="number":p?f="string":g&&(f="array"),!f)return!1;g&&(d=r.length),p&&(d=r.replace(u,"_").length),a?d!==t.len&&o.push(Mt(i.messages[f].len,t.fullField,t.len)):s&&!l&&d<t.min?o.push(Mt(i.messages[f].min,t.fullField,t.min)):l&&!s&&d>t.max?o.push(Mt(i.messages[f].max,t.fullField,t.max)):s&&l&&(d<t.min||d>t.max)&&o.push(Mt(i.messages[f].range,t.fullField,t.min,t.max))},Eu=function(t,r,n,o,i,a){t.required&&(!n.hasOwnProperty(t.field)||at(r,a||t.type))&&o.push(Mt(i.messages.required,t.fullField))},Oo;const Kh=function(){if(Oo)return Oo;var e="[a-fA-F\\d:]",t=function(x){return x&&x.includeBoundaries?"(?:(?<=\\s|^)(?=".concat(e,")|(?<=").concat(e,")(?=\\s|$))"):""},r="(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}",n="[a-fA-F\\d]{1,4}",o=["(?:".concat(n,":){7}(?:").concat(n,"|:)"),"(?:".concat(n,":){6}(?:").concat(r,"|:").concat(n,"|:)"),"(?:".concat(n,":){5}(?::").concat(r,"|(?::").concat(n,"){1,2}|:)"),"(?:".concat(n,":){4}(?:(?::").concat(n,"){0,1}:").concat(r,"|(?::").concat(n,"){1,3}|:)"),"(?:".concat(n,":){3}(?:(?::").concat(n,"){0,2}:").concat(r,"|(?::").concat(n,"){1,4}|:)"),"(?:".concat(n,":){2}(?:(?::").concat(n,"){0,3}:").concat(r,"|(?::").concat(n,"){1,5}|:)"),"(?:".concat(n,":){1}(?:(?::").concat(n,"){0,4}:").concat(r,"|(?::").concat(n,"){1,6}|:)"),"(?::(?:(?::".concat(n,"){0,5}:").concat(r,"|(?::").concat(n,"){1,7}|:))")],i="(?:%[0-9a-zA-Z]{1,})?",a="(?:".concat(o.join("|"),")").concat(i),s=new RegExp("(?:^".concat(r,"$)|(?:^").concat(a,"$)")),l=new RegExp("^".concat(r,"$")),u=new RegExp("^".concat(a,"$")),d=function(x){return x&&x.exact?s:new RegExp("(?:".concat(t(x)).concat(r).concat(t(x),")|(?:").concat(t(x)).concat(a).concat(t(x),")"),"g")};d.v4=function(S){return S&&S.exact?l:new RegExp("".concat(t(S)).concat(r).concat(t(S)),"g")},d.v6=function(S){return S&&S.exact?u:new RegExp("".concat(t(S)).concat(a).concat(t(S)),"g")};var f="(?:(?:[a-z]+:)?//)",m="(?:\\S+(?::\\S*)?@)?",p=d.v4().source,g=d.v6().source,h="(?:(?:[a-z\\u00a1-\\uffff0-9][-_]*)*[a-z\\u00a1-\\uffff0-9]+)",v="(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*",y="(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))",b="(?::\\d{2,5})?",C='(?:[/?#][^\\s"]*)?',w="(?:".concat(f,"|www\\.)").concat(m,"(?:localhost|").concat(p,"|").concat(g,"|").concat(h).concat(v).concat(y,")").concat(b).concat(C);return Oo=new RegExp("(?:^".concat(w,"$)"),"i"),Oo};var $l={email:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/,hex:/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i},Bn={integer:function(t){return Bn.number(t)&&parseInt(t,10)===t},float:function(t){return Bn.number(t)&&!Bn.integer(t)},array:function(t){return Array.isArray(t)},regexp:function(t){if(t instanceof RegExp)return!0;try{return!!new RegExp(t)}catch{return!1}},date:function(t){return typeof t.getTime=="function"&&typeof t.getMonth=="function"&&typeof t.getYear=="function"&&!isNaN(t.getTime())},number:function(t){return isNaN(t)?!1:typeof t=="number"},object:function(t){return ve(t)==="object"&&!Bn.array(t)},method:function(t){return typeof t=="function"},email:function(t){return typeof t=="string"&&t.length<=320&&!!t.match($l.email)},url:function(t){return typeof t=="string"&&t.length<=2048&&!!t.match(Kh())},hex:function(t){return typeof t=="string"&&!!t.match($l.hex)}},Gh=function(t,r,n,o,i){if(t.required&&r===void 0){Eu(t,r,n,o,i);return}var a=["integer","float","array","regexp","object","method","email","number","date","url","hex"],s=t.type;a.indexOf(s)>-1?Bn[s](r)||o.push(Mt(i.messages.types[s],t.fullField,t.type)):s&&ve(r)!==t.type&&o.push(Mt(i.messages.types[s],t.fullField,t.type))},Xh=function(t,r,n,o,i){(/^\s+$/.test(r)||r==="")&&o.push(Mt(i.messages.whitespace,t.fullField))};const Ce={required:Eu,whitespace:Xh,type:Gh,range:Uh,enum:Wh,pattern:qh};var Qh=function(t,r,n,o,i){var a=[],s=t.required||!t.required&&o.hasOwnProperty(t.field);if(s){if(at(r)&&!t.required)return n();Ce.required(t,r,o,a,i)}n(a)},Zh=function(t,r,n,o,i){var a=[],s=t.required||!t.required&&o.hasOwnProperty(t.field);if(s){if(r==null&&!t.required)return n();Ce.required(t,r,o,a,i,"array"),r!=null&&(Ce.type(t,r,o,a,i),Ce.range(t,r,o,a,i))}n(a)},Yh=function(t,r,n,o,i){var a=[],s=t.required||!t.required&&o.hasOwnProperty(t.field);if(s){if(at(r)&&!t.required)return n();Ce.required(t,r,o,a,i),r!==void 0&&Ce.type(t,r,o,a,i)}n(a)},Jh=function(t,r,n,o,i){var a=[],s=t.required||!t.required&&o.hasOwnProperty(t.field);if(s){if(at(r,"date")&&!t.required)return n();if(Ce.required(t,r,o,a,i),!at(r,"date")){var l;r instanceof Date?l=r:l=new Date(r),Ce.type(t,l,o,a,i),l&&Ce.range(t,l.getTime(),o,a,i)}}n(a)},ep="enum",tp=function(t,r,n,o,i){var a=[],s=t.required||!t.required&&o.hasOwnProperty(t.field);if(s){if(at(r)&&!t.required)return n();Ce.required(t,r,o,a,i),r!==void 0&&Ce[ep](t,r,o,a,i)}n(a)},rp=function(t,r,n,o,i){var a=[],s=t.required||!t.required&&o.hasOwnProperty(t.field);if(s){if(at(r)&&!t.required)return n();Ce.required(t,r,o,a,i),r!==void 0&&(Ce.type(t,r,o,a,i),Ce.range(t,r,o,a,i))}n(a)},np=function(t,r,n,o,i){var a=[],s=t.required||!t.required&&o.hasOwnProperty(t.field);if(s){if(at(r)&&!t.required)return n();Ce.required(t,r,o,a,i),r!==void 0&&(Ce.type(t,r,o,a,i),Ce.range(t,r,o,a,i))}n(a)},op=function(t,r,n,o,i){var a=[],s=t.required||!t.required&&o.hasOwnProperty(t.field);if(s){if(at(r)&&!t.required)return n();Ce.required(t,r,o,a,i),r!==void 0&&Ce.type(t,r,o,a,i)}n(a)},ip=function(t,r,n,o,i){var a=[],s=t.required||!t.required&&o.hasOwnProperty(t.field);if(s){if(r===""&&(r=void 0),at(r)&&!t.required)return n();Ce.required(t,r,o,a,i),r!==void 0&&(Ce.type(t,r,o,a,i),Ce.range(t,r,o,a,i))}n(a)},ap=function(t,r,n,o,i){var a=[],s=t.required||!t.required&&o.hasOwnProperty(t.field);if(s){if(at(r)&&!t.required)return n();Ce.required(t,r,o,a,i),r!==void 0&&Ce.type(t,r,o,a,i)}n(a)},sp=function(t,r,n,o,i){var a=[],s=t.required||!t.required&&o.hasOwnProperty(t.field);if(s){if(at(r,"string")&&!t.required)return n();Ce.required(t,r,o,a,i),at(r,"string")||Ce.pattern(t,r,o,a,i)}n(a)},lp=function(t,r,n,o,i){var a=[],s=t.required||!t.required&&o.hasOwnProperty(t.field);if(s){if(at(r)&&!t.required)return n();Ce.required(t,r,o,a,i),at(r)||Ce.type(t,r,o,a,i)}n(a)},cp=function(t,r,n,o,i){var a=[],s=Array.isArray(r)?"array":ve(r);Ce.required(t,r,o,a,i,s),n(a)},up=function(t,r,n,o,i){var a=[],s=t.required||!t.required&&o.hasOwnProperty(t.field);if(s){if(at(r,"string")&&!t.required)return n();Ce.required(t,r,o,a,i,"string"),at(r,"string")||(Ce.type(t,r,o,a,i),Ce.range(t,r,o,a,i),Ce.pattern(t,r,o,a,i),t.whitespace===!0&&Ce.whitespace(t,r,o,a,i))}n(a)},Di=function(t,r,n,o,i){var a=t.type,s=[],l=t.required||!t.required&&o.hasOwnProperty(t.field);if(l){if(at(r,a)&&!t.required)return n();Ce.required(t,r,o,s,i,a),at(r,a)||Ce.type(t,r,o,s,i)}n(s)};const Vn={string:up,method:op,number:ip,boolean:Yh,regexp:lp,integer:np,float:rp,array:Zh,object:ap,enum:tp,pattern:sp,date:Jh,url:Di,hex:Di,email:Di,required:cp,any:Qh};var fo=function(){function e(t){st(this,e),$(this,"rules",null),$(this,"_messages",$a),this.define(t)}return lt(e,[{key:"define",value:function(r){var n=this;if(!r)throw new Error("Cannot configure a schema with no rules");if(ve(r)!=="object"||Array.isArray(r))throw new Error("Rules must be an object");this.rules={},Object.keys(r).forEach(function(o){var i=r[o];n.rules[o]=Array.isArray(i)?i:[i]})}},{key:"messages",value:function(r){return r&&(this._messages=wl(wa(),r)),this._messages}},{key:"validate",value:function(r){var n=this,o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:function(){},a=r,s=o,l=i;if(typeof s=="function"&&(l=s,s={}),!this.rules||Object.keys(this.rules).length===0)return l&&l(null,a),Promise.resolve(a);function u(g){var h=[],v={};function y(C){if(Array.isArray(C)){var w;h=(w=h).concat.apply(w,q(C))}else h.push(C)}for(var b=0;b<g.length;b++)y(g[b]);h.length?(v=Oa(h),l(h,v)):l(null,a)}if(s.messages){var d=this.messages();d===$a&&(d=wa()),wl(d,s.messages),s.messages=d}else s.messages=this.messages();var f={},m=s.keys||Object.keys(this.rules);m.forEach(function(g){var h=n.rules[g],v=a[g];h.forEach(function(y){var b=y;typeof b.transform=="function"&&(a===r&&(a=P({},a)),v=a[g]=b.transform(v),v!=null&&(b.type=b.type||(Array.isArray(v)?"array":ve(v)))),typeof b=="function"?b={validator:b}:b=P({},b),b.validator=n.getValidationMethod(b),b.validator&&(b.field=g,b.fullField=b.fullField||g,b.type=n.getType(b),f[g]=f[g]||[],f[g].push({rule:b,value:v,source:a,field:g}))})});var p={};return Hh(f,s,function(g,h){var v=g.rule,y=(v.type==="object"||v.type==="array")&&(ve(v.fields)==="object"||ve(v.defaultField)==="object");y=y&&(v.required||!v.required&&g.value),v.field=g.field;function b(E,O){return P(P({},O),{},{fullField:"".concat(v.fullField,".").concat(E),fullFields:v.fullFields?[].concat(q(v.fullFields),[E]):[E]})}function C(){var E=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],O=Array.isArray(E)?E:[E];!s.suppressWarning&&O.length&&e.warning("async-validator:",O),O.length&&v.message!==void 0&&(O=[].concat(v.message));var I=O.map(xl(v,a));if(s.first&&I.length)return p[v.field]=1,h(I);if(!y)h(I);else{if(v.required&&!g.value)return v.message!==void 0?I=[].concat(v.message).map(xl(v,a)):s.error&&(I=[s.error(v,Mt(s.messages.required,v.field))]),h(I);var R={};v.defaultField&&Object.keys(g.value).map(function(T){R[T]=v.defaultField}),R=P(P({},R),g.rule.fields);var A={};Object.keys(R).forEach(function(T){var _=R[T],N=Array.isArray(_)?_:[_];A[T]=N.map(b.bind(null,T))});var M=new e(A);M.messages(s.messages),g.rule.options&&(g.rule.options.messages=s.messages,g.rule.options.error=s.error),M.validate(g.value,g.rule.options||s,function(T){var _=[];I&&I.length&&_.push.apply(_,q(I)),T&&T.length&&_.push.apply(_,q(T)),h(_.length?_:null)})}}var w;if(v.asyncValidator)w=v.asyncValidator(v,g.value,C,g.source,s);else if(v.validator){try{w=v.validator(v,g.value,C,g.source,s)}catch(E){var S,x;(S=(x=console).error)===null||S===void 0||S.call(x,E),s.suppressValidatorError||setTimeout(function(){throw E},0),C(E.message)}w===!0?C():w===!1?C(typeof v.message=="function"?v.message(v.fullField||v.field):v.message||"".concat(v.fullField||v.field," fails")):w instanceof Array?C(w):w instanceof Error&&C(w.message)}w&&w.then&&w.then(function(){return C()},function(E){return C(E)})},function(g){u(g)},a)}},{key:"getType",value:function(r){if(r.type===void 0&&r.pattern instanceof RegExp&&(r.type="pattern"),typeof r.validator!="function"&&r.type&&!Vn.hasOwnProperty(r.type))throw new Error(Mt("Unknown rule type %s",r.type));return r.type||"string"}},{key:"getValidationMethod",value:function(r){if(typeof r.validator=="function")return r.validator;var n=Object.keys(r),o=n.indexOf("message");return o!==-1&&n.splice(o,1),n.length===1&&n[0]==="required"?Vn.required:Vn[this.getType(r)]||void 0}}]),e}();$(fo,"register",function(t,r){if(typeof r!="function")throw new Error("Cannot register a validator by type, validator is not a function");Vn[t]=r});$(fo,"warning",Lh);$(fo,"messages",$a);$(fo,"validators",Vn);var Rt="'${name}' is not a valid ${type}",Ou={default:"Validation error on field '${name}'",required:"'${name}' is required",enum:"'${name}' must be one of [${enum}]",whitespace:"'${name}' cannot be empty",date:{format:"'${name}' is invalid for format date",parse:"'${name}' could not be parsed as date",invalid:"'${name}' is invalid date"},types:{string:Rt,method:Rt,array:Rt,object:Rt,number:Rt,date:Rt,boolean:Rt,integer:Rt,float:Rt,regexp:Rt,email:Rt,url:Rt,hex:Rt},string:{len:"'${name}' must be exactly ${len} characters",min:"'${name}' must be at least ${min} characters",max:"'${name}' cannot be longer than ${max} characters",range:"'${name}' must be between ${min} and ${max} characters"},number:{len:"'${name}' must equal ${len}",min:"'${name}' cannot be less than ${min}",max:"'${name}' cannot be greater than ${max}",range:"'${name}' must be between ${min} and ${max}"},array:{len:"'${name}' must be exactly ${len} in length",min:"'${name}' cannot be less than ${min} in length",max:"'${name}' cannot be greater than ${max} in length",range:"'${name}' must be between ${min} and ${max} in length"},pattern:{mismatch:"'${name}' does not match pattern ${pattern}"}},El=fo;function dp(e,t){return e.replace(/\\?\$\{\w+\}/g,function(r){if(r.startsWith("\\"))return r.slice(1);var n=r.slice(2,-1);return t[n]})}var Ol="CODE_LOGIC_ERROR";function Ia(e,t,r,n,o){return Pa.apply(this,arguments)}function Pa(){return Pa=oo(ar().mark(function e(t,r,n,o,i){var a,s,l,u,d,f,m,p,g;return ar().wrap(function(v){for(;;)switch(v.prev=v.next){case 0:return a=P({},n),delete a.ruleIndex,El.warning=function(){},a.validator&&(s=a.validator,a.validator=function(){try{return s.apply(void 0,arguments)}catch(y){return console.error(y),Promise.reject(Ol)}}),l=null,a&&a.type==="array"&&a.defaultField&&(l=a.defaultField,delete a.defaultField),u=new El($({},t,[a])),d=ln(Ou,o.validateMessages),u.messages(d),f=[],v.prev=10,v.next=13,Promise.resolve(u.validate($({},t,r),P({},o)));case 13:v.next=18;break;case 15:v.prev=15,v.t0=v.catch(10),v.t0.errors&&(f=v.t0.errors.map(function(y,b){var C=y.message,w=C===Ol?d.default:C;return c.isValidElement(w)?c.cloneElement(w,{key:"error_".concat(b)}):w}));case 18:if(!(!f.length&&l)){v.next=23;break}return v.next=21,Promise.all(r.map(function(y,b){return Ia("".concat(t,".").concat(b),y,l,o,i)}));case 21:return m=v.sent,v.abrupt("return",m.reduce(function(y,b){return[].concat(q(y),q(b))},[]));case 23:return p=P(P({},n),{},{name:t,enum:(n.enum||[]).join(", ")},i),g=f.map(function(y){return typeof y=="string"?dp(y,p):y}),v.abrupt("return",g);case 26:case"end":return v.stop()}},e,null,[[10,15]])})),Pa.apply(this,arguments)}function fp(e,t,r,n,o,i){var a=e.join("."),s=r.map(function(d,f){var m=d.validator,p=P(P({},d),{},{ruleIndex:f});return m&&(p.validator=function(g,h,v){var y=!1,b=function(){for(var S=arguments.length,x=new Array(S),E=0;E<S;E++)x[E]=arguments[E];Promise.resolve().then(function(){St(!y,"Your validator function has already return a promise. `callback` will be ignored."),y||v.apply(void 0,x)})},C=m(g,h,b);y=C&&typeof C.then=="function"&&typeof C.catch=="function",St(y,"`callback` is deprecated. Please return a promise instead."),y&&C.then(function(){v()}).catch(function(w){v(w||" ")})}),p}).sort(function(d,f){var m=d.warningOnly,p=d.ruleIndex,g=f.warningOnly,h=f.ruleIndex;return!!m==!!g?p-h:m?1:-1}),l;if(o===!0)l=new Promise(function(){var d=oo(ar().mark(function f(m,p){var g,h,v;return ar().wrap(function(b){for(;;)switch(b.prev=b.next){case 0:g=0;case 1:if(!(g<s.length)){b.next=12;break}return h=s[g],b.next=5,Ia(a,t,h,n,i);case 5:if(v=b.sent,!v.length){b.next=9;break}return p([{errors:v,rule:h}]),b.abrupt("return");case 9:g+=1,b.next=1;break;case 12:m([]);case 13:case"end":return b.stop()}},f)}));return function(f,m){return d.apply(this,arguments)}}());else{var u=s.map(function(d){return Ia(a,t,d,n,i).then(function(f){return{errors:f,rule:d}})});l=(o?mp(u):vp(u)).then(function(d){return Promise.reject(d)})}return l.catch(function(d){return d}),l}function vp(e){return Ra.apply(this,arguments)}function Ra(){return Ra=oo(ar().mark(function e(t){return ar().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",Promise.all(t).then(function(o){var i,a=(i=[]).concat.apply(i,q(o));return a}));case 1:case"end":return n.stop()}},e)})),Ra.apply(this,arguments)}function mp(e){return Ma.apply(this,arguments)}function Ma(){return Ma=oo(ar().mark(function e(t){var r;return ar().wrap(function(o){for(;;)switch(o.prev=o.next){case 0:return r=0,o.abrupt("return",new Promise(function(i){t.forEach(function(a){a.then(function(s){s.errors.length&&i([s]),r+=1,r===t.length&&i([])})})}));case 2:case"end":return o.stop()}},e)})),Ma.apply(this,arguments)}function Je(e){return xa(e)}function Il(e,t){var r={};return t.forEach(function(n){var o=ir(e,n);r=Wt(r,n,o)}),r}function fn(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;return e&&e.some(function(n){return Iu(t,n,r)})}function Iu(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;return!e||!t||!r&&e.length!==t.length?!1:t.every(function(n,o){return e[o]===n})}function gp(e,t){if(e===t)return!0;if(!e&&t||e&&!t||!e||!t||ve(e)!=="object"||ve(t)!=="object")return!1;var r=Object.keys(e),n=Object.keys(t),o=new Set([].concat(r,n));return q(o).every(function(i){var a=e[i],s=t[i];return typeof a=="function"&&typeof s=="function"?!0:a===s})}function hp(e){var t=arguments.length<=1?void 0:arguments[1];return t&&t.target&&ve(t.target)==="object"&&e in t.target?t.target[e]:t}function Pl(e,t,r){var n=e.length;if(t<0||t>=n||r<0||r>=n)return e;var o=e[t],i=t-r;return i>0?[].concat(q(e.slice(0,r)),[o],q(e.slice(r,t)),q(e.slice(t+1,n))):i<0?[].concat(q(e.slice(0,t)),q(e.slice(t+1,r+1)),[o],q(e.slice(r+1,n))):e}var pp=["name"],zt=[];function Vi(e,t,r,n,o,i){return typeof e=="function"?e(t,r,"source"in i?{source:i.source}:{}):n!==o}var ss=function(e){yr(r,e);var t=Cr(r);function r(n){var o;if(st(this,r),o=t.call(this,n),$(fe(o),"state",{resetCount:0}),$(fe(o),"cancelRegisterFunc",null),$(fe(o),"mounted",!1),$(fe(o),"touched",!1),$(fe(o),"dirty",!1),$(fe(o),"validatePromise",void 0),$(fe(o),"prevValidating",void 0),$(fe(o),"errors",zt),$(fe(o),"warnings",zt),$(fe(o),"cancelRegister",function(){var l=o.props,u=l.preserve,d=l.isListField,f=l.name;o.cancelRegisterFunc&&o.cancelRegisterFunc(d,u,Je(f)),o.cancelRegisterFunc=null}),$(fe(o),"getNamePath",function(){var l=o.props,u=l.name,d=l.fieldContext,f=d.prefixName,m=f===void 0?[]:f;return u!==void 0?[].concat(q(m),q(u)):[]}),$(fe(o),"getRules",function(){var l=o.props,u=l.rules,d=u===void 0?[]:u,f=l.fieldContext;return d.map(function(m){return typeof m=="function"?m(f):m})}),$(fe(o),"refresh",function(){o.mounted&&o.setState(function(l){var u=l.resetCount;return{resetCount:u+1}})}),$(fe(o),"metaCache",null),$(fe(o),"triggerMetaEvent",function(l){var u=o.props.onMetaChange;if(u){var d=P(P({},o.getMeta()),{},{destroy:l});Qn(o.metaCache,d)||u(d),o.metaCache=d}else o.metaCache=null}),$(fe(o),"onStoreChange",function(l,u,d){var f=o.props,m=f.shouldUpdate,p=f.dependencies,g=p===void 0?[]:p,h=f.onReset,v=d.store,y=o.getNamePath(),b=o.getValue(l),C=o.getValue(v),w=u&&fn(u,y);switch(d.type==="valueUpdate"&&d.source==="external"&&!Qn(b,C)&&(o.touched=!0,o.dirty=!0,o.validatePromise=null,o.errors=zt,o.warnings=zt,o.triggerMetaEvent()),d.type){case"reset":if(!u||w){o.touched=!1,o.dirty=!1,o.validatePromise=void 0,o.errors=zt,o.warnings=zt,o.triggerMetaEvent(),h?.(),o.refresh();return}break;case"remove":{if(m&&Vi(m,l,v,b,C,d)){o.reRender();return}break}case"setField":{var S=d.data;if(w){"touched"in S&&(o.touched=S.touched),"validating"in S&&!("originRCField"in S)&&(o.validatePromise=S.validating?Promise.resolve([]):null),"errors"in S&&(o.errors=S.errors||zt),"warnings"in S&&(o.warnings=S.warnings||zt),o.dirty=!0,o.triggerMetaEvent(),o.reRender();return}else if("value"in S&&fn(u,y,!0)){o.reRender();return}if(m&&!y.length&&Vi(m,l,v,b,C,d)){o.reRender();return}break}case"dependenciesUpdate":{var x=g.map(Je);if(x.some(function(E){return fn(d.relatedFields,E)})){o.reRender();return}break}default:if(w||(!g.length||y.length||m)&&Vi(m,l,v,b,C,d)){o.reRender();return}break}m===!0&&o.reRender()}),$(fe(o),"validateRules",function(l){var u=o.getNamePath(),d=o.getValue(),f=l||{},m=f.triggerName,p=f.validateOnly,g=p===void 0?!1:p,h=Promise.resolve().then(oo(ar().mark(function v(){var y,b,C,w,S,x,E;return ar().wrap(function(I){for(;;)switch(I.prev=I.next){case 0:if(o.mounted){I.next=2;break}return I.abrupt("return",[]);case 2:if(y=o.props,b=y.validateFirst,C=b===void 0?!1:b,w=y.messageVariables,S=y.validateDebounce,x=o.getRules(),m&&(x=x.filter(function(R){return R}).filter(function(R){var A=R.validateTrigger;if(!A)return!0;var M=xa(A);return M.includes(m)})),!(S&&m)){I.next=10;break}return I.next=8,new Promise(function(R){setTimeout(R,S)});case 8:if(o.validatePromise===h){I.next=10;break}return I.abrupt("return",[]);case 10:return E=fp(u,d,x,l,C,w),E.catch(function(R){return R}).then(function(){var R=arguments.length>0&&arguments[0]!==void 0?arguments[0]:zt;if(o.validatePromise===h){var A;o.validatePromise=null;var M=[],T=[];(A=R.forEach)===null||A===void 0||A.call(R,function(_){var N=_.rule.warningOnly,F=_.errors,L=F===void 0?zt:F;N?T.push.apply(T,q(L)):M.push.apply(M,q(L))}),o.errors=M,o.warnings=T,o.triggerMetaEvent(),o.reRender()}}),I.abrupt("return",E);case 13:case"end":return I.stop()}},v)})));return g||(o.validatePromise=h,o.dirty=!0,o.errors=zt,o.warnings=zt,o.triggerMetaEvent(),o.reRender()),h}),$(fe(o),"isFieldValidating",function(){return!!o.validatePromise}),$(fe(o),"isFieldTouched",function(){return o.touched}),$(fe(o),"isFieldDirty",function(){if(o.dirty||o.props.initialValue!==void 0)return!0;var l=o.props.fieldContext,u=l.getInternalHooks(Dr),d=u.getInitialValue;return d(o.getNamePath())!==void 0}),$(fe(o),"getErrors",function(){return o.errors}),$(fe(o),"getWarnings",function(){return o.warnings}),$(fe(o),"isListField",function(){return o.props.isListField}),$(fe(o),"isList",function(){return o.props.isList}),$(fe(o),"isPreserve",function(){return o.props.preserve}),$(fe(o),"getMeta",function(){o.prevValidating=o.isFieldValidating();var l={touched:o.isFieldTouched(),validating:o.prevValidating,errors:o.errors,warnings:o.warnings,name:o.getNamePath(),validated:o.validatePromise===null};return l}),$(fe(o),"getOnlyChild",function(l){if(typeof l=="function"){var u=o.getMeta();return P(P({},o.getOnlyChild(l(o.getControlled(),u,o.props.fieldContext))),{},{isFunction:!0})}var d=Wr(l);return d.length!==1||!c.isValidElement(d[0])?{child:d,isFunction:!1}:{child:d[0],isFunction:!1}}),$(fe(o),"getValue",function(l){var u=o.props.fieldContext.getFieldsValue,d=o.getNamePath();return ir(l||u(!0),d)}),$(fe(o),"getControlled",function(){var l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},u=o.props,d=u.name,f=u.trigger,m=u.validateTrigger,p=u.getValueFromEvent,g=u.normalize,h=u.valuePropName,v=u.getValueProps,y=u.fieldContext,b=m!==void 0?m:y.validateTrigger,C=o.getNamePath(),w=y.getInternalHooks,S=y.getFieldsValue,x=w(Dr),E=x.dispatch,O=o.getValue(),I=v||function(_){return $({},h,_)},R=l[f],A=d!==void 0?I(O):{},M=P(P({},l),A);M[f]=function(){o.touched=!0,o.dirty=!0,o.triggerMetaEvent();for(var _,N=arguments.length,F=new Array(N),L=0;L<N;L++)F[L]=arguments[L];p?_=p.apply(void 0,F):_=hp.apply(void 0,[h].concat(F)),g&&(_=g(_,O,S(!0))),_!==O&&E({type:"updateValue",namePath:C,value:_}),R&&R.apply(void 0,F)};var T=xa(b||[]);return T.forEach(function(_){var N=M[_];M[_]=function(){N&&N.apply(void 0,arguments);var F=o.props.rules;F&&F.length&&E({type:"validateField",namePath:C,triggerName:_})}}),M}),n.fieldContext){var i=n.fieldContext.getInternalHooks,a=i(Dr),s=a.initEntityValue;s(fe(o))}return o}return lt(r,[{key:"componentDidMount",value:function(){var o=this.props,i=o.shouldUpdate,a=o.fieldContext;if(this.mounted=!0,a){var s=a.getInternalHooks,l=s(Dr),u=l.registerField;this.cancelRegisterFunc=u(this)}i===!0&&this.reRender()}},{key:"componentWillUnmount",value:function(){this.cancelRegister(),this.triggerMetaEvent(!0),this.mounted=!1}},{key:"reRender",value:function(){this.mounted&&this.forceUpdate()}},{key:"render",value:function(){var o=this.state.resetCount,i=this.props.children,a=this.getOnlyChild(i),s=a.child,l=a.isFunction,u;return l?u=s:c.isValidElement(s)?u=c.cloneElement(s,this.getControlled(s.props)):(St(!s,"`children` of Field is not validate ReactElement."),u=s),c.createElement(c.Fragment,{key:o},u)}}]),r}(c.Component);$(ss,"contextType",yn);$(ss,"defaultProps",{trigger:"onChange",valuePropName:"value"});function Pu(e){var t,r=e.name,n=ke(e,pp),o=c.useContext(yn),i=c.useContext(qo),a=r!==void 0?Je(r):void 0,s=(t=n.isListField)!==null&&t!==void 0?t:!!i,l="keep";return s||(l="_".concat((a||[]).join("_"))),c.createElement(ss,oe({key:l,name:a,isListField:s},n,{fieldContext:o}))}function bp(e){var t=e.name,r=e.initialValue,n=e.children,o=e.rules,i=e.validateTrigger,a=e.isListField,s=c.useContext(yn),l=c.useContext(qo),u=c.useRef({keys:[],id:0}),d=u.current,f=c.useMemo(function(){var h=Je(s.prefixName)||[];return[].concat(q(h),q(Je(t)))},[s.prefixName,t]),m=c.useMemo(function(){return P(P({},s),{},{prefixName:f})},[s,f]),p=c.useMemo(function(){return{getKey:function(v){var y=f.length,b=v[y];return[d.keys[b],v.slice(y+1)]}}},[f]);if(typeof n!="function")return St(!1,"Form.List only accepts function as children."),null;var g=function(v,y,b){var C=b.source;return C==="internal"?!1:v!==y};return c.createElement(qo.Provider,{value:p},c.createElement(yn.Provider,{value:m},c.createElement(Pu,{name:[],shouldUpdate:g,rules:o,validateTrigger:i,initialValue:r,isList:!0,isListField:a??!!l},function(h,v){var y=h.value,b=y===void 0?[]:y,C=h.onChange,w=s.getFieldValue,S=function(){var I=w(f||[]);return I||[]},x={add:function(I,R){var A=S();R>=0&&R<=A.length?(d.keys=[].concat(q(d.keys.slice(0,R)),[d.id],q(d.keys.slice(R))),C([].concat(q(A.slice(0,R)),[I],q(A.slice(R))))):(d.keys=[].concat(q(d.keys),[d.id]),C([].concat(q(A),[I]))),d.id+=1},remove:function(I){var R=S(),A=new Set(Array.isArray(I)?I:[I]);A.size<=0||(d.keys=d.keys.filter(function(M,T){return!A.has(T)}),C(R.filter(function(M,T){return!A.has(T)})))},move:function(I,R){if(I!==R){var A=S();I<0||I>=A.length||R<0||R>=A.length||(d.keys=Pl(d.keys,I,R),C(Pl(A,I,R)))}}},E=b||[];return Array.isArray(E)||(E=[]),n(E.map(function(O,I){var R=d.keys[I];return R===void 0&&(d.keys[I]=d.id,R=d.keys[I],d.id+=1),{name:I,key:R,isListField:!0}}),x,v)})))}function yp(e){var t=!1,r=e.length,n=[];return e.length?new Promise(function(o,i){e.forEach(function(a,s){a.catch(function(l){return t=!0,l}).then(function(l){r-=1,n[s]=l,!(r>0)&&(t&&i(n),o(n))})})}):Promise.resolve([])}var Ru="__@field_split__";function Wi(e){return e.map(function(t){return"".concat(ve(t),":").concat(t)}).join(Ru)}var tn=function(){function e(){st(this,e),$(this,"kvs",new Map)}return lt(e,[{key:"set",value:function(r,n){this.kvs.set(Wi(r),n)}},{key:"get",value:function(r){return this.kvs.get(Wi(r))}},{key:"update",value:function(r,n){var o=this.get(r),i=n(o);i?this.set(r,i):this.delete(r)}},{key:"delete",value:function(r){this.kvs.delete(Wi(r))}},{key:"map",value:function(r){return q(this.kvs.entries()).map(function(n){var o=j(n,2),i=o[0],a=o[1],s=i.split(Ru);return r({key:s.map(function(l){var u=l.match(/^([^:]*):(.*)$/),d=j(u,3),f=d[1],m=d[2];return f==="number"?Number(m):m}),value:a})})}},{key:"toJSON",value:function(){var r={};return this.map(function(n){var o=n.key,i=n.value;return r[o.join(".")]=i,null}),r}}]),e}(),Cp=["name"],Sp=lt(function e(t){var r=this;st(this,e),$(this,"formHooked",!1),$(this,"forceRootUpdate",void 0),$(this,"subscribable",!0),$(this,"store",{}),$(this,"fieldEntities",[]),$(this,"initialValues",{}),$(this,"callbacks",{}),$(this,"validateMessages",null),$(this,"preserve",null),$(this,"lastValidatePromise",null),$(this,"getForm",function(){return{getFieldValue:r.getFieldValue,getFieldsValue:r.getFieldsValue,getFieldError:r.getFieldError,getFieldWarning:r.getFieldWarning,getFieldsError:r.getFieldsError,isFieldsTouched:r.isFieldsTouched,isFieldTouched:r.isFieldTouched,isFieldValidating:r.isFieldValidating,isFieldsValidating:r.isFieldsValidating,resetFields:r.resetFields,setFields:r.setFields,setFieldValue:r.setFieldValue,setFieldsValue:r.setFieldsValue,validateFields:r.validateFields,submit:r.submit,_init:!0,getInternalHooks:r.getInternalHooks}}),$(this,"getInternalHooks",function(n){return n===Dr?(r.formHooked=!0,{dispatch:r.dispatch,initEntityValue:r.initEntityValue,registerField:r.registerField,useSubscribe:r.useSubscribe,setInitialValues:r.setInitialValues,destroyForm:r.destroyForm,setCallbacks:r.setCallbacks,setValidateMessages:r.setValidateMessages,getFields:r.getFields,setPreserve:r.setPreserve,getInitialValue:r.getInitialValue,registerWatch:r.registerWatch}):(St(!1,"`getInternalHooks` is internal usage. Should not call directly."),null)}),$(this,"useSubscribe",function(n){r.subscribable=n}),$(this,"prevWithoutPreserves",null),$(this,"setInitialValues",function(n,o){if(r.initialValues=n||{},o){var i,a=ln(n,r.store);(i=r.prevWithoutPreserves)===null||i===void 0||i.map(function(s){var l=s.key;a=Wt(a,l,ir(n,l))}),r.prevWithoutPreserves=null,r.updateStore(a)}}),$(this,"destroyForm",function(n){if(n)r.updateStore({});else{var o=new tn;r.getFieldEntities(!0).forEach(function(i){r.isMergedPreserve(i.isPreserve())||o.set(i.getNamePath(),!0)}),r.prevWithoutPreserves=o}}),$(this,"getInitialValue",function(n){var o=ir(r.initialValues,n);return n.length?ln(o):o}),$(this,"setCallbacks",function(n){r.callbacks=n}),$(this,"setValidateMessages",function(n){r.validateMessages=n}),$(this,"setPreserve",function(n){r.preserve=n}),$(this,"watchList",[]),$(this,"registerWatch",function(n){return r.watchList.push(n),function(){r.watchList=r.watchList.filter(function(o){return o!==n})}}),$(this,"notifyWatch",function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];if(r.watchList.length){var o=r.getFieldsValue(),i=r.getFieldsValue(!0);r.watchList.forEach(function(a){a(o,i,n)})}}),$(this,"timeoutId",null),$(this,"warningUnhooked",function(){}),$(this,"updateStore",function(n){r.store=n}),$(this,"getFieldEntities",function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;return n?r.fieldEntities.filter(function(o){return o.getNamePath().length}):r.fieldEntities}),$(this,"getFieldsMap",function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,o=new tn;return r.getFieldEntities(n).forEach(function(i){var a=i.getNamePath();o.set(a,i)}),o}),$(this,"getFieldEntitiesForNamePathList",function(n){if(!n)return r.getFieldEntities(!0);var o=r.getFieldsMap(!0);return n.map(function(i){var a=Je(i);return o.get(a)||{INVALIDATE_NAME_PATH:Je(i)}})}),$(this,"getFieldsValue",function(n,o){r.warningUnhooked();var i,a,s;if(n===!0||Array.isArray(n)?(i=n,a=o):n&&ve(n)==="object"&&(s=n.strict,a=n.filter),i===!0&&!a)return r.store;var l=r.getFieldEntitiesForNamePathList(Array.isArray(i)?i:null),u=[];return l.forEach(function(d){var f,m,p="INVALIDATE_NAME_PATH"in d?d.INVALIDATE_NAME_PATH:d.getNamePath();if(s){var g,h;if((g=(h=d).isList)!==null&&g!==void 0&&g.call(h))return}else if(!i&&(f=(m=d).isListField)!==null&&f!==void 0&&f.call(m))return;if(!a)u.push(p);else{var v="getMeta"in d?d.getMeta():null;a(v)&&u.push(p)}}),Il(r.store,u.map(Je))}),$(this,"getFieldValue",function(n){r.warningUnhooked();var o=Je(n);return ir(r.store,o)}),$(this,"getFieldsError",function(n){r.warningUnhooked();var o=r.getFieldEntitiesForNamePathList(n);return o.map(function(i,a){return i&&!("INVALIDATE_NAME_PATH"in i)?{name:i.getNamePath(),errors:i.getErrors(),warnings:i.getWarnings()}:{name:Je(n[a]),errors:[],warnings:[]}})}),$(this,"getFieldError",function(n){r.warningUnhooked();var o=Je(n),i=r.getFieldsError([o])[0];return i.errors}),$(this,"getFieldWarning",function(n){r.warningUnhooked();var o=Je(n),i=r.getFieldsError([o])[0];return i.warnings}),$(this,"isFieldsTouched",function(){r.warningUnhooked();for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o[0],s=o[1],l,u=!1;o.length===0?l=null:o.length===1?Array.isArray(a)?(l=a.map(Je),u=!1):(l=null,u=a):(l=a.map(Je),u=s);var d=r.getFieldEntities(!0),f=function(v){return v.isFieldTouched()};if(!l)return u?d.every(function(h){return f(h)||h.isList()}):d.some(f);var m=new tn;l.forEach(function(h){m.set(h,[])}),d.forEach(function(h){var v=h.getNamePath();l.forEach(function(y){y.every(function(b,C){return v[C]===b})&&m.update(y,function(b){return[].concat(q(b),[h])})})});var p=function(v){return v.some(f)},g=m.map(function(h){var v=h.value;return v});return u?g.every(p):g.some(p)}),$(this,"isFieldTouched",function(n){return r.warningUnhooked(),r.isFieldsTouched([n])}),$(this,"isFieldsValidating",function(n){r.warningUnhooked();var o=r.getFieldEntities();if(!n)return o.some(function(a){return a.isFieldValidating()});var i=n.map(Je);return o.some(function(a){var s=a.getNamePath();return fn(i,s)&&a.isFieldValidating()})}),$(this,"isFieldValidating",function(n){return r.warningUnhooked(),r.isFieldsValidating([n])}),$(this,"resetWithFieldInitialValue",function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},o=new tn,i=r.getFieldEntities(!0);i.forEach(function(l){var u=l.props.initialValue,d=l.getNamePath();if(u!==void 0){var f=o.get(d)||new Set;f.add({entity:l,value:u}),o.set(d,f)}});var a=function(u){u.forEach(function(d){var f=d.props.initialValue;if(f!==void 0){var m=d.getNamePath(),p=r.getInitialValue(m);if(p!==void 0)St(!1,"Form already set 'initialValues' with path '".concat(m.join("."),"'. Field can not overwrite it."));else{var g=o.get(m);if(g&&g.size>1)St(!1,"Multiple Field with path '".concat(m.join("."),"' set 'initialValue'. Can not decide which one to pick."));else if(g){var h=r.getFieldValue(m),v=d.isListField();!v&&(!n.skipExist||h===void 0)&&r.updateStore(Wt(r.store,m,q(g)[0].value))}}}})},s;n.entities?s=n.entities:n.namePathList?(s=[],n.namePathList.forEach(function(l){var u=o.get(l);if(u){var d;(d=s).push.apply(d,q(q(u).map(function(f){return f.entity})))}})):s=i,a(s)}),$(this,"resetFields",function(n){r.warningUnhooked();var o=r.store;if(!n){r.updateStore(ln(r.initialValues)),r.resetWithFieldInitialValue(),r.notifyObservers(o,null,{type:"reset"}),r.notifyWatch();return}var i=n.map(Je);i.forEach(function(a){var s=r.getInitialValue(a);r.updateStore(Wt(r.store,a,s))}),r.resetWithFieldInitialValue({namePathList:i}),r.notifyObservers(o,i,{type:"reset"}),r.notifyWatch(i)}),$(this,"setFields",function(n){r.warningUnhooked();var o=r.store,i=[];n.forEach(function(a){var s=a.name,l=ke(a,Cp),u=Je(s);i.push(u),"value"in l&&r.updateStore(Wt(r.store,u,l.value)),r.notifyObservers(o,[u],{type:"setField",data:a})}),r.notifyWatch(i)}),$(this,"getFields",function(){var n=r.getFieldEntities(!0),o=n.map(function(i){var a=i.getNamePath(),s=i.getMeta(),l=P(P({},s),{},{name:a,value:r.getFieldValue(a)});return Object.defineProperty(l,"originRCField",{value:!0}),l});return o}),$(this,"initEntityValue",function(n){var o=n.props.initialValue;if(o!==void 0){var i=n.getNamePath(),a=ir(r.store,i);a===void 0&&r.updateStore(Wt(r.store,i,o))}}),$(this,"isMergedPreserve",function(n){var o=n!==void 0?n:r.preserve;return o??!0}),$(this,"registerField",function(n){r.fieldEntities.push(n);var o=n.getNamePath();if(r.notifyWatch([o]),n.props.initialValue!==void 0){var i=r.store;r.resetWithFieldInitialValue({entities:[n],skipExist:!0}),r.notifyObservers(i,[n.getNamePath()],{type:"valueUpdate",source:"internal"})}return function(a,s){var l=arguments.length>2&&arguments[2]!==void 0?arguments[2]:[];if(r.fieldEntities=r.fieldEntities.filter(function(f){return f!==n}),!r.isMergedPreserve(s)&&(!a||l.length>1)){var u=a?void 0:r.getInitialValue(o);if(o.length&&r.getFieldValue(o)!==u&&r.fieldEntities.every(function(f){return!Iu(f.getNamePath(),o)})){var d=r.store;r.updateStore(Wt(d,o,u,!0)),r.notifyObservers(d,[o],{type:"remove"}),r.triggerDependenciesUpdate(d,o)}}r.notifyWatch([o])}}),$(this,"dispatch",function(n){switch(n.type){case"updateValue":{var o=n.namePath,i=n.value;r.updateValue(o,i);break}case"validateField":{var a=n.namePath,s=n.triggerName;r.validateFields([a],{triggerName:s});break}}}),$(this,"notifyObservers",function(n,o,i){if(r.subscribable){var a=P(P({},i),{},{store:r.getFieldsValue(!0)});r.getFieldEntities().forEach(function(s){var l=s.onStoreChange;l(n,o,a)})}else r.forceRootUpdate()}),$(this,"triggerDependenciesUpdate",function(n,o){var i=r.getDependencyChildrenFields(o);return i.length&&r.validateFields(i),r.notifyObservers(n,i,{type:"dependenciesUpdate",relatedFields:[o].concat(q(i))}),i}),$(this,"updateValue",function(n,o){var i=Je(n),a=r.store;r.updateStore(Wt(r.store,i,o)),r.notifyObservers(a,[i],{type:"valueUpdate",source:"internal"}),r.notifyWatch([i]);var s=r.triggerDependenciesUpdate(a,i),l=r.callbacks.onValuesChange;if(l){var u=Il(r.store,[i]);l(u,r.getFieldsValue())}r.triggerOnFieldsChange([i].concat(q(s)))}),$(this,"setFieldsValue",function(n){r.warningUnhooked();var o=r.store;if(n){var i=ln(r.store,n);r.updateStore(i)}r.notifyObservers(o,null,{type:"valueUpdate",source:"external"}),r.notifyWatch()}),$(this,"setFieldValue",function(n,o){r.setFields([{name:n,value:o,errors:[],warnings:[]}])}),$(this,"getDependencyChildrenFields",function(n){var o=new Set,i=[],a=new tn;r.getFieldEntities().forEach(function(l){var u=l.props.dependencies;(u||[]).forEach(function(d){var f=Je(d);a.update(f,function(){var m=arguments.length>0&&arguments[0]!==void 0?arguments[0]:new Set;return m.add(l),m})})});var s=function l(u){var d=a.get(u)||new Set;d.forEach(function(f){if(!o.has(f)){o.add(f);var m=f.getNamePath();f.isFieldDirty()&&m.length&&(i.push(m),l(m))}})};return s(n),i}),$(this,"triggerOnFieldsChange",function(n,o){var i=r.callbacks.onFieldsChange;if(i){var a=r.getFields();if(o){var s=new tn;o.forEach(function(u){var d=u.name,f=u.errors;s.set(d,f)}),a.forEach(function(u){u.errors=s.get(u.name)||u.errors})}var l=a.filter(function(u){var d=u.name;return fn(n,d)});l.length&&i(l,a)}}),$(this,"validateFields",function(n,o){r.warningUnhooked();var i,a;Array.isArray(n)||typeof n=="string"||typeof o=="string"?(i=n,a=o):a=n;var s=!!i,l=s?i.map(Je):[],u=[],d=String(Date.now()),f=new Set,m=a||{},p=m.recursive,g=m.dirty;r.getFieldEntities(!0).forEach(function(b){if(s||l.push(b.getNamePath()),!(!b.props.rules||!b.props.rules.length)&&!(g&&!b.isFieldDirty())){var C=b.getNamePath();if(f.add(C.join(d)),!s||fn(l,C,p)){var w=b.validateRules(P({validateMessages:P(P({},Ou),r.validateMessages)},a));u.push(w.then(function(){return{name:C,errors:[],warnings:[]}}).catch(function(S){var x,E=[],O=[];return(x=S.forEach)===null||x===void 0||x.call(S,function(I){var R=I.rule.warningOnly,A=I.errors;R?O.push.apply(O,q(A)):E.push.apply(E,q(A))}),E.length?Promise.reject({name:C,errors:E,warnings:O}):{name:C,errors:E,warnings:O}}))}}});var h=yp(u);r.lastValidatePromise=h,h.catch(function(b){return b}).then(function(b){var C=b.map(function(w){var S=w.name;return S});r.notifyObservers(r.store,C,{type:"validateFinish"}),r.triggerOnFieldsChange(C,b)});var v=h.then(function(){return r.lastValidatePromise===h?Promise.resolve(r.getFieldsValue(l)):Promise.reject([])}).catch(function(b){var C=b.filter(function(w){return w&&w.errors.length});return Promise.reject({values:r.getFieldsValue(l),errorFields:C,outOfDate:r.lastValidatePromise!==h})});v.catch(function(b){return b});var y=l.filter(function(b){return f.has(b.join(d))});return r.triggerOnFieldsChange(y),v}),$(this,"submit",function(){r.warningUnhooked(),r.validateFields().then(function(n){var o=r.callbacks.onFinish;if(o)try{o(n)}catch(i){console.error(i)}}).catch(function(n){var o=r.callbacks.onFinishFailed;o&&o(n)})}),this.forceRootUpdate=t});function Mu(e){var t=c.useRef(),r=c.useState({}),n=j(r,2),o=n[1];if(!t.current)if(e)t.current=e;else{var i=function(){o({})},a=new Sp(i);t.current=a.getForm()}return[t.current]}var Ta=c.createContext({triggerFormChange:function(){},triggerFormFinish:function(){},registerForm:function(){},unregisterForm:function(){}}),xp=function(t){var r=t.validateMessages,n=t.onFormChange,o=t.onFormFinish,i=t.children,a=c.useContext(Ta),s=c.useRef({});return c.createElement(Ta.Provider,{value:P(P({},a),{},{validateMessages:P(P({},a.validateMessages),r),triggerFormChange:function(u,d){n&&n(u,{changedFields:d,forms:s.current}),a.triggerFormChange(u,d)},triggerFormFinish:function(u,d){o&&o(u,{values:d,forms:s.current}),a.triggerFormFinish(u,d)},registerForm:function(u,d){u&&(s.current=P(P({},s.current),{},$({},u,d))),a.registerForm(u,d)},unregisterForm:function(u){var d=P({},s.current);delete d[u],s.current=d,a.unregisterForm(u)}})},i)},wp=["name","initialValues","fields","form","preserve","children","component","validateMessages","validateTrigger","onValuesChange","onFieldsChange","onFinish","onFinishFailed","clearOnDestroy"],$p=function(t,r){var n=t.name,o=t.initialValues,i=t.fields,a=t.form,s=t.preserve,l=t.children,u=t.component,d=u===void 0?"form":u,f=t.validateMessages,m=t.validateTrigger,p=m===void 0?"onChange":m,g=t.onValuesChange,h=t.onFieldsChange,v=t.onFinish,y=t.onFinishFailed,b=t.clearOnDestroy,C=ke(t,wp),w=c.useRef(null),S=c.useContext(Ta),x=Mu(a),E=j(x,1),O=E[0],I=O.getInternalHooks(Dr),R=I.useSubscribe,A=I.setInitialValues,M=I.setCallbacks,T=I.setValidateMessages,_=I.setPreserve,N=I.destroyForm;c.useImperativeHandle(r,function(){return P(P({},O),{},{nativeElement:w.current})}),c.useEffect(function(){return S.registerForm(n,O),function(){S.unregisterForm(n)}},[S,O,n]),T(P(P({},S.validateMessages),f)),M({onValuesChange:g,onFieldsChange:function(H){if(S.triggerFormChange(n,H),h){for(var k=arguments.length,ae=new Array(k>1?k-1:0),se=1;se<k;se++)ae[se-1]=arguments[se];h.apply(void 0,[H].concat(ae))}},onFinish:function(H){S.triggerFormFinish(n,H),v&&v(H)},onFinishFailed:y}),_(s);var F=c.useRef(null);A(o,!F.current),F.current||(F.current=!0),c.useEffect(function(){return function(){return N(b)}},[]);var L,B=typeof l=="function";if(B){var V=O.getFieldsValue(!0);L=l(V,O)}else L=l;R(!B);var K=c.useRef();c.useEffect(function(){gp(K.current||[],i||[])||O.setFields(i||[]),K.current=i},[i,O]);var U=c.useMemo(function(){return P(P({},O),{},{validateTrigger:p})},[O,p]),z=c.createElement(qo.Provider,{value:null},c.createElement(yn.Provider,{value:U},L));return d===!1?z:c.createElement(d,oe({},C,{ref:w,onSubmit:function(H){H.preventDefault(),H.stopPropagation(),O.submit()},onReset:function(H){var k;H.preventDefault(),O.resetFields(),(k=C.onReset)===null||k===void 0||k.call(C,H)}}),z)};function Rl(e){try{return JSON.stringify(e)}catch{return Math.random()}}function Ep(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t[0],o=t[1],i=o===void 0?{}:o,a=Ah(i)?{form:i}:i,s=a.form,l=c.useState(),u=j(l,2),d=u[0],f=u[1],m=c.useMemo(function(){return Rl(d)},[d]),p=c.useRef(m);p.current=m;var g=c.useContext(yn),h=s||g,v=h&&h._init,y=Je(n),b=c.useRef(y);return b.current=y,c.useEffect(function(){if(v){var C=h.getFieldsValue,w=h.getInternalHooks,S=w(Dr),x=S.registerWatch,E=function(A,M){var T=a.preserve?M:A;return typeof n=="function"?n(T):ir(T,b.current)},O=x(function(R,A){var M=E(R,A),T=Rl(M);p.current!==T&&(p.current=T,f(M))}),I=E(C(),C(!0));return d!==I&&f(I),O}},[v]),d}var Op=c.forwardRef($p),vo=Op;vo.FormProvider=xp;vo.Field=Pu;vo.List=bp;vo.useForm=Mu;vo.useWatch=Ep;const Ml=c.createContext({}),Ip=({children:e,status:t,override:r})=>{const n=c.useContext(Ml),o=c.useMemo(()=>{const i=Object.assign({},n);return r&&delete i.isFormItemInput,t&&(delete i.status,delete i.hasFeedback,delete i.feedbackIcon),i},[t,r,n]);return c.createElement(Ml.Provider,{value:o},e)},Pp=e=>{const{space:t,form:r,children:n}=e;if(n==null)return null;let o=n;return r&&(o=re.createElement(Ip,{override:!0,status:!0},o)),t&&(o=re.createElement(lg,null,o)),o},Rp=function(){if(typeof navigator>"u"||typeof window>"u")return!1;var e=navigator.userAgent||navigator.vendor||window.opera;return/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(e)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw-(n|u)|c55\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do(c|p)o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(-|_)|g1 u|g560|gene|gf-5|g-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd-(m|p|t)|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c(-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac( |-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\/|se(c(-|0|1)|47|mc|nd|ri)|sgh-|shar|sie(-|m)|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel(i|m)|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/i.test(e?.substr(0,4))};var Mp=["prefixCls","invalidate","item","renderItem","responsive","responsiveDisabled","registerSize","itemKey","className","style","children","display","order","component"],rn=void 0;function Tp(e,t){var r=e.prefixCls,n=e.invalidate,o=e.item,i=e.renderItem,a=e.responsive,s=e.responsiveDisabled,l=e.registerSize,u=e.itemKey,d=e.className,f=e.style,m=e.children,p=e.display,g=e.order,h=e.component,v=h===void 0?"div":h,y=ke(e,Mp),b=a&&!p;function C(O){l(u,O)}c.useEffect(function(){return function(){C(null)}},[]);var w=i&&o!==rn?i(o,{index:g}):m,S;n||(S={opacity:b?0:1,height:b?0:rn,overflowY:b?"hidden":rn,order:a?g:rn,pointerEvents:b?"none":rn,position:b?"absolute":rn});var x={};b&&(x["aria-hidden"]=!0);var E=c.createElement(v,oe({className:Q(!n&&r,d),style:P(P({},S),f)},x,y,{ref:t}),w);return a&&(E=c.createElement(so,{onResize:function(I){var R=I.offsetWidth;C(R)},disabled:s},E)),E}var Wn=c.forwardRef(Tp);Wn.displayName="Item";function _p(e){if(typeof MessageChannel>"u")Tt(e);else{var t=new MessageChannel;t.port1.onmessage=function(){return e()},t.port2.postMessage(void 0)}}function Ap(){var e=c.useRef(null),t=function(n){e.current||(e.current=[],_p(function(){za.unstable_batchedUpdates(function(){e.current.forEach(function(o){o()}),e.current=null})})),e.current.push(n)};return t}function An(e,t){var r=c.useState(t),n=j(r,2),o=n[0],i=n[1],a=Ct(function(s){e(function(){i(s)})});return[o,a]}var Uo=re.createContext(null),Fp=["component"],jp=["className"],Np=["className"],Lp=function(t,r){var n=c.useContext(Uo);if(!n){var o=t.component,i=o===void 0?"div":o,a=ke(t,Fp);return c.createElement(i,oe({},a,{ref:r}))}var s=n.className,l=ke(n,jp),u=t.className,d=ke(t,Np);return c.createElement(Uo.Provider,{value:null},c.createElement(Wn,oe({ref:r,className:Q(s,u)},l,d)))},Tu=c.forwardRef(Lp);Tu.displayName="RawItem";var Bp=["prefixCls","data","renderItem","renderRawItem","itemKey","itemWidth","ssr","style","className","maxCount","renderRest","renderRawRest","suffix","component","itemComponent","onVisibleChange"],_u="responsive",Au="invalidate";function zp(e){return"+ ".concat(e.length," ...")}function kp(e,t){var r=e.prefixCls,n=r===void 0?"rc-overflow":r,o=e.data,i=o===void 0?[]:o,a=e.renderItem,s=e.renderRawItem,l=e.itemKey,u=e.itemWidth,d=u===void 0?10:u,f=e.ssr,m=e.style,p=e.className,g=e.maxCount,h=e.renderRest,v=e.renderRawRest,y=e.suffix,b=e.component,C=b===void 0?"div":b,w=e.itemComponent,S=e.onVisibleChange,x=ke(e,Bp),E=f==="full",O=Ap(),I=An(O,null),R=j(I,2),A=R[0],M=R[1],T=A||0,_=An(O,new Map),N=j(_,2),F=N[0],L=N[1],B=An(O,0),V=j(B,2),K=V[0],U=V[1],z=An(O,0),X=j(z,2),H=X[0],k=X[1],ae=An(O,0),se=j(ae,2),Y=se[0],G=se[1],ue=c.useState(null),Se=j(ue,2),me=Se[0],be=Se[1],he=c.useState(null),$e=j(he,2),Ee=$e[0],_e=$e[1],Oe=c.useMemo(function(){return Ee===null&&E?Number.MAX_SAFE_INTEGER:Ee||0},[Ee,A]),we=c.useState(!1),He=j(we,2),ce=He[0],pe=He[1],ye="".concat(n,"-item"),te=Math.max(K,H),qe=g===_u,de=i.length&&qe,ct=g===Au,Ge=de||typeof g=="number"&&i.length>g,Pe=c.useMemo(function(){var Z=i;return de?A===null&&E?Z=i:Z=i.slice(0,Math.min(i.length,T/d)):typeof g=="number"&&(Z=i.slice(0,g)),Z},[i,d,A,g,de]),et=c.useMemo(function(){return de?i.slice(Oe+1):i.slice(Pe.length)},[i,Pe,de,Oe]),ge=c.useCallback(function(Z,J){var W;return typeof l=="function"?l(Z):(W=l&&Z?.[l])!==null&&W!==void 0?W:J},[l]),xe=c.useCallback(a||function(Z){return Z},[a]);function Be(Z,J,W){Ee===Z&&(J===void 0||J===me)||(_e(Z),W||(pe(Z<i.length-1),S?.(Z)),J!==void 0&&be(J))}function Re(Z,J){M(J.clientWidth)}function Me(Z,J){L(function(W){var ne=new Map(W);return J===null?ne.delete(Z):ne.set(Z,J),ne})}function le(Z,J){k(J),U(H)}function Ne(Z,J){G(J)}function Ze(Z){return F.get(ge(Pe[Z],Z))}yt(function(){if(T&&typeof te=="number"&&Pe){var Z=Y,J=Pe.length,W=J-1;if(!J){Be(0,null);return}for(var ne=0;ne<J;ne+=1){var Xe=Ze(ne);if(E&&(Xe=Xe||0),Xe===void 0){Be(ne-1,void 0,!0);break}if(Z+=Xe,W===0&&Z<=T||ne===W-1&&Z+Ze(W)<=T){Be(W,null);break}else if(Z+te>T){Be(ne-1,Z-Xe-Y+H);break}}y&&Ze(0)+Y>T&&be(null)}},[T,F,H,Y,ge,Pe]);var Xt=ce&&!!et.length,Qt={};me!==null&&de&&(Qt={position:"absolute",left:me,top:0});var mt={prefixCls:ye,responsive:de,component:w,invalidate:ct},cr=s?function(Z,J){var W=ge(Z,J);return c.createElement(Uo.Provider,{key:W,value:P(P({},mt),{},{order:J,item:Z,itemKey:W,registerSize:Me,display:J<=Oe})},s(Z,J))}:function(Z,J){var W=ge(Z,J);return c.createElement(Wn,oe({},mt,{order:J,key:W,item:Z,renderItem:xe,itemKey:W,registerSize:Me,display:J<=Oe}))},Ft={order:Xt?Oe:Number.MAX_SAFE_INTEGER,className:"".concat(ye,"-rest"),registerSize:le,display:Xt},Ye=h||zp,Ue=v?c.createElement(Uo.Provider,{value:P(P({},mt),Ft)},v(et)):c.createElement(Wn,oe({},mt,Ft),typeof Ye=="function"?Ye(et):Ye),De=c.createElement(C,oe({className:Q(!ct&&n,p),style:m,ref:t},x),Pe.map(cr),Ge?Ue:null,y&&c.createElement(Wn,oe({},mt,{responsive:qe,responsiveDisabled:!de,order:Oe,className:"".concat(ye,"-suffix"),registerSize:Ne,display:!0,style:Qt}),y));return qe?c.createElement(so,{onResize:Re,disabled:!de},De):De}var br=c.forwardRef(kp);br.displayName="Overflow";br.Item=Tu;br.RESPONSIVE=_u;br.INVALIDATE=Au;function Hp(e){var t=e.prefixCls,r=e.align,n=e.arrow,o=e.arrowPos,i=n||{},a=i.className,s=i.content,l=o.x,u=l===void 0?0:l,d=o.y,f=d===void 0?0:d,m=c.useRef();if(!r||!r.points)return null;var p={position:"absolute"};if(r.autoArrow!==!1){var g=r.points[0],h=r.points[1],v=g[0],y=g[1],b=h[0],C=h[1];v===b||!["t","b"].includes(v)?p.top=f:v==="t"?p.top=0:p.bottom=0,y===C||!["l","r"].includes(y)?p.left=u:y==="l"?p.left=0:p.right=0}return c.createElement("div",{ref:m,className:Q("".concat(t,"-arrow"),a),style:p},s)}function Dp(e){var t=e.prefixCls,r=e.open,n=e.zIndex,o=e.mask,i=e.motion;return o?c.createElement(Sn,oe({},i,{motionAppear:!0,visible:r,removeOnLeave:!0}),function(a){var s=a.className;return c.createElement("div",{style:{zIndex:n},className:Q("".concat(t,"-mask"),s)})}):null}var Vp=c.memo(function(e){var t=e.children;return t},function(e,t){return t.cache}),Wp=c.forwardRef(function(e,t){var r=e.popup,n=e.className,o=e.prefixCls,i=e.style,a=e.target,s=e.onVisibleChanged,l=e.open,u=e.keepDom,d=e.fresh,f=e.onClick,m=e.mask,p=e.arrow,g=e.arrowPos,h=e.align,v=e.motion,y=e.maskMotion,b=e.forceRender,C=e.getPopupContainer,w=e.autoDestroy,S=e.portal,x=e.zIndex,E=e.onMouseEnter,O=e.onMouseLeave,I=e.onPointerEnter,R=e.onPointerDownCapture,A=e.ready,M=e.offsetX,T=e.offsetY,_=e.offsetR,N=e.offsetB,F=e.onAlign,L=e.onPrepare,B=e.stretch,V=e.targetWidth,K=e.targetHeight,U=typeof r=="function"?r():r,z=l||u,X=C?.length>0,H=c.useState(!C||!X),k=j(H,2),ae=k[0],se=k[1];if(yt(function(){!ae&&X&&a&&se(!0)},[ae,X,a]),!ae)return null;var Y="auto",G={left:"-1000vw",top:"-1000vh",right:Y,bottom:Y};if(A||!l){var ue,Se=h.points,me=h.dynamicInset||((ue=h._experimental)===null||ue===void 0?void 0:ue.dynamicInset),be=me&&Se[0][1]==="r",he=me&&Se[0][0]==="b";be?(G.right=_,G.left=Y):(G.left=M,G.right=Y),he?(G.bottom=N,G.top=Y):(G.top=T,G.bottom=Y)}var $e={};return B&&(B.includes("height")&&K?$e.height=K:B.includes("minHeight")&&K&&($e.minHeight=K),B.includes("width")&&V?$e.width=V:B.includes("minWidth")&&V&&($e.minWidth=V)),l||($e.pointerEvents="none"),c.createElement(S,{open:b||z,getContainer:C&&function(){return C(a)},autoDestroy:w},c.createElement(Dp,{prefixCls:o,open:l,zIndex:x,mask:m,motion:y}),c.createElement(so,{onResize:F,disabled:!l},function(Ee){return c.createElement(Sn,oe({motionAppear:!0,motionEnter:!0,motionLeave:!0,removeOnLeave:!1,forceRender:b,leavedClassName:"".concat(o,"-hidden")},v,{onAppearPrepare:L,onEnterPrepare:L,visible:l,onVisibleChanged:function(Oe){var we;v==null||(we=v.onVisibleChanged)===null||we===void 0||we.call(v,Oe),s(Oe)}}),function(_e,Oe){var we=_e.className,He=_e.style,ce=Q(o,we,n);return c.createElement("div",{ref:Zo(Ee,t,Oe),className:ce,style:P(P(P(P({"--arrow-x":"".concat(g.x||0,"px"),"--arrow-y":"".concat(g.y||0,"px")},G),$e),He),{},{boxSizing:"border-box",zIndex:x},i),onMouseEnter:E,onMouseLeave:O,onPointerEnter:I,onClick:f,onPointerDownCapture:R},p&&c.createElement(Hp,{prefixCls:o,arrow:p,arrowPos:g,align:h}),c.createElement(Vp,{cache:!l&&!d},U))})}))}),qp=c.forwardRef(function(e,t){var r=e.children,n=e.getTriggerDOMNode,o=ao(r),i=c.useCallback(function(s){ka(t,n?n(s):s)},[n]),a=io(i,Yo(r));return o?c.cloneElement(r,{ref:a}):r}),Tl=c.createContext(null);function _l(e){return e?Array.isArray(e)?e:[e]:[]}function Up(e,t,r,n){return c.useMemo(function(){var o=_l(r??t),i=_l(n??t),a=new Set(o),s=new Set(i);return e&&(a.has("hover")&&(a.delete("hover"),a.add("click")),s.has("hover")&&(s.delete("hover"),s.add("click"))),[a,s]},[e,t,r,n])}function Kp(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],r=arguments.length>2?arguments[2]:void 0;return r?e[0]===t[0]:e[0]===t[0]&&e[1]===t[1]}function Gp(e,t,r,n){for(var o=r.points,i=Object.keys(e),a=0;a<i.length;a+=1){var s,l=i[a];if(Kp((s=e[l])===null||s===void 0?void 0:s.points,o,n))return"".concat(t,"-placement-").concat(l)}return""}function Al(e,t,r,n){return t||(r?{motionName:"".concat(e,"-").concat(r)}:n?{motionName:n}:null)}function mo(e){return e.ownerDocument.defaultView}function _a(e){for(var t=[],r=e?.parentElement,n=["hidden","scroll","clip","auto"];r;){var o=mo(r).getComputedStyle(r),i=o.overflowX,a=o.overflowY,s=o.overflow;[i,a,s].some(function(l){return n.includes(l)})&&t.push(r),r=r.parentElement}return t}function ro(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;return Number.isNaN(e)?t:e}function Fn(e){return ro(parseFloat(e),0)}function Fl(e,t){var r=P({},e);return(t||[]).forEach(function(n){if(!(n instanceof HTMLBodyElement||n instanceof HTMLHtmlElement)){var o=mo(n).getComputedStyle(n),i=o.overflow,a=o.overflowClipMargin,s=o.borderTopWidth,l=o.borderBottomWidth,u=o.borderLeftWidth,d=o.borderRightWidth,f=n.getBoundingClientRect(),m=n.offsetHeight,p=n.clientHeight,g=n.offsetWidth,h=n.clientWidth,v=Fn(s),y=Fn(l),b=Fn(u),C=Fn(d),w=ro(Math.round(f.width/g*1e3)/1e3),S=ro(Math.round(f.height/m*1e3)/1e3),x=(g-h-b-C)*w,E=(m-p-v-y)*S,O=v*S,I=y*S,R=b*w,A=C*w,M=0,T=0;if(i==="clip"){var _=Fn(a);M=_*w,T=_*S}var N=f.x+R-M,F=f.y+O-T,L=N+f.width+2*M-R-A-x,B=F+f.height+2*T-O-I-E;r.left=Math.max(r.left,N),r.top=Math.max(r.top,F),r.right=Math.min(r.right,L),r.bottom=Math.min(r.bottom,B)}}),r}function jl(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r="".concat(t),n=r.match(/^(.*)\%$/);return n?e*(parseFloat(n[1])/100):parseFloat(r)}function Nl(e,t){var r=t||[],n=j(r,2),o=n[0],i=n[1];return[jl(e.width,o),jl(e.height,i)]}function Ll(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return[e[0],e[1]]}function nn(e,t){var r=t[0],n=t[1],o,i;return r==="t"?i=e.y:r==="b"?i=e.y+e.height:i=e.y+e.height/2,n==="l"?o=e.x:n==="r"?o=e.x+e.width:o=e.x+e.width/2,{x:o,y:i}}function Pr(e,t){var r={t:"b",b:"t",l:"r",r:"l"};return e.map(function(n,o){return o===t?r[n]||"c":n}).join("")}function Xp(e,t,r,n,o,i,a){var s=c.useState({ready:!1,offsetX:0,offsetY:0,offsetR:0,offsetB:0,arrowX:0,arrowY:0,scaleX:1,scaleY:1,align:o[n]||{}}),l=j(s,2),u=l[0],d=l[1],f=c.useRef(0),m=c.useMemo(function(){return t?_a(t):[]},[t]),p=c.useRef({}),g=function(){p.current={}};e||g();var h=Ct(function(){if(t&&r&&e){let Lt=function(Rn,hr){var Ir=arguments.length>2&&arguments[2]!==void 0?arguments[2]:qe,Mn=z.x+Rn,po=z.y+hr,pi=Mn+he,bi=po+be,yi=Math.max(Mn,Ir.left),D=Math.max(po,Ir.top),ee=Math.min(pi,Ir.right),Ke=Math.min(bi,Ir.bottom);return Math.max(0,(ee-yi)*(Ke-D))},Yr=function(){xt=z.y+ne,wt=xt+be,Vt=z.x+W,$t=Vt+he};var b,C,w,S,x=t,E=x.ownerDocument,O=mo(x),I=O.getComputedStyle(x),R=I.position,A=x.style.left,M=x.style.top,T=x.style.right,_=x.style.bottom,N=x.style.overflow,F=P(P({},o[n]),i),L=E.createElement("div");(b=x.parentElement)===null||b===void 0||b.appendChild(L),L.style.left="".concat(x.offsetLeft,"px"),L.style.top="".concat(x.offsetTop,"px"),L.style.position=R,L.style.height="".concat(x.offsetHeight,"px"),L.style.width="".concat(x.offsetWidth,"px"),x.style.left="0",x.style.top="0",x.style.right="auto",x.style.bottom="auto",x.style.overflow="hidden";var B;if(Array.isArray(r))B={x:r[0],y:r[1],width:0,height:0};else{var V,K,U=r.getBoundingClientRect();U.x=(V=U.x)!==null&&V!==void 0?V:U.left,U.y=(K=U.y)!==null&&K!==void 0?K:U.top,B={x:U.x,y:U.y,width:U.width,height:U.height}}var z=x.getBoundingClientRect(),X=O.getComputedStyle(x),H=X.height,k=X.width;z.x=(C=z.x)!==null&&C!==void 0?C:z.left,z.y=(w=z.y)!==null&&w!==void 0?w:z.top;var ae=E.documentElement,se=ae.clientWidth,Y=ae.clientHeight,G=ae.scrollWidth,ue=ae.scrollHeight,Se=ae.scrollTop,me=ae.scrollLeft,be=z.height,he=z.width,$e=B.height,Ee=B.width,_e={left:0,top:0,right:se,bottom:Y},Oe={left:-me,top:-Se,right:G-me,bottom:ue-Se},we=F.htmlRegion,He="visible",ce="visibleFirst";we!=="scroll"&&we!==ce&&(we=He);var pe=we===ce,ye=Fl(Oe,m),te=Fl(_e,m),qe=we===He?te:ye,de=pe?te:qe;x.style.left="auto",x.style.top="auto",x.style.right="0",x.style.bottom="0";var ct=x.getBoundingClientRect();x.style.left=A,x.style.top=M,x.style.right=T,x.style.bottom=_,x.style.overflow=N,(S=x.parentElement)===null||S===void 0||S.removeChild(L);var Ge=ro(Math.round(he/parseFloat(k)*1e3)/1e3),Pe=ro(Math.round(be/parseFloat(H)*1e3)/1e3);if(Ge===0||Pe===0||qn(r)&&!os(r))return;var et=F.offset,ge=F.targetOffset,xe=Nl(z,et),Be=j(xe,2),Re=Be[0],Me=Be[1],le=Nl(B,ge),Ne=j(le,2),Ze=Ne[0],Xt=Ne[1];B.x-=Ze,B.y-=Xt;var Qt=F.points||[],mt=j(Qt,2),cr=mt[0],Ft=mt[1],Ye=Ll(Ft),Ue=Ll(cr),De=nn(B,Ye),Z=nn(z,Ue),J=P({},F),W=De.x-Z.x+Re,ne=De.y-Z.y+Me,Xe=Lt(W,ne),jt=Lt(W,ne,te),Te=nn(B,["t","l"]),ur=nn(z,["t","l"]),dr=nn(B,["b","r"]),Ae=nn(z,["b","r"]),Fe=F.overflow||{},gt=Fe.adjustX,nt=Fe.adjustY,ot=Fe.shiftX,Zt=Fe.shiftY,Yt=function(hr){return typeof hr=="boolean"?hr:hr>=0},xt,wt,Vt,$t;Yr();var ht=Yt(nt),pt=Ue[0]===Ye[0];if(ht&&Ue[0]==="t"&&(wt>de.bottom||p.current.bt)){var fr=ne;pt?fr-=be-$e:fr=Te.y-Ae.y-Me;var Kr=Lt(W,fr),Gr=Lt(W,fr,te);Kr>Xe||Kr===Xe&&(!pe||Gr>=jt)?(p.current.bt=!0,ne=fr,Me=-Me,J.points=[Pr(Ue,0),Pr(Ye,0)]):p.current.bt=!1}if(ht&&Ue[0]==="b"&&(xt<de.top||p.current.tb)){var Et=ne;pt?Et+=be-$e:Et=dr.y-ur.y-Me;var $r=Lt(W,Et),En=Lt(W,Et,te);$r>Xe||$r===Xe&&(!pe||En>=jt)?(p.current.tb=!0,ne=Et,Me=-Me,J.points=[Pr(Ue,0),Pr(Ye,0)]):p.current.tb=!1}var Xr=Yt(gt),Qr=Ue[1]===Ye[1];if(Xr&&Ue[1]==="l"&&($t>de.right||p.current.rl)){var Jt=W;Qr?Jt-=he-Ee:Jt=Te.x-Ae.x-Re;var Nt=Lt(Jt,ne),Fr=Lt(Jt,ne,te);Nt>Xe||Nt===Xe&&(!pe||Fr>=jt)?(p.current.rl=!0,W=Jt,Re=-Re,J.points=[Pr(Ue,1),Pr(Ye,1)]):p.current.rl=!1}if(Xr&&Ue[1]==="r"&&(Vt<de.left||p.current.lr)){var vr=W;Qr?vr+=he-Ee:vr=dr.x-ur.x-Re;var jr=Lt(vr,ne),mr=Lt(vr,ne,te);jr>Xe||jr===Xe&&(!pe||mr>=jt)?(p.current.lr=!0,W=vr,Re=-Re,J.points=[Pr(Ue,1),Pr(Ye,1)]):p.current.lr=!1}Yr();var Ot=ot===!0?0:ot;typeof Ot=="number"&&(Vt<te.left&&(W-=Vt-te.left-Re,B.x+Ee<te.left+Ot&&(W+=B.x-te.left+Ee-Ot)),$t>te.right&&(W-=$t-te.right-Re,B.x>te.right-Ot&&(W+=B.x-te.right+Ot)));var er=Zt===!0?0:Zt;typeof er=="number"&&(xt<te.top&&(ne-=xt-te.top-Me,B.y+$e<te.top+er&&(ne+=B.y-te.top+$e-er)),wt>te.bottom&&(ne-=wt-te.bottom-Me,B.y>te.bottom-er&&(ne+=B.y-te.bottom+er)));var tr=z.x+W,rr=tr+he,Nr=z.y+ne,We=Nr+be,Ie=B.x,Le=Ie+Ee,ut=B.y,ft=ut+$e,Er=Math.max(tr,Ie),Or=Math.min(rr,Le),On=(Er+Or)/2,Zr=On-tr,Lr=Math.max(Nr,ut),gr=Math.min(We,ft),Br=(Lr+gr)/2,In=Br-Nr;a?.(t,J);var nr=ct.right-z.x-(W+z.width),Pn=ct.bottom-z.y-(ne+z.height);Ge===1&&(W=Math.round(W),nr=Math.round(nr)),Pe===1&&(ne=Math.round(ne),Pn=Math.round(Pn));var hi={ready:!0,offsetX:W/Ge,offsetY:ne/Pe,offsetR:nr/Ge,offsetB:Pn/Pe,arrowX:Zr/Ge,arrowY:In/Pe,scaleX:Ge,scaleY:Pe,align:J};d(hi)}}),v=function(){f.current+=1;var C=f.current;Promise.resolve().then(function(){f.current===C&&h()})},y=function(){d(function(C){return P(P({},C),{},{ready:!1})})};return yt(y,[n]),yt(function(){e||y()},[e]),[u.ready,u.offsetX,u.offsetY,u.offsetR,u.offsetB,u.arrowX,u.arrowY,u.scaleX,u.scaleY,u.align,v]}function Qp(e,t,r,n,o){yt(function(){if(e&&t&&r){let f=function(){n(),o()};var i=t,a=r,s=_a(i),l=_a(a),u=mo(a),d=new Set([u].concat(q(s),q(l)));return d.forEach(function(m){m.addEventListener("scroll",f,{passive:!0})}),u.addEventListener("resize",f,{passive:!0}),n(),function(){d.forEach(function(m){m.removeEventListener("scroll",f),u.removeEventListener("resize",f)})}}},[e,t,r])}function Zp(e,t,r,n,o,i,a,s){var l=c.useRef(e);l.current=e;var u=c.useRef(!1);c.useEffect(function(){if(t&&n&&(!o||i)){var f=function(){u.current=!1},m=function(v){var y;l.current&&!a(((y=v.composedPath)===null||y===void 0||(y=y.call(v))===null||y===void 0?void 0:y[0])||v.target)&&!u.current&&s(!1)},p=mo(n);p.addEventListener("pointerdown",f,!0),p.addEventListener("mousedown",m,!0),p.addEventListener("contextmenu",m,!0);var g=Wo(r);return g&&(g.addEventListener("mousedown",m,!0),g.addEventListener("contextmenu",m,!0)),function(){p.removeEventListener("pointerdown",f,!0),p.removeEventListener("mousedown",m,!0),p.removeEventListener("contextmenu",m,!0),g&&(g.removeEventListener("mousedown",m,!0),g.removeEventListener("contextmenu",m,!0))}}},[t,r,n,o,i]);function d(){u.current=!0}return d}var Yp=["prefixCls","children","action","showAction","hideAction","popupVisible","defaultPopupVisible","onPopupVisibleChange","afterPopupVisibleChange","mouseEnterDelay","mouseLeaveDelay","focusDelay","blurDelay","mask","maskClosable","getPopupContainer","forceRender","autoDestroy","destroyPopupOnHide","popup","popupClassName","popupStyle","popupPlacement","builtinPlacements","popupAlign","zIndex","stretch","getPopupClassNameFromAlign","fresh","alignPoint","onPopupClick","onPopupAlign","arrow","popupMotion","maskMotion","popupTransitionName","popupAnimation","maskTransitionName","maskAnimation","className","getTriggerDOMNode"];function Jp(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:wu,t=c.forwardRef(function(r,n){var o=r.prefixCls,i=o===void 0?"rc-trigger-popup":o,a=r.children,s=r.action,l=s===void 0?"hover":s,u=r.showAction,d=r.hideAction,f=r.popupVisible,m=r.defaultPopupVisible,p=r.onPopupVisibleChange,g=r.afterPopupVisibleChange,h=r.mouseEnterDelay,v=r.mouseLeaveDelay,y=v===void 0?.1:v,b=r.focusDelay,C=r.blurDelay,w=r.mask,S=r.maskClosable,x=S===void 0?!0:S,E=r.getPopupContainer,O=r.forceRender,I=r.autoDestroy,R=r.destroyPopupOnHide,A=r.popup,M=r.popupClassName,T=r.popupStyle,_=r.popupPlacement,N=r.builtinPlacements,F=N===void 0?{}:N,L=r.popupAlign,B=r.zIndex,V=r.stretch,K=r.getPopupClassNameFromAlign,U=r.fresh,z=r.alignPoint,X=r.onPopupClick,H=r.onPopupAlign,k=r.arrow,ae=r.popupMotion,se=r.maskMotion,Y=r.popupTransitionName,G=r.popupAnimation,ue=r.maskTransitionName,Se=r.maskAnimation,me=r.className,be=r.getTriggerDOMNode,he=ke(r,Yp),$e=I||R||!1,Ee=c.useState(!1),_e=j(Ee,2),Oe=_e[0],we=_e[1];yt(function(){we(Rp())},[]);var He=c.useRef({}),ce=c.useContext(Tl),pe=c.useMemo(function(){return{registerSubPopup:function(ee,Ke){He.current[ee]=Ke,ce?.registerSubPopup(ee,Ke)}}},[ce]),ye=$u(),te=c.useState(null),qe=j(te,2),de=qe[0],ct=qe[1],Ge=c.useRef(null),Pe=Ct(function(D){Ge.current=D,qn(D)&&de!==D&&ct(D),ce?.registerSubPopup(ye,D)}),et=c.useState(null),ge=j(et,2),xe=ge[0],Be=ge[1],Re=c.useRef(null),Me=Ct(function(D){qn(D)&&xe!==D&&(Be(D),Re.current=D)}),le=c.Children.only(a),Ne=le?.props||{},Ze={},Xt=Ct(function(D){var ee,Ke,it=xe;return it?.contains(D)||((ee=Wo(it))===null||ee===void 0?void 0:ee.host)===D||D===it||de?.contains(D)||((Ke=Wo(de))===null||Ke===void 0?void 0:Ke.host)===D||D===de||Object.values(He.current).some(function(Qe){return Qe?.contains(D)||D===Qe})}),Qt=Al(i,ae,G,Y),mt=Al(i,se,Se,ue),cr=c.useState(m||!1),Ft=j(cr,2),Ye=Ft[0],Ue=Ft[1],De=f??Ye,Z=Ct(function(D){f===void 0&&Ue(D)});yt(function(){Ue(f||!1)},[f]);var J=c.useRef(De);J.current=De;var W=c.useRef([]);W.current=[];var ne=Ct(function(D){var ee;Z(D),((ee=W.current[W.current.length-1])!==null&&ee!==void 0?ee:De)!==D&&(W.current.push(D),p?.(D))}),Xe=c.useRef(),jt=function(){clearTimeout(Xe.current)},Te=function(ee){var Ke=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;jt(),Ke===0?ne(ee):Xe.current=setTimeout(function(){ne(ee)},Ke*1e3)};c.useEffect(function(){return jt},[]);var ur=c.useState(!1),dr=j(ur,2),Ae=dr[0],Fe=dr[1];yt(function(D){(!D||De)&&Fe(!0)},[De]);var gt=c.useState(null),nt=j(gt,2),ot=nt[0],Zt=nt[1],Yt=c.useState(null),xt=j(Yt,2),wt=xt[0],Vt=xt[1],$t=function(ee){Vt([ee.clientX,ee.clientY])},ht=Xp(De,de,z&&wt!==null?wt:xe,_,F,L,H),pt=j(ht,11),fr=pt[0],Kr=pt[1],Gr=pt[2],Et=pt[3],$r=pt[4],En=pt[5],Xr=pt[6],Qr=pt[7],Jt=pt[8],Nt=pt[9],Fr=pt[10],vr=Up(Oe,l,u,d),jr=j(vr,2),mr=jr[0],Ot=jr[1],er=mr.has("click"),tr=Ot.has("click")||Ot.has("contextMenu"),rr=Ct(function(){Ae||Fr()}),Nr=function(){J.current&&z&&tr&&Te(!1)};Qp(De,xe,de,rr,Nr),yt(function(){rr()},[wt,_]),yt(function(){De&&!(F!=null&&F[_])&&rr()},[JSON.stringify(L)]);var We=c.useMemo(function(){var D=Gp(F,i,Nt,z);return Q(D,K?.(Nt))},[Nt,K,F,i,z]);c.useImperativeHandle(n,function(){return{nativeElement:Re.current,popupElement:Ge.current,forceAlign:rr}});var Ie=c.useState(0),Le=j(Ie,2),ut=Le[0],ft=Le[1],Er=c.useState(0),Or=j(Er,2),On=Or[0],Zr=Or[1],Lr=function(){if(V&&xe){var ee=xe.getBoundingClientRect();ft(ee.width),Zr(ee.height)}},gr=function(){Lr(),rr()},Br=function(ee){Fe(!1),Fr(),g?.(ee)},In=function(){return new Promise(function(ee){Lr(),Zt(function(){return ee})})};yt(function(){ot&&(Fr(),ot(),Zt(null))},[ot]);function nr(D,ee,Ke,it){Ze[D]=function(Qe){var bo;it?.(Qe),Te(ee,Ke);for(var Ci=arguments.length,hs=new Array(Ci>1?Ci-1:0),yo=1;yo<Ci;yo++)hs[yo-1]=arguments[yo];(bo=Ne[D])===null||bo===void 0||bo.call.apply(bo,[Ne,Qe].concat(hs))}}(er||tr)&&(Ze.onClick=function(D){var ee;J.current&&tr?Te(!1):!J.current&&er&&($t(D),Te(!0));for(var Ke=arguments.length,it=new Array(Ke>1?Ke-1:0),Qe=1;Qe<Ke;Qe++)it[Qe-1]=arguments[Qe];(ee=Ne.onClick)===null||ee===void 0||ee.call.apply(ee,[Ne,D].concat(it))});var Pn=Zp(De,tr,xe,de,w,x,Xt,Te),hi=mr.has("hover"),Lt=Ot.has("hover"),Yr,Rn;hi&&(nr("onMouseEnter",!0,h,function(D){$t(D)}),nr("onPointerEnter",!0,h,function(D){$t(D)}),Yr=function(ee){(De||Ae)&&de!==null&&de!==void 0&&de.contains(ee.target)&&Te(!0,h)},z&&(Ze.onMouseMove=function(D){var ee;(ee=Ne.onMouseMove)===null||ee===void 0||ee.call(Ne,D)})),Lt&&(nr("onMouseLeave",!1,y),nr("onPointerLeave",!1,y),Rn=function(){Te(!1,y)}),mr.has("focus")&&nr("onFocus",!0,b),Ot.has("focus")&&nr("onBlur",!1,C),mr.has("contextMenu")&&(Ze.onContextMenu=function(D){var ee;J.current&&Ot.has("contextMenu")?Te(!1):($t(D),Te(!0)),D.preventDefault();for(var Ke=arguments.length,it=new Array(Ke>1?Ke-1:0),Qe=1;Qe<Ke;Qe++)it[Qe-1]=arguments[Qe];(ee=Ne.onContextMenu)===null||ee===void 0||ee.call.apply(ee,[Ne,D].concat(it))}),me&&(Ze.className=Q(Ne.className,me));var hr=c.useRef(!1);hr.current||(hr.current=O||De||Ae);var Ir=P(P({},Ne),Ze),Mn={},po=["onContextMenu","onClick","onMouseDown","onTouchStart","onMouseEnter","onMouseLeave","onFocus","onBlur"];po.forEach(function(D){he[D]&&(Mn[D]=function(){for(var ee,Ke=arguments.length,it=new Array(Ke),Qe=0;Qe<Ke;Qe++)it[Qe]=arguments[Qe];(ee=Ir[D])===null||ee===void 0||ee.call.apply(ee,[Ir].concat(it)),he[D].apply(he,it)})});var pi=c.cloneElement(le,P(P({},Ir),Mn)),bi={x:En,y:Xr},yi=k?P({},k!==!0?k:{}):null;return c.createElement(c.Fragment,null,c.createElement(so,{disabled:!De,ref:Me,onResize:gr},c.createElement(qp,{getTriggerDOMNode:be},pi)),hr.current&&c.createElement(Tl.Provider,{value:pe},c.createElement(Wp,{portal:e,ref:Pe,prefixCls:i,popup:A,className:Q(M,We),style:T,target:xe,onMouseEnter:Yr,onMouseLeave:Rn,onPointerEnter:Yr,zIndex:B,open:De,keepDom:Ae,fresh:U,onClick:X,onPointerDownCapture:Pn,mask:w,motion:Qt,maskMotion:mt,onVisibleChanged:Br,onPrepare:In,forceRender:O,autoDestroy:$e,getPopupContainer:E,align:Nt,arrow:yi,arrowPos:bi,ready:fr,offsetX:Kr,offsetY:Gr,offsetR:Et,offsetB:$r,onAlign:rr,stretch:V,targetWidth:ut/Qr,targetHeight:On/Jt})))});return t}const Fu=Jp(wu),e0=(e,t)=>{typeof e?.addEventListener<"u"?e.addEventListener("change",t):typeof e?.addListener<"u"&&e.addListener(t)},t0=(e,t)=>{typeof e?.removeEventListener<"u"?e.removeEventListener("change",t):typeof e?.removeListener<"u"&&e.removeListener(t)};function ju(e){var t=e.children,r=e.prefixCls,n=e.id,o=e.overlayInnerStyle,i=e.bodyClassName,a=e.className,s=e.style;return c.createElement("div",{className:Q("".concat(r,"-content"),a),style:s},c.createElement("div",{className:Q("".concat(r,"-inner"),i),id:n,role:"tooltip",style:o},typeof t=="function"?t():t))}var on={shiftX:64,adjustY:1},an={adjustX:1,shiftY:!0},kt=[0,0],r0={left:{points:["cr","cl"],overflow:an,offset:[-4,0],targetOffset:kt},right:{points:["cl","cr"],overflow:an,offset:[4,0],targetOffset:kt},top:{points:["bc","tc"],overflow:on,offset:[0,-4],targetOffset:kt},bottom:{points:["tc","bc"],overflow:on,offset:[0,4],targetOffset:kt},topLeft:{points:["bl","tl"],overflow:on,offset:[0,-4],targetOffset:kt},leftTop:{points:["tr","tl"],overflow:an,offset:[-4,0],targetOffset:kt},topRight:{points:["br","tr"],overflow:on,offset:[0,-4],targetOffset:kt},rightTop:{points:["tl","tr"],overflow:an,offset:[4,0],targetOffset:kt},bottomRight:{points:["tr","br"],overflow:on,offset:[0,4],targetOffset:kt},rightBottom:{points:["bl","br"],overflow:an,offset:[4,0],targetOffset:kt},bottomLeft:{points:["tl","bl"],overflow:on,offset:[0,4],targetOffset:kt},leftBottom:{points:["br","bl"],overflow:an,offset:[-4,0],targetOffset:kt}},n0=["overlayClassName","trigger","mouseEnterDelay","mouseLeaveDelay","overlayStyle","prefixCls","children","onVisibleChange","afterVisibleChange","transitionName","animation","motion","placement","align","destroyTooltipOnHide","defaultVisible","getTooltipContainer","overlayInnerStyle","arrowContent","overlay","id","showArrow","classNames","styles"],o0=function(t,r){var n=t.overlayClassName,o=t.trigger,i=o===void 0?["hover"]:o,a=t.mouseEnterDelay,s=a===void 0?0:a,l=t.mouseLeaveDelay,u=l===void 0?.1:l,d=t.overlayStyle,f=t.prefixCls,m=f===void 0?"rc-tooltip":f,p=t.children,g=t.onVisibleChange,h=t.afterVisibleChange,v=t.transitionName,y=t.animation,b=t.motion,C=t.placement,w=C===void 0?"right":C,S=t.align,x=S===void 0?{}:S,E=t.destroyTooltipOnHide,O=E===void 0?!1:E,I=t.defaultVisible,R=t.getTooltipContainer,A=t.overlayInnerStyle;t.arrowContent;var M=t.overlay,T=t.id,_=t.showArrow,N=_===void 0?!0:_,F=t.classNames,L=t.styles,B=ke(t,n0),V=$u(T),K=c.useRef(null);c.useImperativeHandle(r,function(){return K.current});var U=P({},B);"visible"in t&&(U.popupVisible=t.visible);var z=function(){return c.createElement(ju,{key:"content",prefixCls:m,id:V,bodyClassName:F?.body,overlayInnerStyle:P(P({},A),L?.body)},M)},X=function(){var k=c.Children.only(p),ae=k?.props||{},se=P(P({},ae),{},{"aria-describedby":M?V:null});return c.cloneElement(p,se)};return c.createElement(Fu,oe({popupClassName:Q(n,F?.root),prefixCls:m,popup:z,action:i,builtinPlacements:r0,popupPlacement:w,ref:K,popupAlign:x,getPopupContainer:R,onPopupVisibleChange:g,afterPopupVisibleChange:h,popupTransitionName:v,popupAnimation:y,popupMotion:b,defaultPopupVisible:I,autoDestroy:O,mouseLeaveDelay:u,popupStyle:P(P({},d),L?.root),mouseEnterDelay:s,arrow:N},U),X())};const i0=c.forwardRef(o0);function a0(e){const{sizePopupArrow:t,borderRadiusXS:r,borderRadiusOuter:n}=e,o=t/2,i=0,a=o,s=n*1/Math.sqrt(2),l=o-n*(1-1/Math.sqrt(2)),u=o-r*(1/Math.sqrt(2)),d=n*(Math.sqrt(2)-1)+r*(1/Math.sqrt(2)),f=2*o-u,m=d,p=2*o-s,g=l,h=2*o-i,v=a,y=o*Math.sqrt(2)+n*(Math.sqrt(2)-2),b=n*(Math.sqrt(2)-1),C=`polygon(${b}px 100%, 50% ${b}px, ${2*o-b}px 100%, ${b}px 100%)`,w=`path('M ${i} ${a} A ${n} ${n} 0 0 0 ${s} ${l} L ${u} ${d} A ${r} ${r} 0 0 1 ${f} ${m} L ${p} ${g} A ${n} ${n} 0 0 0 ${h} ${v} Z')`;return{arrowShadowWidth:y,arrowPath:w,arrowPolygon:C}}const s0=(e,t,r)=>{const{sizePopupArrow:n,arrowPolygon:o,arrowPath:i,arrowShadowWidth:a,borderRadiusXS:s,calc:l}=e;return{pointerEvents:"none",width:n,height:n,overflow:"hidden","&::before":{position:"absolute",bottom:0,insetInlineStart:0,width:n,height:l(n).div(2).equal(),background:t,clipPath:{_multi_value_:!0,value:[o,i]},content:'""'},"&::after":{content:'""',position:"absolute",width:a,height:a,bottom:0,insetInline:0,margin:"auto",borderRadius:{_skip_check_:!0,value:`0 0 ${ie(s)} 0`},transform:"translateY(50%) rotate(-135deg)",boxShadow:r,zIndex:0,background:"transparent"}}},Nu=8;function Lu(e){const{contentRadius:t,limitVerticalRadius:r}=e,n=t>12?t+2:12;return{arrowOffsetHorizontal:n,arrowOffsetVertical:r?Nu:n}}function Io(e,t){return e?t:{}}function l0(e,t,r){const{componentCls:n,boxShadowPopoverArrow:o,arrowOffsetVertical:i,arrowOffsetHorizontal:a}=e,{arrowDistance:s=0,arrowPlacement:l={left:!0,right:!0,top:!0,bottom:!0}}={};return{[n]:Object.assign(Object.assign(Object.assign(Object.assign({[`${n}-arrow`]:[Object.assign(Object.assign({position:"absolute",zIndex:1,display:"block"},s0(e,t,o)),{"&:before":{background:t}})]},Io(!!l.top,{[[`&-placement-top > ${n}-arrow`,`&-placement-topLeft > ${n}-arrow`,`&-placement-topRight > ${n}-arrow`].join(",")]:{bottom:s,transform:"translateY(100%) rotate(180deg)"},[`&-placement-top > ${n}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(100%) rotate(180deg)"},"&-placement-topLeft":{"--arrow-offset-horizontal":a,[`> ${n}-arrow`]:{left:{_skip_check_:!0,value:a}}},"&-placement-topRight":{"--arrow-offset-horizontal":`calc(100% - ${ie(a)})`,[`> ${n}-arrow`]:{right:{_skip_check_:!0,value:a}}}})),Io(!!l.bottom,{[[`&-placement-bottom > ${n}-arrow`,`&-placement-bottomLeft > ${n}-arrow`,`&-placement-bottomRight > ${n}-arrow`].join(",")]:{top:s,transform:"translateY(-100%)"},[`&-placement-bottom > ${n}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(-100%)"},"&-placement-bottomLeft":{"--arrow-offset-horizontal":a,[`> ${n}-arrow`]:{left:{_skip_check_:!0,value:a}}},"&-placement-bottomRight":{"--arrow-offset-horizontal":`calc(100% - ${ie(a)})`,[`> ${n}-arrow`]:{right:{_skip_check_:!0,value:a}}}})),Io(!!l.left,{[[`&-placement-left > ${n}-arrow`,`&-placement-leftTop > ${n}-arrow`,`&-placement-leftBottom > ${n}-arrow`].join(",")]:{right:{_skip_check_:!0,value:s},transform:"translateX(100%) rotate(90deg)"},[`&-placement-left > ${n}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(100%) rotate(90deg)"},[`&-placement-leftTop > ${n}-arrow`]:{top:i},[`&-placement-leftBottom > ${n}-arrow`]:{bottom:i}})),Io(!!l.right,{[[`&-placement-right > ${n}-arrow`,`&-placement-rightTop > ${n}-arrow`,`&-placement-rightBottom > ${n}-arrow`].join(",")]:{left:{_skip_check_:!0,value:s},transform:"translateX(-100%) rotate(-90deg)"},[`&-placement-right > ${n}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(-100%) rotate(-90deg)"},[`&-placement-rightTop > ${n}-arrow`]:{top:i},[`&-placement-rightBottom > ${n}-arrow`]:{bottom:i}}))}}function c0(e,t,r,n){if(n===!1)return{adjustX:!1,adjustY:!1};const o=n&&typeof n=="object"?n:{},i={};switch(e){case"top":case"bottom":i.shiftX=t.arrowOffsetHorizontal*2+r,i.shiftY=!0,i.adjustY=!0;break;case"left":case"right":i.shiftY=t.arrowOffsetVertical*2+r,i.shiftX=!0,i.adjustX=!0;break}const a=Object.assign(Object.assign({},i),o);return a.shiftX||(a.adjustX=!0),a.shiftY||(a.adjustY=!0),a}const Bl={left:{points:["cr","cl"]},right:{points:["cl","cr"]},top:{points:["bc","tc"]},bottom:{points:["tc","bc"]},topLeft:{points:["bl","tl"]},leftTop:{points:["tr","tl"]},topRight:{points:["br","tr"]},rightTop:{points:["tl","tr"]},bottomRight:{points:["tr","br"]},rightBottom:{points:["bl","br"]},bottomLeft:{points:["tl","bl"]},leftBottom:{points:["br","bl"]}},u0={topLeft:{points:["bl","tc"]},leftTop:{points:["tr","cl"]},topRight:{points:["br","tc"]},rightTop:{points:["tl","cr"]},bottomRight:{points:["tr","bc"]},rightBottom:{points:["bl","cr"]},bottomLeft:{points:["tl","bc"]},leftBottom:{points:["br","cl"]}},d0=new Set(["topLeft","topRight","bottomLeft","bottomRight","leftTop","leftBottom","rightTop","rightBottom"]);function f0(e){const{arrowWidth:t,autoAdjustOverflow:r,arrowPointAtCenter:n,offset:o,borderRadius:i}=e,a=t/2,s={};return Object.keys(Bl).forEach(l=>{const u=n&&u0[l]||Bl[l],d=Object.assign(Object.assign({},u),{offset:[0,0],dynamicInset:!0});switch(s[l]=d,d0.has(l)&&(d.autoArrow=!1),l){case"top":case"topLeft":case"topRight":d.offset[1]=-a-o;break;case"bottom":case"bottomLeft":case"bottomRight":d.offset[1]=a+o;break;case"left":case"leftTop":case"leftBottom":d.offset[0]=-a-o;break;case"right":case"rightTop":case"rightBottom":d.offset[0]=a+o;break}const f=Lu({contentRadius:i,limitVerticalRadius:!0});if(n)switch(l){case"topLeft":case"bottomLeft":d.offset[0]=-f.arrowOffsetHorizontal-a;break;case"topRight":case"bottomRight":d.offset[0]=f.arrowOffsetHorizontal+a;break;case"leftTop":case"rightTop":d.offset[1]=-f.arrowOffsetHorizontal*2+a;break;case"leftBottom":case"rightBottom":d.offset[1]=f.arrowOffsetHorizontal*2-a;break}d.overflow=c0(l,f,t,r),d.htmlRegion="visibleFirst"}),s}const v0=e=>{const{calc:t,componentCls:r,tooltipMaxWidth:n,tooltipColor:o,tooltipBg:i,tooltipBorderRadius:a,zIndexPopup:s,controlHeight:l,boxShadowSecondary:u,paddingSM:d,paddingXS:f,arrowOffsetHorizontal:m,sizePopupArrow:p}=e,g=t(a).add(p).add(m).equal(),h=t(a).mul(2).add(p).equal();return[{[r]:Object.assign(Object.assign(Object.assign(Object.assign({},Uc(e)),{position:"absolute",zIndex:s,display:"block",width:"max-content",maxWidth:n,visibility:"visible","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"&-hidden":{display:"none"},"--antd-arrow-background-color":i,[`${r}-inner`]:{minWidth:h,minHeight:l,padding:`${ie(e.calc(d).div(2).equal())} ${ie(f)}`,color:o,textAlign:"start",textDecoration:"none",wordWrap:"break-word",backgroundColor:i,borderRadius:a,boxShadow:u,boxSizing:"border-box"},[["&-placement-topLeft","&-placement-topRight","&-placement-bottomLeft","&-placement-bottomRight"].join(",")]:{minWidth:g},[["&-placement-left","&-placement-leftTop","&-placement-leftBottom","&-placement-right","&-placement-rightTop","&-placement-rightBottom"].join(",")]:{[`${r}-inner`]:{borderRadius:e.min(a,Nu)}},[`${r}-content`]:{position:"relative"}}),Yv(e,(v,{darkColor:y})=>({[`&${r}-${v}`]:{[`${r}-inner`]:{backgroundColor:y},[`${r}-arrow`]:{"--antd-arrow-background-color":y}}}))),{"&-rtl":{direction:"rtl"}})},l0(e,"var(--antd-arrow-background-color)"),{[`${r}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow}}]},m0=e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+70},Lu({contentRadius:e.borderRadius,limitVerticalRadius:!0})),a0(sr(e,{borderRadiusOuter:Math.min(e.borderRadiusOuter,4)}))),Bu=(e,t=!0)=>co("Tooltip",n=>{const{borderRadius:o,colorTextLightSolid:i,colorBgSpotlight:a}=n,s=sr(n,{tooltipMaxWidth:250,tooltipColor:i,tooltipBorderRadius:o,tooltipBg:a});return[v0(s),bu(n,"zoom-big-fast")]},m0,{resetStyle:!1,injectStyle:t})(e),g0=Ur.map(e=>`${e}-inverse`);function h0(e,t=!0){return t?[].concat(q(g0),q(Ur)).includes(e):Ur.includes(e)}function zu(e,t){const r=h0(t),n=Q({[`${e}-${t}`]:t&&r}),o={},i={};return t&&!r&&(o.background=t,i["--antd-arrow-background-color"]=t),{className:n,overlayStyle:o,arrowStyle:i}}const p0=e=>{const{prefixCls:t,className:r,placement:n="top",title:o,color:i,overlayInnerStyle:a}=e,{getPrefixCls:s}=c.useContext(vt),l=s("tooltip",t),[u,d,f]=Bu(l),m=zu(l,i),p=m.arrowStyle,g=Object.assign(Object.assign({},a),m.overlayStyle),h=Q(d,f,l,`${l}-pure`,`${l}-placement-${n}`,r,m.className);return u(c.createElement("div",{className:h,style:p},c.createElement("div",{className:`${l}-arrow`}),c.createElement(ju,Object.assign({},e,{className:d,prefixCls:l,overlayInnerStyle:g}),o)))};var b0=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const y0=c.forwardRef((e,t)=>{var r,n;const{prefixCls:o,openClassName:i,getTooltipContainer:a,color:s,overlayInnerStyle:l,children:u,afterOpenChange:d,afterVisibleChange:f,destroyTooltipOnHide:m,destroyOnHidden:p,arrow:g=!0,title:h,overlay:v,builtinPlacements:y,arrowPointAtCenter:b=!1,autoAdjustOverflow:C=!0,motion:w,getPopupContainer:S,placement:x="top",mouseEnterDelay:E=.1,mouseLeaveDelay:O=.1,overlayStyle:I,rootClassName:R,overlayClassName:A,styles:M,classNames:T}=e,_=b0(e,["prefixCls","openClassName","getTooltipContainer","color","overlayInnerStyle","children","afterOpenChange","afterVisibleChange","destroyTooltipOnHide","destroyOnHidden","arrow","title","overlay","builtinPlacements","arrowPointAtCenter","autoAdjustOverflow","motion","getPopupContainer","placement","mouseEnterDelay","mouseLeaveDelay","overlayStyle","rootClassName","overlayClassName","styles","classNames"]),N=!!g,[,F]=Sr(),{getPopupContainer:L,getPrefixCls:B,direction:V,className:K,style:U,classNames:z,styles:X}=es("tooltip"),H=Fc(),k=c.useRef(null),ae=()=>{var ge;(ge=k.current)===null||ge===void 0||ge.forceAlign()};c.useImperativeHandle(t,()=>{var ge,xe;return{forceAlign:ae,forcePopupAlign:()=>{H.deprecated(!1,"forcePopupAlign","forceAlign"),ae()},nativeElement:(ge=k.current)===null||ge===void 0?void 0:ge.nativeElement,popupElement:(xe=k.current)===null||xe===void 0?void 0:xe.popupElement}});const[se,Y]=Hn(!1,{value:(r=e.open)!==null&&r!==void 0?r:e.visible,defaultValue:(n=e.defaultOpen)!==null&&n!==void 0?n:e.defaultVisible}),G=!h&&!v&&h!==0,ue=ge=>{var xe,Be;Y(G?!1:ge),G||((xe=e.onOpenChange)===null||xe===void 0||xe.call(e,ge),(Be=e.onVisibleChange)===null||Be===void 0||Be.call(e,ge))},Se=c.useMemo(()=>{var ge,xe;let Be=b;return typeof g=="object"&&(Be=(xe=(ge=g.pointAtCenter)!==null&&ge!==void 0?ge:g.arrowPointAtCenter)!==null&&xe!==void 0?xe:b),y||f0({arrowPointAtCenter:Be,autoAdjustOverflow:C,arrowWidth:N?F.sizePopupArrow:0,borderRadius:F.borderRadius,offset:F.marginXXS})},[b,g,y,F]),me=c.useMemo(()=>h===0?h:v||h||"",[v,h]),be=c.createElement(Pp,{space:!0},typeof me=="function"?me():me),he=B("tooltip",o),$e=B(),Ee=e["data-popover-inject"];let _e=se;!("open"in e)&&!("visible"in e)&&G&&(_e=!1);const Oe=c.isValidElement(u)&&!uu(u)?u:c.createElement("span",null,u),we=Oe.props,He=!we.className||typeof we.className=="string"?Q(we.className,i||`${he}-open`):we.className,[ce,pe,ye]=Bu(he,!Ee),te=zu(he,s),qe=te.arrowStyle,de=Q(A,{[`${he}-rtl`]:V==="rtl"},te.className,R,pe,ye,K,z.root,T?.root),ct=Q(z.body,T?.body),[Ge,Pe]=vu("Tooltip",_.zIndex),et=c.createElement(i0,Object.assign({},_,{zIndex:Ge,showArrow:N,placement:x,mouseEnterDelay:E,mouseLeaveDelay:O,prefixCls:he,classNames:{root:de,body:ct},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},qe),X.root),U),I),M?.root),body:Object.assign(Object.assign(Object.assign(Object.assign({},X.body),l),M?.body),te.overlayStyle)},getTooltipContainer:S||a||L,ref:k,builtinPlacements:Se,overlay:be,visible:_e,onVisibleChange:ue,afterVisibleChange:d??f,arrowContent:c.createElement("span",{className:`${he}-arrow-content`}),motion:{motionName:Ym($e,"zoom-big-fast",e.transitionName),motionDeadline:1e3},destroyTooltipOnHide:p??!!m}),_e?wn(Oe,{className:He}):Oe);return ce(c.createElement(du.Provider,{value:Pe},et))}),ku=y0;ku._InternalPanelDoNotUseOrYouWillBeFired=p0;var C0={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"},S0=function(t,r){return c.createElement(At,oe({},t,{ref:r,icon:C0}))},zl=c.forwardRef(S0),Hu=c.createContext(null);function Du(e,t){return e===void 0?null:"".concat(e,"-").concat(t)}function Vu(e){var t=c.useContext(Hu);return Du(t,e)}var x0=["children","locked"],Gt=c.createContext(null);function w0(e,t){var r=P({},e);return Object.keys(t).forEach(function(n){var o=t[n];o!==void 0&&(r[n]=o)}),r}function no(e){var t=e.children,r=e.locked,n=ke(e,x0),o=c.useContext(Gt),i=Qo(function(){return w0(o,n)},[o,n],function(a,s){return!r&&(a[0]!==s[0]||!Qn(a[1],s[1],!0))});return c.createElement(Gt.Provider,{value:i},t)}var $0=[],Wu=c.createContext(null);function di(){return c.useContext(Wu)}var qu=c.createContext($0);function $n(e){var t=c.useContext(qu);return c.useMemo(function(){return e!==void 0?[].concat(q(t),[e]):t},[t,e])}var Uu=c.createContext(null),ls=c.createContext({});function kl(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(os(e)){var r=e.nodeName.toLowerCase(),n=["input","select","textarea","button"].includes(r)||e.isContentEditable||r==="a"&&!!e.getAttribute("href"),o=e.getAttribute("tabindex"),i=Number(o),a=null;return o&&!Number.isNaN(i)?a=i:n&&a===null&&(a=0),n&&e.disabled&&(a=null),a!==null&&(a>=0||t&&a<0)}return!1}function E0(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,r=q(e.querySelectorAll("*")).filter(function(n){return kl(n,t)});return kl(e,t)&&r.unshift(e),r}var Aa=xr.LEFT,Fa=xr.RIGHT,ja=xr.UP,No=xr.DOWN,Lo=xr.ENTER,Ku=xr.ESC,jn=xr.HOME,Nn=xr.END,Hl=[ja,No,Aa,Fa];function O0(e,t,r,n){var o,i="prev",a="next",s="children",l="parent";if(e==="inline"&&n===Lo)return{inlineTrigger:!0};var u=$($({},ja,i),No,a),d=$($($($({},Aa,r?a:i),Fa,r?i:a),No,s),Lo,s),f=$($($($($($({},ja,i),No,a),Lo,s),Ku,l),Aa,r?s:l),Fa,r?l:s),m={inline:u,horizontal:d,vertical:f,inlineSub:u,horizontalSub:f,verticalSub:f},p=(o=m["".concat(e).concat(t?"":"Sub")])===null||o===void 0?void 0:o[n];switch(p){case i:return{offset:-1,sibling:!0};case a:return{offset:1,sibling:!0};case l:return{offset:-1,sibling:!1};case s:return{offset:1,sibling:!1};default:return null}}function I0(e){for(var t=e;t;){if(t.getAttribute("data-menu-list"))return t;t=t.parentElement}return null}function P0(e,t){for(var r=e||document.activeElement;r;){if(t.has(r))return r;r=r.parentElement}return null}function cs(e,t){var r=E0(e,!0);return r.filter(function(n){return t.has(n)})}function Dl(e,t,r){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:1;if(!e)return null;var o=cs(e,t),i=o.length,a=o.findIndex(function(s){return r===s});return n<0?a===-1?a=i-1:a-=1:n>0&&(a+=1),a=(a+i)%i,o[a]}var Na=function(t,r){var n=new Set,o=new Map,i=new Map;return t.forEach(function(a){var s=document.querySelector("[data-menu-id='".concat(Du(r,a),"']"));s&&(n.add(s),i.set(s,a),o.set(a,s))}),{elements:n,key2element:o,element2key:i}};function R0(e,t,r,n,o,i,a,s,l,u){var d=c.useRef(),f=c.useRef();f.current=t;var m=function(){Tt.cancel(d.current)};return c.useEffect(function(){return function(){m()}},[]),function(p){var g=p.which;if([].concat(Hl,[Lo,Ku,jn,Nn]).includes(g)){var h=i(),v=Na(h,n),y=v,b=y.elements,C=y.key2element,w=y.element2key,S=C.get(t),x=P0(S,b),E=w.get(x),O=O0(e,a(E,!0).length===1,r,g);if(!O&&g!==jn&&g!==Nn)return;(Hl.includes(g)||[jn,Nn].includes(g))&&p.preventDefault();var I=function(L){if(L){var B=L,V=L.querySelector("a");V!=null&&V.getAttribute("href")&&(B=V);var K=w.get(L);s(K),m(),d.current=Tt(function(){f.current===K&&B.focus()})}};if([jn,Nn].includes(g)||O.sibling||!x){var R;!x||e==="inline"?R=o.current:R=I0(x);var A,M=cs(R,b);g===jn?A=M[0]:g===Nn?A=M[M.length-1]:A=Dl(R,b,x,O.offset),I(A)}else if(O.inlineTrigger)l(E);else if(O.offset>0)l(E,!0),m(),d.current=Tt(function(){v=Na(h,n);var F=x.getAttribute("aria-controls"),L=document.getElementById(F),B=Dl(L,v.elements);I(B)},5);else if(O.offset<0){var T=a(E,!0),_=T[T.length-2],N=C.get(_);l(_,!1),I(N)}}u?.(p)}}function M0(e){Promise.resolve().then(e)}var us="__RC_UTIL_PATH_SPLIT__",Vl=function(t){return t.join(us)},T0=function(t){return t.split(us)},La="rc-menu-more";function _0(){var e=c.useState({}),t=j(e,2),r=t[1],n=c.useRef(new Map),o=c.useRef(new Map),i=c.useState([]),a=j(i,2),s=a[0],l=a[1],u=c.useRef(0),d=c.useRef(!1),f=function(){d.current||r({})},m=c.useCallback(function(C,w){var S=Vl(w);o.current.set(S,C),n.current.set(C,S),u.current+=1;var x=u.current;M0(function(){x===u.current&&f()})},[]),p=c.useCallback(function(C,w){var S=Vl(w);o.current.delete(S),n.current.delete(C)},[]),g=c.useCallback(function(C){l(C)},[]),h=c.useCallback(function(C,w){var S=n.current.get(C)||"",x=T0(S);return w&&s.includes(x[0])&&x.unshift(La),x},[s]),v=c.useCallback(function(C,w){return C.filter(function(S){return S!==void 0}).some(function(S){var x=h(S,!0);return x.includes(w)})},[h]),y=function(){var w=q(n.current.keys());return s.length&&w.push(La),w},b=c.useCallback(function(C){var w="".concat(n.current.get(C)).concat(us),S=new Set;return q(o.current.keys()).forEach(function(x){x.startsWith(w)&&S.add(o.current.get(x))}),S},[]);return c.useEffect(function(){return function(){d.current=!0}},[]),{registerPath:m,unregisterPath:p,refreshOverflowKeys:g,isSubPathKey:v,getKeyPath:h,getKeys:y,getSubPathKeys:b}}function zn(e){var t=c.useRef(e);t.current=e;var r=c.useCallback(function(){for(var n,o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return(n=t.current)===null||n===void 0?void 0:n.call.apply(n,[t].concat(i))},[]);return e?r:void 0}var A0=Math.random().toFixed(5).toString().slice(2),Wl=0;function F0(e){var t=Hn(e,{value:e}),r=j(t,2),n=r[0],o=r[1];return c.useEffect(function(){Wl+=1;var i="".concat(A0,"-").concat(Wl);o("rc-menu-uuid-".concat(i))},[]),n}function Gu(e,t,r,n){var o=c.useContext(Gt),i=o.activeKey,a=o.onActive,s=o.onInactive,l={active:i===e};return t||(l.onMouseEnter=function(u){r?.({key:e,domEvent:u}),a(e)},l.onMouseLeave=function(u){n?.({key:e,domEvent:u}),s(e)}),l}function Xu(e){var t=c.useContext(Gt),r=t.mode,n=t.rtl,o=t.inlineIndent;if(r!=="inline")return null;var i=e;return n?{paddingRight:i*o}:{paddingLeft:i*o}}function Qu(e){var t=e.icon,r=e.props,n=e.children,o;return t===null||t===!1?null:(typeof t=="function"?o=c.createElement(t,P({},r)):typeof t!="boolean"&&(o=t),o||n||null)}var j0=["item"];function Ko(e){var t=e.item,r=ke(e,j0);return Object.defineProperty(r,"item",{get:function(){return St(!1,"`info.item` is deprecated since we will move to function component that not provides React Node instance in future."),t}}),r}var N0=["title","attribute","elementRef"],L0=["style","className","eventKey","warnKey","disabled","itemIcon","children","role","onMouseEnter","onMouseLeave","onClick","onKeyDown","onFocus"],B0=["active"],z0=function(e){yr(r,e);var t=Cr(r);function r(){return st(this,r),t.apply(this,arguments)}return lt(r,[{key:"render",value:function(){var o=this.props,i=o.title,a=o.attribute,s=o.elementRef,l=ke(o,N0),u=wr(l,["eventKey","popupClassName","popupOffset","onTitleClick"]);return St(!a,"`attribute` of Menu.Item is deprecated. Please pass attribute directly."),c.createElement(br.Item,oe({},a,{title:typeof i=="string"?i:void 0},u,{ref:s}))}}]),r}(c.Component),k0=c.forwardRef(function(e,t){var r=e.style,n=e.className,o=e.eventKey;e.warnKey;var i=e.disabled,a=e.itemIcon,s=e.children,l=e.role,u=e.onMouseEnter,d=e.onMouseLeave,f=e.onClick,m=e.onKeyDown,p=e.onFocus,g=ke(e,L0),h=Vu(o),v=c.useContext(Gt),y=v.prefixCls,b=v.onItemClick,C=v.disabled,w=v.overflowDisabled,S=v.itemIcon,x=v.selectedKeys,E=v.onActive,O=c.useContext(ls),I=O._internalRenderMenuItem,R="".concat(y,"-item"),A=c.useRef(),M=c.useRef(),T=C||i,_=io(t,M),N=$n(o),F=function(G){return{key:o,keyPath:q(N).reverse(),item:A.current,domEvent:G}},L=a||S,B=Gu(o,T,u,d),V=B.active,K=ke(B,B0),U=x.includes(o),z=Xu(N.length),X=function(G){if(!T){var ue=F(G);f?.(Ko(ue)),b(ue)}},H=function(G){if(m?.(G),G.which===xr.ENTER){var ue=F(G);f?.(Ko(ue)),b(ue)}},k=function(G){E(o),p?.(G)},ae={};e.role==="option"&&(ae["aria-selected"]=U);var se=c.createElement(z0,oe({ref:A,elementRef:_,role:l===null?"none":l||"menuitem",tabIndex:i?null:-1,"data-menu-id":w&&h?null:h},wr(g,["extra"]),K,ae,{component:"li","aria-disabled":i,style:P(P({},z),r),className:Q(R,$($($({},"".concat(R,"-active"),V),"".concat(R,"-selected"),U),"".concat(R,"-disabled"),T),n),onClick:X,onKeyDown:H,onFocus:k}),s,c.createElement(Qu,{props:P(P({},e),{},{isSelected:U}),icon:L}));return I&&(se=I(se,e,{selected:U})),se});function H0(e,t){var r=e.eventKey,n=di(),o=$n(r);return c.useEffect(function(){if(n)return n.registerPath(r,o),function(){n.unregisterPath(r,o)}},[o]),n?null:c.createElement(k0,oe({},e,{ref:t}))}const fi=c.forwardRef(H0);var D0=["className","children"],V0=function(t,r){var n=t.className,o=t.children,i=ke(t,D0),a=c.useContext(Gt),s=a.prefixCls,l=a.mode,u=a.rtl;return c.createElement("ul",oe({className:Q(s,u&&"".concat(s,"-rtl"),"".concat(s,"-sub"),"".concat(s,"-").concat(l==="inline"?"inline":"vertical"),n),role:"menu"},i,{"data-menu-list":!0,ref:r}),o)},ds=c.forwardRef(V0);ds.displayName="SubMenuList";function fs(e,t){return Wr(e).map(function(r,n){if(c.isValidElement(r)){var o,i,a=r.key,s=(o=(i=r.props)===null||i===void 0?void 0:i.eventKey)!==null&&o!==void 0?o:a,l=s==null;l&&(s="tmp_key-".concat([].concat(q(t),[n]).join("-")));var u={key:s,eventKey:s};return c.cloneElement(r,u)}return r})}var bt={adjustX:1,adjustY:1},W0={topLeft:{points:["bl","tl"],overflow:bt},topRight:{points:["br","tr"],overflow:bt},bottomLeft:{points:["tl","bl"],overflow:bt},bottomRight:{points:["tr","br"],overflow:bt},leftTop:{points:["tr","tl"],overflow:bt},leftBottom:{points:["br","bl"],overflow:bt},rightTop:{points:["tl","tr"],overflow:bt},rightBottom:{points:["bl","br"],overflow:bt}},q0={topLeft:{points:["bl","tl"],overflow:bt},topRight:{points:["br","tr"],overflow:bt},bottomLeft:{points:["tl","bl"],overflow:bt},bottomRight:{points:["tr","br"],overflow:bt},rightTop:{points:["tr","tl"],overflow:bt},rightBottom:{points:["br","bl"],overflow:bt},leftTop:{points:["tl","tr"],overflow:bt},leftBottom:{points:["bl","br"],overflow:bt}};function Zu(e,t,r){if(t)return t;if(r)return r[e]||r.other}var U0={horizontal:"bottomLeft",vertical:"rightTop","vertical-left":"rightTop","vertical-right":"leftTop"};function K0(e){var t=e.prefixCls,r=e.visible,n=e.children,o=e.popup,i=e.popupStyle,a=e.popupClassName,s=e.popupOffset,l=e.disabled,u=e.mode,d=e.onVisibleChange,f=c.useContext(Gt),m=f.getPopupContainer,p=f.rtl,g=f.subMenuOpenDelay,h=f.subMenuCloseDelay,v=f.builtinPlacements,y=f.triggerSubMenuAction,b=f.forceSubMenuRender,C=f.rootClassName,w=f.motion,S=f.defaultMotions,x=c.useState(!1),E=j(x,2),O=E[0],I=E[1],R=p?P(P({},q0),v):P(P({},W0),v),A=U0[u],M=Zu(u,w,S),T=c.useRef(M);u!=="inline"&&(T.current=M);var _=P(P({},T.current),{},{leavedClassName:"".concat(t,"-hidden"),removeOnLeave:!1,motionAppear:!0}),N=c.useRef();return c.useEffect(function(){return N.current=Tt(function(){I(r)}),function(){Tt.cancel(N.current)}},[r]),c.createElement(Fu,{prefixCls:t,popupClassName:Q("".concat(t,"-popup"),$({},"".concat(t,"-rtl"),p),a,C),stretch:u==="horizontal"?"minWidth":null,getPopupContainer:m,builtinPlacements:R,popupPlacement:A,popupVisible:O,popup:o,popupStyle:i,popupAlign:s&&{offset:s},action:l?[]:[y],mouseEnterDelay:g,mouseLeaveDelay:h,onPopupVisibleChange:d,forceRender:b,popupMotion:_,fresh:!0},n)}function G0(e){var t=e.id,r=e.open,n=e.keyPath,o=e.children,i="inline",a=c.useContext(Gt),s=a.prefixCls,l=a.forceSubMenuRender,u=a.motion,d=a.defaultMotions,f=a.mode,m=c.useRef(!1);m.current=f===i;var p=c.useState(!m.current),g=j(p,2),h=g[0],v=g[1],y=m.current?r:!1;c.useEffect(function(){m.current&&v(!1)},[f]);var b=P({},Zu(i,u,d));n.length>1&&(b.motionAppear=!1);var C=b.onVisibleChanged;return b.onVisibleChanged=function(w){return!m.current&&!w&&v(!0),C?.(w)},h?null:c.createElement(no,{mode:i,locked:!m.current},c.createElement(Sn,oe({visible:y},b,{forceRender:l,removeOnLeave:!1,leavedClassName:"".concat(s,"-hidden")}),function(w){var S=w.className,x=w.style;return c.createElement(ds,{id:t,className:S,style:x},o)}))}var X0=["style","className","title","eventKey","warnKey","disabled","internalPopupClose","children","itemIcon","expandIcon","popupClassName","popupOffset","popupStyle","onClick","onMouseEnter","onMouseLeave","onTitleClick","onTitleMouseEnter","onTitleMouseLeave"],Q0=["active"],Z0=c.forwardRef(function(e,t){var r=e.style,n=e.className,o=e.title,i=e.eventKey;e.warnKey;var a=e.disabled,s=e.internalPopupClose,l=e.children,u=e.itemIcon,d=e.expandIcon,f=e.popupClassName,m=e.popupOffset,p=e.popupStyle,g=e.onClick,h=e.onMouseEnter,v=e.onMouseLeave,y=e.onTitleClick,b=e.onTitleMouseEnter,C=e.onTitleMouseLeave,w=ke(e,X0),S=Vu(i),x=c.useContext(Gt),E=x.prefixCls,O=x.mode,I=x.openKeys,R=x.disabled,A=x.overflowDisabled,M=x.activeKey,T=x.selectedKeys,_=x.itemIcon,N=x.expandIcon,F=x.onItemClick,L=x.onOpenChange,B=x.onActive,V=c.useContext(ls),K=V._internalRenderSubMenuItem,U=c.useContext(Uu),z=U.isSubPathKey,X=$n(),H="".concat(E,"-submenu"),k=R||a,ae=c.useRef(),se=c.useRef(),Y=u??_,G=d??N,ue=I.includes(i),Se=!A&&ue,me=z(T,i),be=Gu(i,k,b,C),he=be.active,$e=ke(be,Q0),Ee=c.useState(!1),_e=j(Ee,2),Oe=_e[0],we=_e[1],He=function(le){k||we(le)},ce=function(le){He(!0),h?.({key:i,domEvent:le})},pe=function(le){He(!1),v?.({key:i,domEvent:le})},ye=c.useMemo(function(){return he||(O!=="inline"?Oe||z([M],i):!1)},[O,he,M,Oe,i,z]),te=Xu(X.length),qe=function(le){k||(y?.({key:i,domEvent:le}),O==="inline"&&L(i,!ue))},de=zn(function(Me){g?.(Ko(Me)),F(Me)}),ct=function(le){O!=="inline"&&L(i,le)},Ge=function(){B(i)},Pe=S&&"".concat(S,"-popup"),et=c.useMemo(function(){return c.createElement(Qu,{icon:O!=="horizontal"?G:void 0,props:P(P({},e),{},{isOpen:Se,isSubMenu:!0})},c.createElement("i",{className:"".concat(H,"-arrow")}))},[O,G,e,Se,H]),ge=c.createElement("div",oe({role:"menuitem",style:te,className:"".concat(H,"-title"),tabIndex:k?null:-1,ref:ae,title:typeof o=="string"?o:null,"data-menu-id":A&&S?null:S,"aria-expanded":Se,"aria-haspopup":!0,"aria-controls":Pe,"aria-disabled":k,onClick:qe,onFocus:Ge},$e),o,et),xe=c.useRef(O);if(O!=="inline"&&X.length>1?xe.current="vertical":xe.current=O,!A){var Be=xe.current;ge=c.createElement(K0,{mode:Be,prefixCls:H,visible:!s&&Se&&O!=="inline",popupClassName:f,popupOffset:m,popupStyle:p,popup:c.createElement(no,{mode:Be==="horizontal"?"vertical":Be},c.createElement(ds,{id:Pe,ref:se},l)),disabled:k,onVisibleChange:ct},ge)}var Re=c.createElement(br.Item,oe({ref:t,role:"none"},w,{component:"li",style:r,className:Q(H,"".concat(H,"-").concat(O),n,$($($($({},"".concat(H,"-open"),Se),"".concat(H,"-active"),ye),"".concat(H,"-selected"),me),"".concat(H,"-disabled"),k)),onMouseEnter:ce,onMouseLeave:pe}),ge,!A&&c.createElement(G0,{id:Pe,open:Se,keyPath:X},l));return K&&(Re=K(Re,e,{selected:me,active:ye,open:Se,disabled:k})),c.createElement(no,{onItemClick:de,mode:O==="horizontal"?"vertical":O,itemIcon:Y,expandIcon:G},Re)}),vi=c.forwardRef(function(e,t){var r=e.eventKey,n=e.children,o=$n(r),i=fs(n,o),a=di();c.useEffect(function(){if(a)return a.registerPath(r,o),function(){a.unregisterPath(r,o)}},[o]);var s;return a?s=i:s=c.createElement(Z0,oe({ref:t},e),i),c.createElement(qu.Provider,{value:o},s)});function vs(e){var t=e.className,r=e.style,n=c.useContext(Gt),o=n.prefixCls,i=di();return i?null:c.createElement("li",{role:"separator",className:Q("".concat(o,"-item-divider"),t),style:r})}var Y0=["className","title","eventKey","children"],J0=c.forwardRef(function(e,t){var r=e.className,n=e.title;e.eventKey;var o=e.children,i=ke(e,Y0),a=c.useContext(Gt),s=a.prefixCls,l="".concat(s,"-item-group");return c.createElement("li",oe({ref:t,role:"presentation"},i,{onClick:function(d){return d.stopPropagation()},className:Q(l,r)}),c.createElement("div",{role:"presentation",className:"".concat(l,"-title"),title:typeof n=="string"?n:void 0},n),c.createElement("ul",{role:"group",className:"".concat(l,"-list")},o))}),ms=c.forwardRef(function(e,t){var r=e.eventKey,n=e.children,o=$n(r),i=fs(n,o),a=di();return a?i:c.createElement(J0,oe({ref:t},wr(e,["warnKey"])),i)}),eb=["label","children","key","type","extra"];function Ba(e,t,r){var n=t.item,o=t.group,i=t.submenu,a=t.divider;return(e||[]).map(function(s,l){if(s&&ve(s)==="object"){var u=s,d=u.label,f=u.children,m=u.key,p=u.type,g=u.extra,h=ke(u,eb),v=m??"tmp-".concat(l);return f||p==="group"?p==="group"?c.createElement(o,oe({key:v},h,{title:d}),Ba(f,t,r)):c.createElement(i,oe({key:v},h,{title:d}),Ba(f,t,r)):p==="divider"?c.createElement(a,oe({key:v},h)):c.createElement(n,oe({key:v},h,{extra:g}),d,(!!g||g===0)&&c.createElement("span",{className:"".concat(r,"-item-extra")},g))}return null}).filter(function(s){return s})}function ql(e,t,r,n,o){var i=e,a=P({divider:vs,item:fi,group:ms,submenu:vi},n);return t&&(i=Ba(t,a,o)),fs(i,r)}var tb=["prefixCls","rootClassName","style","className","tabIndex","items","children","direction","id","mode","inlineCollapsed","disabled","disabledOverflow","subMenuOpenDelay","subMenuCloseDelay","forceSubMenuRender","defaultOpenKeys","openKeys","activeKey","defaultActiveFirst","selectable","multiple","defaultSelectedKeys","selectedKeys","onSelect","onDeselect","inlineIndent","motion","defaultMotions","triggerSubMenuAction","builtinPlacements","itemIcon","expandIcon","overflowedIndicator","overflowedIndicatorPopupClassName","getPopupContainer","onClick","onOpenChange","onKeyDown","openAnimation","openTransitionName","_internalRenderMenuItem","_internalRenderSubMenuItem","_internalComponents"],kr=[],rb=c.forwardRef(function(e,t){var r,n=e,o=n.prefixCls,i=o===void 0?"rc-menu":o,a=n.rootClassName,s=n.style,l=n.className,u=n.tabIndex,d=u===void 0?0:u,f=n.items,m=n.children,p=n.direction,g=n.id,h=n.mode,v=h===void 0?"vertical":h,y=n.inlineCollapsed,b=n.disabled,C=n.disabledOverflow,w=n.subMenuOpenDelay,S=w===void 0?.1:w,x=n.subMenuCloseDelay,E=x===void 0?.1:x,O=n.forceSubMenuRender,I=n.defaultOpenKeys,R=n.openKeys,A=n.activeKey,M=n.defaultActiveFirst,T=n.selectable,_=T===void 0?!0:T,N=n.multiple,F=N===void 0?!1:N,L=n.defaultSelectedKeys,B=n.selectedKeys,V=n.onSelect,K=n.onDeselect,U=n.inlineIndent,z=U===void 0?24:U,X=n.motion,H=n.defaultMotions,k=n.triggerSubMenuAction,ae=k===void 0?"hover":k,se=n.builtinPlacements,Y=n.itemIcon,G=n.expandIcon,ue=n.overflowedIndicator,Se=ue===void 0?"...":ue,me=n.overflowedIndicatorPopupClassName,be=n.getPopupContainer,he=n.onClick,$e=n.onOpenChange,Ee=n.onKeyDown;n.openAnimation,n.openTransitionName;var _e=n._internalRenderMenuItem,Oe=n._internalRenderSubMenuItem,we=n._internalComponents,He=ke(n,tb),ce=c.useMemo(function(){return[ql(m,f,kr,we,i),ql(m,f,kr,{},i)]},[m,f,we]),pe=j(ce,2),ye=pe[0],te=pe[1],qe=c.useState(!1),de=j(qe,2),ct=de[0],Ge=de[1],Pe=c.useRef(),et=F0(g),ge=p==="rtl",xe=Hn(I,{value:R,postState:function(Ie){return Ie||kr}}),Be=j(xe,2),Re=Be[0],Me=Be[1],le=function(Ie){var Le=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;function ut(){Me(Ie),$e?.(Ie)}Le?za.flushSync(ut):ut()},Ne=c.useState(Re),Ze=j(Ne,2),Xt=Ze[0],Qt=Ze[1],mt=c.useRef(!1),cr=c.useMemo(function(){return(v==="inline"||v==="vertical")&&y?["vertical",y]:[v,!1]},[v,y]),Ft=j(cr,2),Ye=Ft[0],Ue=Ft[1],De=Ye==="inline",Z=c.useState(Ye),J=j(Z,2),W=J[0],ne=J[1],Xe=c.useState(Ue),jt=j(Xe,2),Te=jt[0],ur=jt[1];c.useEffect(function(){ne(Ye),ur(Ue),mt.current&&(De?Me(Xt):le(kr))},[Ye,Ue]);var dr=c.useState(0),Ae=j(dr,2),Fe=Ae[0],gt=Ae[1],nt=Fe>=ye.length-1||W!=="horizontal"||C;c.useEffect(function(){De&&Qt(Re)},[Re]),c.useEffect(function(){return mt.current=!0,function(){mt.current=!1}},[]);var ot=_0(),Zt=ot.registerPath,Yt=ot.unregisterPath,xt=ot.refreshOverflowKeys,wt=ot.isSubPathKey,Vt=ot.getKeyPath,$t=ot.getKeys,ht=ot.getSubPathKeys,pt=c.useMemo(function(){return{registerPath:Zt,unregisterPath:Yt}},[Zt,Yt]),fr=c.useMemo(function(){return{isSubPathKey:wt}},[wt]);c.useEffect(function(){xt(nt?kr:ye.slice(Fe+1).map(function(We){return We.key}))},[Fe,nt]);var Kr=Hn(A||M&&((r=ye[0])===null||r===void 0?void 0:r.key),{value:A}),Gr=j(Kr,2),Et=Gr[0],$r=Gr[1],En=zn(function(We){$r(We)}),Xr=zn(function(){$r(void 0)});c.useImperativeHandle(t,function(){return{list:Pe.current,focus:function(Ie){var Le,ut=$t(),ft=Na(ut,et),Er=ft.elements,Or=ft.key2element,On=ft.element2key,Zr=cs(Pe.current,Er),Lr=Et??(Zr[0]?On.get(Zr[0]):(Le=ye.find(function(In){return!In.props.disabled}))===null||Le===void 0?void 0:Le.key),gr=Or.get(Lr);if(Lr&&gr){var Br;gr==null||(Br=gr.focus)===null||Br===void 0||Br.call(gr,Ie)}}}});var Qr=Hn(L||[],{value:B,postState:function(Ie){return Array.isArray(Ie)?Ie:Ie==null?kr:[Ie]}}),Jt=j(Qr,2),Nt=Jt[0],Fr=Jt[1],vr=function(Ie){if(_){var Le=Ie.key,ut=Nt.includes(Le),ft;F?ut?ft=Nt.filter(function(Or){return Or!==Le}):ft=[].concat(q(Nt),[Le]):ft=[Le],Fr(ft);var Er=P(P({},Ie),{},{selectedKeys:ft});ut?K?.(Er):V?.(Er)}!F&&Re.length&&W!=="inline"&&le(kr)},jr=zn(function(We){he?.(Ko(We)),vr(We)}),mr=zn(function(We,Ie){var Le=Re.filter(function(ft){return ft!==We});if(Ie)Le.push(We);else if(W!=="inline"){var ut=ht(We);Le=Le.filter(function(ft){return!ut.has(ft)})}Qn(Re,Le,!0)||le(Le,!0)}),Ot=function(Ie,Le){var ut=Le??!Re.includes(Ie);mr(Ie,ut)},er=R0(W,Et,ge,et,Pe,$t,Vt,$r,Ot,Ee);c.useEffect(function(){Ge(!0)},[]);var tr=c.useMemo(function(){return{_internalRenderMenuItem:_e,_internalRenderSubMenuItem:Oe}},[_e,Oe]),rr=W!=="horizontal"||C?ye:ye.map(function(We,Ie){return c.createElement(no,{key:We.key,overflowDisabled:Ie>Fe},We)}),Nr=c.createElement(br,oe({id:g,ref:Pe,prefixCls:"".concat(i,"-overflow"),component:"ul",itemComponent:fi,className:Q(i,"".concat(i,"-root"),"".concat(i,"-").concat(W),l,$($({},"".concat(i,"-inline-collapsed"),Te),"".concat(i,"-rtl"),ge),a),dir:p,style:s,role:"menu",tabIndex:d,data:rr,renderRawItem:function(Ie){return Ie},renderRawRest:function(Ie){var Le=Ie.length,ut=Le?ye.slice(-Le):null;return c.createElement(vi,{eventKey:La,title:Se,disabled:nt,internalPopupClose:Le===0,popupClassName:me},ut)},maxCount:W!=="horizontal"||C?br.INVALIDATE:br.RESPONSIVE,ssr:"full","data-menu-list":!0,onVisibleChange:function(Ie){gt(Ie)},onKeyDown:er},He));return c.createElement(ls.Provider,{value:tr},c.createElement(Hu.Provider,{value:et},c.createElement(no,{prefixCls:i,rootClassName:a,mode:W,openKeys:Re,rtl:ge,disabled:b,motion:ct?X:null,defaultMotions:ct?H:null,activeKey:Et,onActive:En,onInactive:Xr,selectedKeys:Nt,inlineIndent:z,subMenuOpenDelay:S,subMenuCloseDelay:E,forceSubMenuRender:O,builtinPlacements:se,triggerSubMenuAction:ae,getPopupContainer:be,itemIcon:Y,expandIcon:G,onItemClick:jr,onOpenChange:mr},c.createElement(Uu.Provider,{value:fr},Nr),c.createElement("div",{style:{display:"none"},"aria-hidden":!0},c.createElement(Wu.Provider,{value:pt},te)))))}),go=rb;go.Item=fi;go.SubMenu=vi;go.ItemGroup=ms;go.Divider=vs;var nb={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"},ob=function(t,r){return c.createElement(At,oe({},t,{ref:r,icon:nb}))},ib=c.forwardRef(ob);const Yu=c.createContext({siderHook:{addSider:()=>null,removeSider:()=>null}}),ab=e=>{const{antCls:t,componentCls:r,colorText:n,footerBg:o,headerHeight:i,headerPadding:a,headerColor:s,footerPadding:l,fontSize:u,bodyBg:d,headerBg:f}=e;return{[r]:{display:"flex",flex:"auto",flexDirection:"column",minHeight:0,background:d,"&, *":{boxSizing:"border-box"},[`&${r}-has-sider`]:{flexDirection:"row",[`> ${r}, > ${r}-content`]:{width:0}},[`${r}-header, &${r}-footer`]:{flex:"0 0 auto"},"&-rtl":{direction:"rtl"}},[`${r}-header`]:{height:i,padding:a,color:s,lineHeight:ie(i),background:f,[`${t}-menu`]:{lineHeight:"inherit"}},[`${r}-footer`]:{padding:l,color:n,fontSize:u,background:o},[`${r}-content`]:{flex:"auto",color:n,minHeight:0}}},Ju=e=>{const{colorBgLayout:t,controlHeight:r,controlHeightLG:n,colorText:o,controlHeightSM:i,marginXXS:a,colorTextLightSolid:s,colorBgContainer:l}=e,u=n*1.25;return{colorBgHeader:"#001529",colorBgBody:t,colorBgTrigger:"#002140",bodyBg:t,headerBg:"#001529",headerHeight:r*2,headerPadding:`0 ${u}px`,headerColor:o,footerPadding:`${i}px ${u}px`,footerBg:t,siderBg:"#001529",triggerHeight:n+a*2,triggerBg:"#002140",triggerColor:s,zeroTriggerWidth:n,zeroTriggerHeight:n,lightSiderBg:l,lightTriggerBg:l,lightTriggerColor:o}},ed=[["colorBgBody","bodyBg"],["colorBgHeader","headerBg"],["colorBgTrigger","triggerBg"]],td=co("Layout",e=>[ab(e)],Ju,{deprecatedTokens:ed}),sb=e=>{const{componentCls:t,siderBg:r,motionDurationMid:n,motionDurationSlow:o,antCls:i,triggerHeight:a,triggerColor:s,triggerBg:l,headerHeight:u,zeroTriggerWidth:d,zeroTriggerHeight:f,borderRadiusLG:m,lightSiderBg:p,lightTriggerColor:g,lightTriggerBg:h,bodyBg:v}=e;return{[t]:{position:"relative",minWidth:0,background:r,transition:`all ${n}, background 0s`,"&-has-trigger":{paddingBottom:a},"&-right":{order:1},[`${t}-children`]:{height:"100%",marginTop:-.1,paddingTop:.1,[`${i}-menu${i}-menu-inline-collapsed`]:{width:"auto"}},[`&-zero-width ${t}-children`]:{overflow:"hidden"},[`${t}-trigger`]:{position:"fixed",bottom:0,zIndex:1,height:a,color:s,lineHeight:ie(a),textAlign:"center",background:l,cursor:"pointer",transition:`all ${n}`},[`${t}-zero-width-trigger`]:{position:"absolute",top:u,insetInlineEnd:e.calc(d).mul(-1).equal(),zIndex:1,width:d,height:f,color:s,fontSize:e.fontSizeXL,display:"flex",alignItems:"center",justifyContent:"center",background:r,borderRadius:`0 ${ie(m)} ${ie(m)} 0`,cursor:"pointer",transition:`background ${o} ease`,"&::after":{position:"absolute",inset:0,background:"transparent",transition:`all ${o}`,content:'""'},"&:hover::after":{background:"rgba(255, 255, 255, 0.2)"},"&-right":{insetInlineStart:e.calc(d).mul(-1).equal(),borderRadius:`${ie(m)} 0 0 ${ie(m)}`}},"&-light":{background:p,[`${t}-trigger`]:{color:g,background:h},[`${t}-zero-width-trigger`]:{color:g,background:h,border:`1px solid ${v}`,borderInlineStart:0}}}}},lb=co(["Layout","Sider"],e=>[sb(e)],Ju,{deprecatedTokens:ed});var cb=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Ul={xs:"479.98px",sm:"575.98px",md:"767.98px",lg:"991.98px",xl:"1199.98px",xxl:"1599.98px"},ub=e=>!Number.isNaN(Number.parseFloat(e))&&isFinite(e),mi=c.createContext({}),db=(()=>{let e=0;return(t="")=>(e+=1,`${t}${e}`)})(),rd=c.forwardRef((e,t)=>{const{prefixCls:r,className:n,trigger:o,children:i,defaultCollapsed:a=!1,theme:s="dark",style:l={},collapsible:u=!1,reverseArrow:d=!1,width:f=200,collapsedWidth:m=80,zeroWidthTriggerStyle:p,breakpoint:g,onCollapse:h,onBreakpoint:v}=e,y=cb(e,["prefixCls","className","trigger","children","defaultCollapsed","theme","style","collapsible","reverseArrow","width","collapsedWidth","zeroWidthTriggerStyle","breakpoint","onCollapse","onBreakpoint"]),{siderHook:b}=c.useContext(Yu),[C,w]=c.useState("collapsed"in e?e.collapsed:a),[S,x]=c.useState(!1);c.useEffect(()=>{"collapsed"in e&&w(e.collapsed)},[e.collapsed]);const E=(Y,G)=>{"collapsed"in e||w(Y),h?.(Y,G)},{getPrefixCls:O,direction:I}=c.useContext(vt),R=O("layout-sider",r),[A,M,T]=lb(R),_=c.useRef(null);_.current=Y=>{x(Y.matches),v?.(Y.matches),C!==Y.matches&&E(Y.matches,"responsive")},c.useEffect(()=>{function Y(ue){var Se;return(Se=_.current)===null||Se===void 0?void 0:Se.call(_,ue)}let G;return typeof window?.matchMedia<"u"&&g&&g in Ul&&(G=window.matchMedia(`screen and (max-width: ${Ul[g]})`),e0(G,Y),Y(G)),()=>{t0(G,Y)}},[g]),c.useEffect(()=>{const Y=db("ant-sider-");return b.addSider(Y),()=>b.removeSider(Y)},[]);const N=()=>{E(!C,"clickTrigger")},F=wr(y,["collapsed"]),L=C?m:f,B=ub(L)?`${L}px`:String(L),V=parseFloat(String(m||0))===0?c.createElement("span",{onClick:N,className:Q(`${R}-zero-width-trigger`,`${R}-zero-width-trigger-${d?"right":"left"}`),style:p},o||c.createElement(ib,null)):null,K=I==="rtl"==!d,X={expanded:K?c.createElement(dl,null):c.createElement(zl,null),collapsed:K?c.createElement(zl,null):c.createElement(dl,null)}[C?"collapsed":"expanded"],H=o!==null?V||c.createElement("div",{className:`${R}-trigger`,onClick:N,style:{width:B}},o||X):null,k=Object.assign(Object.assign({},l),{flex:`0 0 ${B}`,maxWidth:B,minWidth:B,width:B}),ae=Q(R,`${R}-${s}`,{[`${R}-collapsed`]:!!C,[`${R}-has-trigger`]:u&&o!==null&&!V,[`${R}-below`]:!!S,[`${R}-zero-width`]:parseFloat(B)===0},n,M,T),se=c.useMemo(()=>({siderCollapsed:C}),[C]);return A(c.createElement(mi.Provider,{value:se},c.createElement("aside",Object.assign({className:ae},F,{style:k,ref:t}),c.createElement("div",{className:`${R}-children`},i),u||S&&V?H:null)))});var fb={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"ellipsis",theme:"outlined"},vb=function(t,r){return c.createElement(At,oe({},t,{ref:r,icon:fb}))},mb=c.forwardRef(vb);const Go=c.createContext({prefixCls:"",firstLevel:!0,inlineCollapsed:!1});var gb=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const nd=e=>{const{prefixCls:t,className:r,dashed:n}=e,o=gb(e,["prefixCls","className","dashed"]),{getPrefixCls:i}=c.useContext(vt),a=i("menu",t),s=Q({[`${a}-item-divider-dashed`]:!!n},r);return c.createElement(vs,Object.assign({className:s},o))},od=e=>{var t;const{className:r,children:n,icon:o,title:i,danger:a,extra:s}=e,{prefixCls:l,firstLevel:u,direction:d,disableMenuItemTitleTooltip:f,inlineCollapsed:m}=c.useContext(Go),p=C=>{const w=n?.[0],S=c.createElement("span",{className:Q(`${l}-title-content`,{[`${l}-title-content-with-extra`]:!!s||s===0})},n);return(!o||c.isValidElement(n)&&n.type==="span")&&n&&C&&u&&typeof w=="string"?c.createElement("div",{className:`${l}-inline-collapsed-noicon`},w.charAt(0)):S},{siderCollapsed:g}=c.useContext(mi);let h=i;typeof i>"u"?h=u?n:"":i===!1&&(h="");const v={title:h};!g&&!m&&(v.title=null,v.open=!1);const y=Wr(n).length;let b=c.createElement(fi,Object.assign({},wr(e,["title","icon","danger"]),{className:Q({[`${l}-item-danger`]:a,[`${l}-item-only-child`]:(o?y+1:y)===1},r),title:typeof i=="string"?i:void 0}),wn(o,{className:Q(c.isValidElement(o)?(t=o.props)===null||t===void 0?void 0:t.className:void 0,`${l}-item-icon`)}),p(m));return f||(b=c.createElement(ku,Object.assign({},v,{placement:d==="rtl"?"left":"right",classNames:{root:`${l}-inline-collapsed-tooltip`}}),b)),b},Kl=c.createContext(null),hb=e=>{const{componentCls:t,motionDurationSlow:r,horizontalLineHeight:n,colorSplit:o,lineWidth:i,lineType:a,itemPaddingInline:s}=e;return{[`${t}-horizontal`]:{lineHeight:n,border:0,borderBottom:`${ie(i)} ${a} ${o}`,boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},[`${t}-item, ${t}-submenu`]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:s},[`> ${t}-item:hover,
        > ${t}-item-active,
        > ${t}-submenu ${t}-submenu-title:hover`]:{backgroundColor:"transparent"},[`${t}-item, ${t}-submenu-title`]:{transition:[`border-color ${r}`,`background ${r}`].join(",")},[`${t}-submenu-arrow`]:{display:"none"}}}},pb=({componentCls:e,menuArrowOffset:t,calc:r})=>({[`${e}-rtl`]:{direction:"rtl"},[`${e}-submenu-rtl`]:{transformOrigin:"100% 0"},[`${e}-rtl${e}-vertical,
    ${e}-submenu-rtl ${e}-vertical`]:{[`${e}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateY(${ie(r(t).mul(-1).equal())})`},"&::after":{transform:`rotate(45deg) translateY(${ie(t)})`}}}}),Gl=e=>Object.assign({},Kc(e)),Xl=(e,t)=>{const{componentCls:r,itemColor:n,itemSelectedColor:o,subMenuItemSelectedColor:i,groupTitleColor:a,itemBg:s,subMenuItemBg:l,itemSelectedBg:u,activeBarHeight:d,activeBarWidth:f,activeBarBorderWidth:m,motionDurationSlow:p,motionEaseInOut:g,motionEaseOut:h,itemPaddingInline:v,motionDurationMid:y,itemHoverColor:b,lineType:C,colorSplit:w,itemDisabledColor:S,dangerItemColor:x,dangerItemHoverColor:E,dangerItemSelectedColor:O,dangerItemActiveBg:I,dangerItemSelectedBg:R,popupBg:A,itemHoverBg:M,itemActiveBg:T,menuSubMenuBg:_,horizontalItemSelectedColor:N,horizontalItemSelectedBg:F,horizontalItemBorderRadius:L,horizontalItemHoverBg:B}=e;return{[`${r}-${t}, ${r}-${t} > ${r}`]:{color:n,background:s,[`&${r}-root:focus-visible`]:Object.assign({},Gl(e)),[`${r}-item`]:{"&-group-title, &-extra":{color:a}},[`${r}-submenu-selected > ${r}-submenu-title`]:{color:i},[`${r}-item, ${r}-submenu-title`]:{color:n,[`&:not(${r}-item-disabled):focus-visible`]:Object.assign({},Gl(e))},[`${r}-item-disabled, ${r}-submenu-disabled`]:{color:`${S} !important`},[`${r}-item:not(${r}-item-selected):not(${r}-submenu-selected)`]:{[`&:hover, > ${r}-submenu-title:hover`]:{color:b}},[`&:not(${r}-horizontal)`]:{[`${r}-item:not(${r}-item-selected)`]:{"&:hover":{backgroundColor:M},"&:active":{backgroundColor:T}},[`${r}-submenu-title`]:{"&:hover":{backgroundColor:M},"&:active":{backgroundColor:T}}},[`${r}-item-danger`]:{color:x,[`&${r}-item:hover`]:{[`&:not(${r}-item-selected):not(${r}-submenu-selected)`]:{color:E}},[`&${r}-item:active`]:{background:I}},[`${r}-item a`]:{"&, &:hover":{color:"inherit"}},[`${r}-item-selected`]:{color:o,[`&${r}-item-danger`]:{color:O},"a, a:hover":{color:"inherit"}},[`& ${r}-item-selected`]:{backgroundColor:u,[`&${r}-item-danger`]:{backgroundColor:R}},[`&${r}-submenu > ${r}`]:{backgroundColor:_},[`&${r}-popup > ${r}`]:{backgroundColor:A},[`&${r}-submenu-popup > ${r}`]:{backgroundColor:A},[`&${r}-horizontal`]:Object.assign(Object.assign({},t==="dark"?{borderBottom:0}:{}),{[`> ${r}-item, > ${r}-submenu`]:{top:m,marginTop:e.calc(m).mul(-1).equal(),marginBottom:0,borderRadius:L,"&::after":{position:"absolute",insetInline:v,bottom:0,borderBottom:`${ie(d)} solid transparent`,transition:`border-color ${p} ${g}`,content:'""'},"&:hover, &-active, &-open":{background:B,"&::after":{borderBottomWidth:d,borderBottomColor:N}},"&-selected":{color:N,backgroundColor:F,"&:hover":{backgroundColor:F},"&::after":{borderBottomWidth:d,borderBottomColor:N}}}}),[`&${r}-root`]:{[`&${r}-inline, &${r}-vertical`]:{borderInlineEnd:`${ie(m)} ${C} ${w}`}},[`&${r}-inline`]:{[`${r}-sub${r}-inline`]:{background:l},[`${r}-item`]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:`${ie(f)} solid ${o}`,transform:"scaleY(0.0001)",opacity:0,transition:[`transform ${y} ${h}`,`opacity ${y} ${h}`].join(","),content:'""'},[`&${r}-item-danger`]:{"&::after":{borderInlineEndColor:O}}},[`${r}-selected, ${r}-item-selected`]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:[`transform ${y} ${g}`,`opacity ${y} ${g}`].join(",")}}}}}},Ql=e=>{const{componentCls:t,itemHeight:r,itemMarginInline:n,padding:o,menuArrowSize:i,marginXS:a,itemMarginBlock:s,itemWidth:l,itemPaddingInline:u}=e,d=e.calc(i).add(o).add(a).equal();return{[`${t}-item`]:{position:"relative",overflow:"hidden"},[`${t}-item, ${t}-submenu-title`]:{height:r,lineHeight:ie(r),paddingInline:u,overflow:"hidden",textOverflow:"ellipsis",marginInline:n,marginBlock:s,width:l},[`> ${t}-item,
            > ${t}-submenu > ${t}-submenu-title`]:{height:r,lineHeight:ie(r)},[`${t}-item-group-list ${t}-submenu-title,
            ${t}-submenu-title`]:{paddingInlineEnd:d}}},bb=e=>{const{componentCls:t,iconCls:r,itemHeight:n,colorTextLightSolid:o,dropdownWidth:i,controlHeightLG:a,motionEaseOut:s,paddingXL:l,itemMarginInline:u,fontSizeLG:d,motionDurationFast:f,motionDurationSlow:m,paddingXS:p,boxShadowSecondary:g,collapsedWidth:h,collapsedIconSize:v}=e,y={height:n,lineHeight:ie(n),listStylePosition:"inside",listStyleType:"disc"};return[{[t]:{"&-inline, &-vertical":Object.assign({[`&${t}-root`]:{boxShadow:"none"}},Ql(e))},[`${t}-submenu-popup`]:{[`${t}-vertical`]:Object.assign(Object.assign({},Ql(e)),{boxShadow:g})}},{[`${t}-submenu-popup ${t}-vertical${t}-sub`]:{minWidth:i,maxHeight:`calc(100vh - ${ie(e.calc(a).mul(2.5).equal())})`,padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{[`${t}-inline`]:{width:"100%",[`&${t}-root`]:{[`${t}-item, ${t}-submenu-title`]:{display:"flex",alignItems:"center",transition:[`border-color ${m}`,`background ${m}`,`padding ${f} ${s}`].join(","),[`> ${t}-title-content`]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},[`${t}-sub${t}-inline`]:{padding:0,border:0,borderRadius:0,boxShadow:"none",[`& > ${t}-submenu > ${t}-submenu-title`]:y,[`& ${t}-item-group-title`]:{paddingInlineStart:l}},[`${t}-item`]:y}},{[`${t}-inline-collapsed`]:{width:h,[`&${t}-root`]:{[`${t}-item, ${t}-submenu ${t}-submenu-title`]:{[`> ${t}-inline-collapsed-noicon`]:{fontSize:d,textAlign:"center"}}},[`> ${t}-item,
          > ${t}-item-group > ${t}-item-group-list > ${t}-item,
          > ${t}-item-group > ${t}-item-group-list > ${t}-submenu > ${t}-submenu-title,
          > ${t}-submenu > ${t}-submenu-title`]:{insetInlineStart:0,paddingInline:`calc(50% - ${ie(e.calc(v).div(2).equal())} - ${ie(u)})`,textOverflow:"clip",[`
            ${t}-submenu-arrow,
            ${t}-submenu-expand-icon
          `]:{opacity:0},[`${t}-item-icon, ${r}`]:{margin:0,fontSize:v,lineHeight:ie(n),"+ span":{display:"inline-block",opacity:0}}},[`${t}-item-icon, ${r}`]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",[`${t}-item-icon, ${r}`]:{display:"none"},"a, a:hover":{color:o}},[`${t}-item-group-title`]:Object.assign(Object.assign({},Uv),{paddingInline:p})}}]},Zl=e=>{const{componentCls:t,motionDurationSlow:r,motionDurationMid:n,motionEaseInOut:o,motionEaseOut:i,iconCls:a,iconSize:s,iconMarginInlineEnd:l}=e;return{[`${t}-item, ${t}-submenu-title`]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:[`border-color ${r}`,`background ${r}`,`padding calc(${r} + 0.1s) ${o}`].join(","),[`${t}-item-icon, ${a}`]:{minWidth:s,fontSize:s,transition:[`font-size ${n} ${i}`,`margin ${r} ${o}`,`color ${r}`].join(","),"+ span":{marginInlineStart:l,opacity:1,transition:[`opacity ${r} ${o}`,`margin ${r}`,`color ${r}`].join(",")}},[`${t}-item-icon`]:Object.assign({},rs()),[`&${t}-item-only-child`]:{[`> ${a}, > ${t}-item-icon`]:{marginInlineEnd:0}}},[`${t}-item-disabled, ${t}-submenu-disabled`]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important",cursor:"not-allowed",pointerEvents:"none"},[`> ${t}-submenu-title`]:{color:"inherit !important",cursor:"not-allowed"}}}},Yl=e=>{const{componentCls:t,motionDurationSlow:r,motionEaseInOut:n,borderRadius:o,menuArrowSize:i,menuArrowOffset:a}=e;return{[`${t}-submenu`]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:i,color:"currentcolor",transform:"translateY(-50%)",transition:`transform ${r} ${n}, opacity ${r}`},"&-arrow":{"&::before, &::after":{position:"absolute",width:e.calc(i).mul(.6).equal(),height:e.calc(i).mul(.15).equal(),backgroundColor:"currentcolor",borderRadius:o,transition:[`background ${r} ${n}`,`transform ${r} ${n}`,`top ${r} ${n}`,`color ${r} ${n}`].join(","),content:'""'},"&::before":{transform:`rotate(45deg) translateY(${ie(e.calc(a).mul(-1).equal())})`},"&::after":{transform:`rotate(-45deg) translateY(${ie(a)})`}}}}},yb=e=>{const{antCls:t,componentCls:r,fontSize:n,motionDurationSlow:o,motionDurationMid:i,motionEaseInOut:a,paddingXS:s,padding:l,colorSplit:u,lineWidth:d,zIndexPopup:f,borderRadiusLG:m,subMenuItemBorderRadius:p,menuArrowSize:g,menuArrowOffset:h,lineType:v,groupTitleLineHeight:y,groupTitleFontSize:b}=e;return[{"":{[r]:Object.assign(Object.assign({},Qs()),{"&-hidden":{display:"none"}})},[`${r}-submenu-hidden`]:{display:"none"}},{[r]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Uc(e)),Qs()),{marginBottom:0,paddingInlineStart:0,fontSize:n,lineHeight:0,listStyle:"none",outline:"none",transition:`width ${o} cubic-bezier(0.2, 0, 0, 1) 0s`,"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",[`${r}-item`]:{flex:"none"}},[`${r}-item, ${r}-submenu, ${r}-submenu-title`]:{borderRadius:e.itemBorderRadius},[`${r}-item-group-title`]:{padding:`${ie(s)} ${ie(l)}`,fontSize:b,lineHeight:y,transition:`all ${o}`},[`&-horizontal ${r}-submenu`]:{transition:[`border-color ${o} ${a}`,`background ${o} ${a}`].join(",")},[`${r}-submenu, ${r}-submenu-inline`]:{transition:[`border-color ${o} ${a}`,`background ${o} ${a}`,`padding ${i} ${a}`].join(",")},[`${r}-submenu ${r}-sub`]:{cursor:"initial",transition:[`background ${o} ${a}`,`padding ${o} ${a}`].join(",")},[`${r}-title-content`]:{transition:`color ${o}`,"&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},[`> ${t}-typography-ellipsis-single-line`]:{display:"inline",verticalAlign:"unset"},[`${r}-item-extra`]:{marginInlineStart:"auto",paddingInlineStart:e.padding}},[`${r}-item a`]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},[`${r}-item-divider`]:{overflow:"hidden",lineHeight:0,borderColor:u,borderStyle:v,borderWidth:0,borderTopWidth:d,marginBlock:d,padding:0,"&-dashed":{borderStyle:"dashed"}}}),Zl(e)),{[`${r}-item-group`]:{[`${r}-item-group-list`]:{margin:0,padding:0,[`${r}-item, ${r}-submenu-title`]:{paddingInline:`${ie(e.calc(n).mul(2).equal())} ${ie(l)}`}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:f,borderRadius:m,boxShadow:"none",transformOrigin:"0 0",[`&${r}-submenu`]:{background:"transparent"},"&::before":{position:"absolute",inset:0,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'},[`> ${r}`]:Object.assign(Object.assign(Object.assign({borderRadius:m},Zl(e)),Yl(e)),{[`${r}-item, ${r}-submenu > ${r}-submenu-title`]:{borderRadius:p},[`${r}-submenu-title::after`]:{transition:`transform ${o} ${a}`}})},"\n          &-placement-leftTop,\n          &-placement-bottomRight,\n          ":{transformOrigin:"100% 0"},"\n          &-placement-leftBottom,\n          &-placement-topRight,\n          ":{transformOrigin:"100% 100%"},"\n          &-placement-rightBottom,\n          &-placement-topLeft,\n          ":{transformOrigin:"0 100%"},"\n          &-placement-bottomLeft,\n          &-placement-rightTop,\n          ":{transformOrigin:"0 0"},"\n          &-placement-leftTop,\n          &-placement-leftBottom\n          ":{paddingInlineEnd:e.paddingXS},"\n          &-placement-rightTop,\n          &-placement-rightBottom\n          ":{paddingInlineStart:e.paddingXS},"\n          &-placement-topRight,\n          &-placement-topLeft\n          ":{paddingBottom:e.paddingXS},"\n          &-placement-bottomRight,\n          &-placement-bottomLeft\n          ":{paddingTop:e.paddingXS}}}),Yl(e)),{[`&-inline-collapsed ${r}-submenu-arrow,
        &-inline ${r}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateX(${ie(h)})`},"&::after":{transform:`rotate(45deg) translateX(${ie(e.calc(h).mul(-1).equal())})`}},[`${r}-submenu-open${r}-submenu-inline > ${r}-submenu-title > ${r}-submenu-arrow`]:{transform:`translateY(${ie(e.calc(g).mul(.2).mul(-1).equal())})`,"&::after":{transform:`rotate(-45deg) translateX(${ie(e.calc(h).mul(-1).equal())})`},"&::before":{transform:`rotate(45deg) translateX(${ie(h)})`}}})},{[`${t}-layout-header`]:{[r]:{lineHeight:"inherit"}}}]},Cb=e=>{var t,r,n;const{colorPrimary:o,colorError:i,colorTextDisabled:a,colorErrorBg:s,colorText:l,colorTextDescription:u,colorBgContainer:d,colorFillAlter:f,colorFillContent:m,lineWidth:p,lineWidthBold:g,controlItemBgActive:h,colorBgTextHover:v,controlHeightLG:y,lineHeight:b,colorBgElevated:C,marginXXS:w,padding:S,fontSize:x,controlHeightSM:E,fontSizeLG:O,colorTextLightSolid:I,colorErrorHover:R}=e,A=(t=e.activeBarWidth)!==null&&t!==void 0?t:0,M=(r=e.activeBarBorderWidth)!==null&&r!==void 0?r:p,T=(n=e.itemMarginInline)!==null&&n!==void 0?n:e.marginXXS,_=new Ve(I).setA(.65).toRgbString();return{dropdownWidth:160,zIndexPopup:e.zIndexPopupBase+50,radiusItem:e.borderRadiusLG,itemBorderRadius:e.borderRadiusLG,radiusSubMenuItem:e.borderRadiusSM,subMenuItemBorderRadius:e.borderRadiusSM,colorItemText:l,itemColor:l,colorItemTextHover:l,itemHoverColor:l,colorItemTextHoverHorizontal:o,horizontalItemHoverColor:o,colorGroupTitle:u,groupTitleColor:u,colorItemTextSelected:o,itemSelectedColor:o,subMenuItemSelectedColor:o,colorItemTextSelectedHorizontal:o,horizontalItemSelectedColor:o,colorItemBg:d,itemBg:d,colorItemBgHover:v,itemHoverBg:v,colorItemBgActive:m,itemActiveBg:h,colorSubItemBg:f,subMenuItemBg:f,colorItemBgSelected:h,itemSelectedBg:h,colorItemBgSelectedHorizontal:"transparent",horizontalItemSelectedBg:"transparent",colorActiveBarWidth:0,activeBarWidth:A,colorActiveBarHeight:g,activeBarHeight:g,colorActiveBarBorderSize:p,activeBarBorderWidth:M,colorItemTextDisabled:a,itemDisabledColor:a,colorDangerItemText:i,dangerItemColor:i,colorDangerItemTextHover:i,dangerItemHoverColor:i,colorDangerItemTextSelected:i,dangerItemSelectedColor:i,colorDangerItemBgActive:s,dangerItemActiveBg:s,colorDangerItemBgSelected:s,dangerItemSelectedBg:s,itemMarginInline:T,horizontalItemBorderRadius:0,horizontalItemHoverBg:"transparent",itemHeight:y,groupTitleLineHeight:b,collapsedWidth:y*2,popupBg:C,itemMarginBlock:w,itemPaddingInline:S,horizontalLineHeight:`${y*1.15}px`,iconSize:x,iconMarginInlineEnd:E-x,collapsedIconSize:O,groupTitleFontSize:x,darkItemDisabledColor:new Ve(I).setA(.25).toRgbString(),darkItemColor:_,darkDangerItemColor:i,darkItemBg:"#001529",darkPopupBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedColor:I,darkItemSelectedBg:o,darkDangerItemSelectedBg:i,darkItemHoverBg:"transparent",darkGroupTitleColor:_,darkItemHoverColor:I,darkDangerItemHoverColor:R,darkDangerItemSelectedColor:I,darkDangerItemActiveBg:i,itemWidth:A?`calc(100% + ${M}px)`:`calc(100% - ${T*2}px)`}},Sb=(e,t=e,r=!0)=>co("Menu",o=>{const{colorBgElevated:i,controlHeightLG:a,fontSize:s,darkItemColor:l,darkDangerItemColor:u,darkItemBg:d,darkSubMenuItemBg:f,darkItemSelectedColor:m,darkItemSelectedBg:p,darkDangerItemSelectedBg:g,darkItemHoverBg:h,darkGroupTitleColor:v,darkItemHoverColor:y,darkItemDisabledColor:b,darkDangerItemHoverColor:C,darkDangerItemSelectedColor:w,darkDangerItemActiveBg:S,popupBg:x,darkPopupBg:E}=o,O=o.calc(s).div(7).mul(5).equal(),I=sr(o,{menuArrowSize:O,menuHorizontalHeight:o.calc(a).mul(1.15).equal(),menuArrowOffset:o.calc(O).mul(.25).equal(),menuSubMenuBg:i,calc:o.calc,popupBg:x}),R=sr(I,{itemColor:l,itemHoverColor:y,groupTitleColor:v,itemSelectedColor:m,subMenuItemSelectedColor:m,itemBg:d,popupBg:E,subMenuItemBg:f,itemActiveBg:"transparent",itemSelectedBg:p,activeBarHeight:0,activeBarBorderWidth:0,itemHoverBg:h,itemDisabledColor:b,dangerItemColor:u,dangerItemHoverColor:C,dangerItemSelectedColor:w,dangerItemActiveBg:S,dangerItemSelectedBg:g,menuSubMenuBg:f,horizontalItemSelectedColor:m,horizontalItemSelectedBg:p});return[yb(I),hb(I),bb(I),Xl(I,"light"),Xl(R,"dark"),pb(I),$g(I),fl(I,"slide-up"),fl(I,"slide-down"),bu(I,"zoom-big")]},Cb,{deprecatedTokens:[["colorGroupTitle","groupTitleColor"],["radiusItem","itemBorderRadius"],["radiusSubMenuItem","subMenuItemBorderRadius"],["colorItemText","itemColor"],["colorItemTextHover","itemHoverColor"],["colorItemTextHoverHorizontal","horizontalItemHoverColor"],["colorItemTextSelected","itemSelectedColor"],["colorItemTextSelectedHorizontal","horizontalItemSelectedColor"],["colorItemTextDisabled","itemDisabledColor"],["colorDangerItemText","dangerItemColor"],["colorDangerItemTextHover","dangerItemHoverColor"],["colorDangerItemTextSelected","dangerItemSelectedColor"],["colorDangerItemBgActive","dangerItemActiveBg"],["colorDangerItemBgSelected","dangerItemSelectedBg"],["colorItemBg","itemBg"],["colorItemBgHover","itemHoverBg"],["colorSubItemBg","subMenuItemBg"],["colorItemBgActive","itemActiveBg"],["colorItemBgSelectedHorizontal","horizontalItemSelectedBg"],["colorActiveBarWidth","activeBarWidth"],["colorActiveBarHeight","activeBarHeight"],["colorActiveBarBorderSize","activeBarBorderWidth"],["colorItemBgSelected","itemSelectedBg"]],injectStyle:r,unitless:{groupTitleLineHeight:!0}})(e,t),id=e=>{var t;const{popupClassName:r,icon:n,title:o,theme:i}=e,a=c.useContext(Go),{prefixCls:s,inlineCollapsed:l,theme:u}=a,d=$n();let f;if(!n)f=l&&!d.length&&o&&typeof o=="string"?c.createElement("div",{className:`${s}-inline-collapsed-noicon`},o.charAt(0)):c.createElement("span",{className:`${s}-title-content`},o);else{const g=c.isValidElement(o)&&o.type==="span";f=c.createElement(c.Fragment,null,wn(n,{className:Q(c.isValidElement(n)?(t=n.props)===null||t===void 0?void 0:t.className:void 0,`${s}-item-icon`)}),g?o:c.createElement("span",{className:`${s}-title-content`},o))}const m=c.useMemo(()=>Object.assign(Object.assign({},a),{firstLevel:!1}),[a]),[p]=vu("Menu");return c.createElement(Go.Provider,{value:m},c.createElement(vi,Object.assign({},wr(e,["icon"]),{title:f,popupClassName:Q(s,r,`${s}-${i||u}`),popupStyle:Object.assign({zIndex:p},e.popupStyle)})))};var xb=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function qi(e){return e===null||e===!1}const wb={item:od,submenu:id,divider:nd},$b=c.forwardRef((e,t)=>{var r;const n=c.useContext(Kl),o=n||{},{getPrefixCls:i,getPopupContainer:a,direction:s,menu:l}=c.useContext(vt),u=i(),{prefixCls:d,className:f,style:m,theme:p="light",expandIcon:g,_internalDisableMenuItemTitleTooltip:h,inlineCollapsed:v,siderCollapsed:y,rootClassName:b,mode:C,selectable:w,onClick:S,overflowedIndicatorPopupClassName:x}=e,E=xb(e,["prefixCls","className","style","theme","expandIcon","_internalDisableMenuItemTitleTooltip","inlineCollapsed","siderCollapsed","rootClassName","mode","selectable","onClick","overflowedIndicatorPopupClassName"]),O=wr(E,["collapsedWidth"]);(r=o.validator)===null||r===void 0||r.call(o,{mode:C});const I=Ct((...z)=>{var X;S?.apply(void 0,z),(X=o.onClick)===null||X===void 0||X.call(o)}),R=o.mode||C,A=w??o.selectable,M=v??y,T={horizontal:{motionName:`${u}-slide-up`},inline:Zm(u),other:{motionName:`${u}-zoom-big`}},_=i("menu",d||o.prefixCls),N=Wm(_),[F,L,B]=Sb(_,N,!n),V=Q(`${_}-${p}`,l?.className,f),K=c.useMemo(()=>{var z,X;if(typeof g=="function"||qi(g))return g||null;if(typeof o.expandIcon=="function"||qi(o.expandIcon))return o.expandIcon||null;if(typeof l?.expandIcon=="function"||qi(l?.expandIcon))return l?.expandIcon||null;const H=(z=g??o?.expandIcon)!==null&&z!==void 0?z:l?.expandIcon;return wn(H,{className:Q(`${_}-submenu-expand-icon`,c.isValidElement(H)?(X=H.props)===null||X===void 0?void 0:X.className:void 0)})},[g,o?.expandIcon,l?.expandIcon,_]),U=c.useMemo(()=>({prefixCls:_,inlineCollapsed:M||!1,direction:s,firstLevel:!0,theme:p,mode:R,disableMenuItemTitleTooltip:h}),[_,M,s,h,p]);return F(c.createElement(Kl.Provider,{value:null},c.createElement(Go.Provider,{value:U},c.createElement(go,Object.assign({getPopupContainer:a,overflowedIndicator:c.createElement(mb,null),overflowedIndicatorPopupClassName:Q(_,`${_}-${p}`,x),mode:R,selectable:A,onClick:I},O,{inlineCollapsed:M,style:Object.assign(Object.assign({},l?.style),m),className:V,prefixCls:_,direction:s,defaultMotions:T,expandIcon:K,ref:t,rootClassName:Q(b,L,o.rootClassName,B,N),_internalComponents:wb})))))}),ho=c.forwardRef((e,t)=>{const r=c.useRef(null),n=c.useContext(mi);return c.useImperativeHandle(t,()=>({menu:r.current,focus:o=>{var i;(i=r.current)===null||i===void 0||i.focus(o)}})),c.createElement($b,Object.assign({ref:r},e,n))});ho.Item=od;ho.SubMenu=id;ho.Divider=nd;ho.ItemGroup=ms;function Eb(e,t,r){return typeof r=="boolean"?r:e.length?!0:Wr(t).some(o=>o.type===rd)}var ad=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function gi({suffixCls:e,tagName:t,displayName:r}){return n=>c.forwardRef((i,a)=>c.createElement(n,Object.assign({ref:a,suffixCls:e,tagName:t},i)))}const gs=c.forwardRef((e,t)=>{const{prefixCls:r,suffixCls:n,className:o,tagName:i}=e,a=ad(e,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:s}=c.useContext(vt),l=s("layout",r),[u,d,f]=td(l),m=n?`${l}-${n}`:l;return u(c.createElement(i,Object.assign({className:Q(r||m,o,d,f),ref:t},a)))}),Ob=c.forwardRef((e,t)=>{const{direction:r}=c.useContext(vt),[n,o]=c.useState([]),{prefixCls:i,className:a,rootClassName:s,children:l,hasSider:u,tagName:d,style:f}=e,m=ad(e,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),p=wr(m,["suffixCls"]),{getPrefixCls:g,className:h,style:v}=es("layout"),y=g("layout",i),b=Eb(n,l,u),[C,w,S]=td(y),x=Q(y,{[`${y}-has-sider`]:b,[`${y}-rtl`]:r==="rtl"},h,a,s,w,S),E=c.useMemo(()=>({siderHook:{addSider:O=>{o(I=>[].concat(q(I),[O]))},removeSider:O=>{o(I=>I.filter(R=>R!==O))}}}),[]);return C(c.createElement(Yu.Provider,{value:E},c.createElement(d,Object.assign({ref:t,className:x,style:Object.assign(Object.assign({},v),f)},p),l)))}),Ib=gi({tagName:"div",displayName:"Layout"})(Ob),Pb=gi({suffixCls:"header",tagName:"header",displayName:"Header"})(gs),Rb=gi({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(gs),Mb=gi({suffixCls:"content",tagName:"main",displayName:"Content"})(gs),Ar=Ib;Ar.Header=Pb;Ar.Footer=Rb;Ar.Content=Mb;Ar.Sider=rd;Ar._InternalSiderContext=mi;var Tb={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 000 13.8z"}}]},name:"menu-fold",theme:"outlined"},_b=function(t,r){return c.createElement(At,oe({},t,{ref:r,icon:Tb}))},Ab=c.forwardRef(_b),Fb={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 000-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0014.4 7z"}}]},name:"menu-unfold",theme:"outlined"},jb=function(t,r){return c.createElement(At,oe({},t,{ref:r,icon:Fb}))},Nb=c.forwardRef(jb),Lb={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 00-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"upload",theme:"outlined"},Bb=function(t,r){return c.createElement(At,oe({},t,{ref:r,icon:Lb}))},zb=c.forwardRef(Bb),kb={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"},Hb=function(t,r){return c.createElement(At,oe({},t,{ref:r,icon:kb}))},Db=c.forwardRef(Hb),Vb={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 302.3L784 376V224c0-35.3-28.7-64-64-64H128c-35.3 0-64 28.7-64 64v576c0 35.3 28.7 64 64 64h592c35.3 0 64-28.7 64-64V648l128 73.7c21.3 12.3 48-3.1 48-27.6V330c0-24.6-26.7-40-48-27.7zM712 792H136V232h576v560zm176-167l-104-59.8V458.9L888 399v226zM208 360h112c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H208c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}}]},name:"video-camera",theme:"outlined"},Wb=function(t,r){return c.createElement(At,oe({},t,{ref:r,icon:Vb}))},qb=c.forwardRef(Wb);const Ub=e=>{const t=e?.algorithm?ko(e.algorithm):Za,r=Object.assign(Object.assign({},pn),e?.token);return yc(r,{override:e?.token},t,ts)};function Kb(e){const{sizeUnit:t,sizeStep:r}=e,n=r-2;return{sizeXXL:t*(n+10),sizeXL:t*(n+6),sizeLG:t*(n+2),sizeMD:t*(n+2),sizeMS:t*(n+1),size:t*n,sizeSM:t*n,sizeXS:t*(n-1),sizeXXS:t*(n-1)}}const Gb=(e,t)=>{const r=t??oi(e),n=r.fontSizeSM,o=r.controlHeight-4;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},r),Kb(t??e)),kc(n)),{controlHeight:o}),zc(Object.assign(Object.assign({},r),{controlHeight:o})))},Ht=(e,t)=>new Ve(e).setA(t).toRgbString(),sn=(e,t)=>new Ve(e).lighten(t).toHexString(),Xb=e=>{const t=qr(e,{theme:"dark"});return{1:t[0],2:t[1],3:t[2],4:t[3],5:t[6],6:t[5],7:t[4],8:t[6],9:t[5],10:t[4]}},Qb=(e,t)=>{const r=e||"#000",n=t||"#fff";return{colorBgBase:r,colorTextBase:n,colorText:Ht(n,.85),colorTextSecondary:Ht(n,.65),colorTextTertiary:Ht(n,.45),colorTextQuaternary:Ht(n,.25),colorFill:Ht(n,.18),colorFillSecondary:Ht(n,.12),colorFillTertiary:Ht(n,.08),colorFillQuaternary:Ht(n,.04),colorBgSolid:Ht(n,.95),colorBgSolidHover:Ht(n,1),colorBgSolidActive:Ht(n,.9),colorBgElevated:sn(r,12),colorBgContainer:sn(r,8),colorBgLayout:sn(r,0),colorBgSpotlight:sn(r,26),colorBgBlur:Ht(n,.04),colorBorder:sn(r,26),colorBorderSecondary:sn(r,19)}},Zb=(e,t)=>{const r=Object.keys(Qa).map(i=>{const a=qr(e[i],{theme:"dark"});return Array.from({length:10},()=>1).reduce((s,l,u)=>(s[`${i}-${u+1}`]=a[u],s[`${i}${u+1}`]=a[u],s),{})}).reduce((i,a)=>(i=Object.assign(Object.assign({},i),a),i),{}),n=t??oi(e),o=Bc(e,{generateColorPalettes:Xb,generateNeutralColorPalettes:Qb});return Object.assign(Object.assign(Object.assign(Object.assign({},n),r),o),{colorPrimaryBg:o.colorPrimaryBorder,colorPrimaryBgHover:o.colorPrimaryBorderHover})};function Yb(){const[e,t,r]=Sr();return{theme:e,token:t,hashId:r}}const Jb={defaultSeed:Yn.token,useToken:Yb,defaultAlgorithm:oi,darkAlgorithm:Zb,compactAlgorithm:Gb,getDesignToken:Ub,defaultConfig:Yn,_internalContext:Ya},{Header:ey,Sider:ty,Content:ry}=Ar,ny=()=>{const[e,t]=c.useState(!1),{token:{colorBgContainer:r,borderRadiusLG:n}}=Jb.useToken();return It.jsxs(Ar,{children:[It.jsxs(ty,{trigger:null,collapsible:!0,collapsed:e,children:[It.jsx("div",{className:"demo-logo-vertical"}),It.jsx(ho,{theme:"dark",mode:"inline",defaultSelectedKeys:["1"],items:[{key:"1",icon:It.jsx(Db,{}),label:"nav 1"},{key:"2",icon:It.jsx(qb,{}),label:"nav 2"},{key:"3",icon:It.jsx(zb,{}),label:"nav 3"}]})]}),It.jsxs(Ar,{children:[It.jsx(ey,{style:{padding:0,background:r},children:It.jsx(as,{type:"text",icon:e?It.jsx(Nb,{}):It.jsx(Ab,{}),onClick:()=>t(!e),style:{fontSize:"16px",width:64,height:64}})}),It.jsx(ry,{style:{margin:"24px 16px",padding:24,minHeight:280,background:r,borderRadius:n},children:"Content"})]})]})},ay=Object.freeze(Object.defineProperty({__proto__:null,default:ny},Symbol.toStringTag,{value:"Module"}));export{ay as _};
