import{G as i,o as e}from"./index-hfMliPo3.js";import{S as h,a as x,b as l,c as p,d as j,e as f,f as u}from"./sheet-CJ0KXymD.js";import{I as n}from"./icon-DQsjpPVI.js";import{B as c}from"./button-BDk51iWJ.js";import{S as g}from"./scroll-area-DwuRsZTj.js";import{B as b}from"./badge-Bnp0TbTO.js";import{a as S}from"./index-BcAf74l_.js";import{a as v,S as o}from"./use-system-DVY9KgBi.js";import{L as N}from"./label-DPhRWIda.js";import{S as w}from"./switch-Cf-lMhzr.js";const m=()=>{const t=i(s=>s.themeConfig.themeMode),r=i(s=>s.setThemeMode),a=i(s=>s.themeConfig.availableThemeMode);return{themeMode:t,setThemeMode:r,availableThemeMode:a}};function y(){const{setThemeMode:t,availableThemeMode:r}=m();return e.jsx("div",{className:"flex flex-row flex-wrap gap-4 p-2 justify-between",children:Object.entries(r).map(([a,s])=>e.jsx(T,{themeMode:a,setThemeMode:t,themeColor:s},a))})}function T({themeMode:t,themeColor:r,setThemeMode:a}){const{themeMode:s}=m(),d=s===t;return e.jsx(e.Fragment,{children:e.jsx(b,{variant:"secondary",className:"group cursor-pointer w-23 h-23 transition",onClick:()=>a(t),children:e.jsx("div",{className:S("w-7 h-7 transition-all duration-100 rounded-full group-hover:w-18 group-hover:h-18",d?"w-18 h-18":"w-7 h-7"),style:{backgroundColor:r}})})})}const M={opacity:1};function C(){const{showHeaderTab:t,setShowHeaderTab:r}=v();return e.jsxs(h,{modal:!0,children:[e.jsx(x,{asChild:!0,children:e.jsx(c,{variant:"ghost",className:"cursor-pointer",children:e.jsx(n,{icon:"line-md:cog-filled-loop",size:25,className:"bg-background"})})}),e.jsxs(l,{hidden:!0,children:[e.jsx(p,{}),e.jsx(j,{})]}),e.jsxs(f,{style:M,className:"max-w-xs",children:[e.jsx(l,{}),e.jsx(g,{children:e.jsxs("div",{className:"p-5",children:[e.jsxs("div",{className:"flex items-center gap-2 my-1",children:[e.jsx(o,{className:"flex-1"}),e.jsx("span",{className:"text-md font-bold text-primary whitespace-nowrap",children:"主题"}),e.jsx(o,{className:"flex-1"})]}),e.jsx("div",{className:"mb-5",children:e.jsx(y,{})}),e.jsxs("div",{className:"flex items-center gap-2 my-1",children:[e.jsx(o,{className:"flex-1"}),e.jsx("span",{className:"text-md font-bold text-primary whitespace-nowrap",children:"界面设置"}),e.jsx(o,{className:"flex-1"})]}),e.jsx("div",{children:e.jsxs("div",{className:"flex justify-between p-3",children:[e.jsx(N,{className:"font-bold text-md",children:"头部导航栏Tab"}),e.jsx(w,{className:"cursor-pointer",checked:t,onCheckedChange:r})]})})]})}),e.jsx(u,{children:e.jsx(c,{variant:"outline",className:"w-full border-dashed text-text-primary hover:border-primary hover:text-primary",children:e.jsx("div",{className:"flex items-center justify-center",children:e.jsx(n,{icon:"local:ic-settings-exit-fullscreen",size:25,color:"#f55188"})})})})]})]})}const D=Object.freeze(Object.defineProperty({__proto__:null,SystemSetting:C},Symbol.toStringTag,{value:"Module"}));export{C as S,D as _};
