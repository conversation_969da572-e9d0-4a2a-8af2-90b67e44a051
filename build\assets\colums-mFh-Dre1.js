import{o as e}from"./index-hfMliPo3.js";import{B as r}from"./button-BDk51iWJ.js";import{C as i}from"./checkbox-C3f9JzdG.js";import{D as t,a as n,b as o,c,d as m}from"./dropdown-menu-DENvmJeK.js";import{E as u}from"./ellipsis-BKx9zESB.js";import{M as x}from"./move-down-gLXNML-u.js";import{c as p}from"./index-CzvqTnBS.js";const s=p();function j({handleOpenEditDialog:d,handleDeleteUser:h}){return[s.display({id:"select",header:({table:l})=>e.jsx(i,{checked:l.getIsAllPageRowsSelected()?!0:l.getIsSomePageRowsSelected()?"indeterminate":!1,onCheckedChange:a=>l.toggleAllPageRowsSelected(!!a),"aria-label":"Select all"}),cell:({row:l})=>e.jsx(i,{checked:l.getIsSelected(),onCheckedChange:a=>l.toggleSelected(!!a),"aria-label":"Select row"}),enableSorting:!1,enableHiding:!1}),s.accessor("id",{header:"ID"}),s.accessor("name",{header:"姓名",cell:l=>l.getValue()}),s.accessor("email",{header:"邮箱",cell:l=>l.getValue()}),s.accessor("avatar",{header:"头像",cell:l=>e.jsx("img",{src:l.getValue(),alt:"avatar",className:"w-8 h-8 rounded-full"})}),s.display({id:"actions",header:({table:l})=>e.jsx("div",{className:" flex",children:e.jsxs(t,{children:[e.jsx(n,{asChild:!0,children:e.jsxs(r,{variant:"ghost",className:"ml-auto",children:["属性筛选",e.jsx(x,{})]})}),e.jsx(o,{align:"end",children:l.getAllColumns().filter(a=>a.getCanHide()).map(a=>e.jsx(m,{className:"capitalize",checked:a.getIsVisible(),onCheckedChange:g=>a.toggleVisibility(!!g),children:a.id},a.id))})]})}),cell:({row:l})=>e.jsx("div",{className:" flex justify-end",children:e.jsxs(t,{children:[e.jsx(n,{asChild:!0,children:e.jsx(r,{variant:"ghost",className:"h-8 p-0",children:e.jsx(u,{className:"h-4 w-4"})})}),e.jsxs(o,{align:"end",children:[e.jsx(c,{onClick:()=>d(l.original),children:"编辑"}),e.jsx(c,{className:"text-red-600",onClick:()=>h(l.original.id),children:"删除"})]})]})})})]}const v=Object.freeze(Object.defineProperty({__proto__:null,default:j},Symbol.toStringTag,{value:"Module"}));export{j as C,v as _};
