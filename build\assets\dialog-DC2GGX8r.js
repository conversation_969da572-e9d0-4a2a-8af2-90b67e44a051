import{o as t}from"./index-hfMliPo3.js";import{R as i,C as r,b as s,a as d,D as c,P as g,O as u}from"./index-CfO0Bd0J.js";import{c as o}from"./index-BcAf74l_.js";import{X as f}from"./x-DAWoEBhS.js";function v({...a}){return t.jsx(i,{"data-slot":"dialog",...a})}function m({...a}){return t.jsx(g,{"data-slot":"dialog-portal",...a})}function h({...a}){return t.jsx(s,{"data-slot":"dialog-close",...a})}function x({className:a,...e}){return t.jsx(u,{"data-slot":"dialog-overlay",className:o("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...e})}function y({className:a,children:e,showCloseButton:n=!0,...l}){return t.jsxs(m,{"data-slot":"dialog-portal",children:[t.jsx(x,{}),t.jsxs(r,{"data-slot":"dialog-content",className:o("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...l,children:[e,n&&t.jsxs(s,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[t.jsx(f,{}),t.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function N({className:a,...e}){return t.jsx("div",{"data-slot":"dialog-header",className:o("flex flex-col gap-2 text-center sm:text-left",a),...e})}function z({className:a,...e}){return t.jsx("div",{"data-slot":"dialog-footer",className:o("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...e})}function C({className:a,...e}){return t.jsx(d,{"data-slot":"dialog-title",className:o("text-lg leading-none font-semibold",a),...e})}function w({className:a,...e}){return t.jsx(c,{"data-slot":"dialog-description",className:o("text-muted-foreground text-sm",a),...e})}export{v as D,y as a,N as b,C as c,w as d,z as e,h as f};
